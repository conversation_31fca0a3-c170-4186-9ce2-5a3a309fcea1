declare global {
  var abc: string;

  /**
   * from base.js
   * default settings are hardcoded
   * user settings are loaded from localStorage: localStorage.getItem("df_html5")
   */
  interface UserSettings {
    general: {
      playSound: boolean;
      volume: number;
      displayAvatars: boolean;
      statusPercents: boolean;
      simpleMenus: boolean;
    };
    forum: {
      displayAvatars: boolean;
    };
    gamblingden: {
      instant: boolean;
    };
    inventory: {
      sortstyle: number;
      sortbyscrap: boolean;
    };
    hidden: {
      hidearmour: boolean;
    };
  }
  var defUserSettings: UserSettings;
  var userSettings: UserSettings;

  // checks if a setting is true in userSettings. boolCat is first index into userSettings, boolName is the second index
  function checkLSBool(
    boolCat: keyof UserSettings,
    boolName: keyof UserSettings[keyof UserSettings]
  ): boolean;

  /**
   * from base.js
   * div element displaying prompt messages (e.g. loading, are your sure, etc)
   *
   * html structure:
   *    #prompt -> #gamecontent === df_prompt
   */
  var df_prompt: DivElement | false;

  // displays a prompt with text "Loading..." or customMessage
  function promptLoading(customMessage?: string): void;

  // Hides and resets the global df_prompt element, clearing its contents and removing highlight classes
  function promptEnd(): void;

  /**
   * from base.js
   * shows a prompt with text "Connection Error"
   * then reloads window after a short delay
   */
  function webCallError(): void;
}
