var userVars;
var pageLock = true;

var nf;
if (typeof Intl !== "undefined") {
  nf = new Intl.NumberFormat("en-US");
} else {
  nf = {};
  nf.format = function (numberToFormat) {
    return (numberToFormat + "").replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,");
  };
}
var df_prompt = false;

function checkLSBool(boolCat, boolName) {
  if (
    typeof userSettings !== "undefined" &&
    typeof userSettings[boolCat] !== "undefined" &&
    typeof userSettings[boolCat][boolName] !== "undefined"
  ) {
    return userSettings[boolCat][boolName];
  } else {
    if (typeof userSettings[boolCat] === "undefined") {
      userSettings[boolCat] = {};
    }
    userSettings[boolCat][boolName] = defUserSettings[boolCat][boolName];
    localStorage.setItem("df_html5", JSON.stringify(userSettings));
    return userSettings[boolCat][boolName];
  }
}

function checkPropertyValid(propCat, propName) {
  var uSet = [false, false];
  if (typeof userSettings !== "undefined") {
    if (typeof userSettings[propCat] !== "undefined") {
      if (typeof userSettings[propCat][propName] !== "undefined") {
        return true;
      }
      uSet[1] = true;
    }
    uSet[0] = true;
  }
  if (typeof defUserSettings !== "undefined") {
    if (typeof defUserSettings[propCat] !== "undefined") {
      if (typeof defUserSettings[propCat][propName] !== "undefined") {
        if (uSet[1]) {
          userSettings[propCat][propName] = defUserSettings[propCat][propName];
          localStorage.setItem("df_html5", JSON.stringify(userSettings));
          return true;
        } else {
          if (uSet[0]) {
            userSettings[propCat] = defUserSettings[propCat];
            localStorage.setItem("df_html5", JSON.stringify(userSettings));
            return true;
          } else {
            userSettings = defUserSettings;
            localStorage.setItem("df_html5", JSON.stringify(userSettings));
            return true;
          }
        }
      }
    }
  } else {
    location.reload(true);
  }
  return false;
}

let intsToParse = [
  "df_cash",
  "df_credits",
  "DFSTATS_df_cash",
  "DFSTATS_df_credits",
  "DFSTATS_df_survival",
];
function flshToArr(flashStr, padding = "", callback = false) {
  var output = {};
  var flashBlock = flashStr.split("&");
  for (var i in flashBlock) {
    var splt = flashBlock[i].explode("=", 2);
    if (typeof splt[1] !== "undefined") {
      if (intsToParse.indexOf(splt[0]) !== -1) {
        splt[1] = parseInt(splt[1]);
      }
      output[padding + splt[0]] = splt[1];
    }
  }
  if (callback !== false) {
    callback(output);
  } else {
    return output;
  }
}

function updateIntoArr(flshArr, baseArr) {
  for (var i in flshArr) {
    baseArr[i] = flshArr[i];
  }
}

function setUserVars(flshArr) {
  userVars = flshArr;
}

function objectJoin(obj) {
  var output = "",
    p;
  for (p in obj) {
    if (output !== "") {
      output += "&";
    }
    output += p + "=" + obj[p];
  }
  return output;
}

const webCall = function (call, params, callback, hashed) {
  if (typeof hashed === "undefined") {
    hashed = false;
  }

  if (typeof callback === "undefined") {
    callback = false;
  }

  var actualCall = call;
  if (window.location.pathname.indexOf("/DF3D/") >= 0) {
    actualCall = "../" + actualCall;
  }

  if (!df_prompt && document.getElementById("gamecontent")) {
    df_prompt = document.getElementById("gamecontent");
  }

  params = objectJoin(params);

  let xhr = new XMLHttpRequest();
  xhr.addEventListener("load", function () {
    checkValidPacket(callback, call, xhr.responseText, xhr.status, xhr);
  });
  xhr.addEventListener("error", webCallError);
  xhr.open("POST", actualCall + ".php");
  xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
  if (hashed) {
    let datahash = hash(params);
    xhr.send("hash=" + datahash + "&" + params);
  } else {
    xhr.send(params);
  }
};

function findTagExist(itemData, tag) {
  if (itemData.indexOf("_" + tag) !== -1) {
    return true;
  } else {
    return false;
  }
}

function findTagValue(itemData, tag) {
  var start = itemData.indexOf("_" + tag);
  if (start !== -1) {
    var end = itemData.indexOf("_", start + 1);
    if (end !== -1) {
      return itemData.substr(
        start + tag.length + 1,
        end - start - tag.length - 1
      );
    } else {
      return itemData.substr(start + tag.length + 1);
    }
  } else {
    return "";
  }
}

function timedReload(timeToReload = 2500) {
  setTimeout(() => {
    window.location.reload();
  }, timeToReload);
}
function timedRedirect(locationToGo, timeToRedirect = 2500) {
  if (locationToGo.indexOf("/") !== 0) {
    return false;
  }
  setTimeout(() => {
    window.location = locationToGo;
  }, timeToRedirect);
}

/**
 *
 * @param {Function} callback
 * @param {String} call
 * @param {String} data
 * @param {Number} status
 * @param {XMLHttpRequest} xhr
 */
function checkValidPacket(callback, call, data, status, xhr) {
  var exceptionUrls = ["modify_values", "shop", "surgeon"];
  if (
    !exceptionUrls.includes(call) &&
    typeof xhr.responseURL !== "undefined" &&
    xhr.responseURL.indexOf(call + ".php") === -1
  ) {
    webCallError();
  } else if (call === "inventory_new" && data.trim().length === 0) {
    webCallError();
  } else {
    if (data.indexOf("status=") === 0) {
      let errorData = flshToArr(data);
      switch (errorData["status"]) {
        case "generic":
          promptLoading("Unknown error... Reloading.");
          timedReload();
          break;
        case "no_clan":
          promptLoading("No clan found... Redirecting.");
          timedRedirect("/onlinezombiemmo/index.php");
          break;
        case "no_permission":
          promptLoading("You don't have permission to do that... Reloading.");
          timedReload();
          break;
        case "invalid_storage_slot":
          promptLoading("Storage slot is inaccessible... Reloading.");
          timedReload();
          break;
        case "no_transfer":
          promptLoading("Item is non-transferable... Reloading.");
          timedReload();
          break;
        case "low_cash":
          promptLoading("Not enough funds... Reloading.");
          timedReload();
          break;
        case "value_mismatch":
          promptLoading("User value is incorrect... Reloading.");
          timedReload();
          break;
        case "missing_value":
          promptLoading("Variable not found... Reloading.");
          timedReload();
          break;
        case "pointless":
          promptLoading("Pointless action... Reloading.");
          timedReload();
          break;
        case "no_results":
          promptLoading("Zero results... Try again.");
          setTimeout(function () {
            if (callback) {
              callback(data, status, xhr);
              checkForTime = flshToArr(data);
              if (checkForTime["HR_start_time"]) {
                console.log(checkForTime["HR_start_time"]);
              }
              if (checkForTime["HR_end_time"]) {
                console.log(checkForTime["HR_end_time"]);
              }
              if (checkForTime["HR_time"]) {
                console.log(checkForTime["HR_time"]);
              }
            }
          }, 750);
          break;
        case "no_deny":
          promptLoading("You cannot deny a trade from Help Bot... Reloading.");
          timedReload();
          break;
        default: // fallback
          if (callback) {
            callback(data, status, xhr);
            checkForTime = flshToArr(data);
            if (checkForTime["HR_start_time"]) {
              console.log(checkForTime["HR_start_time"]);
            }
            if (checkForTime["HR_end_time"]) {
              console.log(checkForTime["HR_end_time"]);
            }
            if (checkForTime["HR_time"]) {
              console.log(checkForTime["HR_time"]);
            }
          }
          break;
      }
    } else if (callback) {
      callback(data, status, xhr);
      checkForTime = flshToArr(data);
      if (checkForTime["HR_start_time"]) {
        console.log(checkForTime["HR_start_time"]);
      }
      if (checkForTime["HR_end_time"]) {
        console.log(checkForTime["HR_end_time"]);
      }
      if (checkForTime["HR_time"]) {
        console.log(checkForTime["HR_time"]);
      }
    }
  }
}

var noColor = false;
webCall("hotrods/hotrods_avatars/getColorList", "", function (colorData) {
  noColor = colorData.split("&");
  noColor.push("bald");
});

function playSound(sound) {
  if (
    checkLSBool("general", "playSound") &&
    checkPropertyValid("general", "volume") &&
    hrV !== 0
  ) {
    if (Audio !== undefined) {
      var audioPath = "hotrods/hotrods_v" + hrV + "/HTML5/sounds/" + sound;
      if (window.location.pathname.indexOf("/DF3D/") >= 0) {
        audioPath = "../" + audioPath;
      }
      var audSrc = new Audio();
      if (audSrc.canPlayType("audio/mp3") !== "") {
        audioPath += ".mp3";
      } else if (audSrc.canPlayType("audio/ogg") !== "") {
        audioPath += ".ogg";
      } else {
        console.log("Audio is not playable.");
        return;
      }
      audSrc.volume = parseInt(userSettings["general"]["volume"]) / 100;
      audSrc.src = audioPath;
      audSrc.play();
    }
  }
}

function checkIfLevelUp(
  neededExp,
  currentExp,
  freePoints,
  curLevel,
  password,
  userid,
  sc,
  templateid
) {
  neededExp = parseInt(neededExp);
  currentExp = parseInt(currentExp);
  freePoints = parseInt(freePoints);
  curLevel = parseInt(curLevel);
  if (currentExp >= neededExp && freePoints === 0 && curLevel < 415) {
    var dataArr = {};
    var rank = "Exterminator";
    if (curLevel + 1 < 10) {
      rank = "Survivor";
    } else if (curLevel + 1 < 20) {
      rank = "Militia";
    } else if (curLevel + 1 < 30) {
      rank = "Mercenary";
    } else if (curLevel + 1 < 40) {
      rank = "Hired Gun";
    } else if (curLevel + 1 < 50) {
      rank = "Bounty Hunter";
    }

    dataArr["default_options[df_exp]"] = currentExp - neededExp;
    dataArr["default_options[df_freepoints]"] = 1;
    dataArr["default_options[df_level]"] = curLevel + 1;
    dataArr["default_options[df_rank]"] = rank;
    dataArr["password"] = password;
    dataArr["userID"] = userid;
    dataArr["sc"] = sc;
    dataArr["templateID"] = templateid;
    dataArr["redirect_url"] = "index.php?page=14";
    webCall("modify_values", dataArr, function () {
      window.location.href = "index.php?page=14";
    });
  }
}

function baseGameUpdates(
  curHP,
  dead,
  outpost,
  serverTime,
  hungerTime,
  lastSpawn,
  password,
  userID,
  sc,
  templateID,
  tts
) {
  var dataArr = {};
  dataArr["password"] = password;
  dataArr["userID"] = userID;
  dataArr["sc"] = sc;
  dataArr["templateID"] = templateID;
  if (parseInt(curHP) === 0 && parseInt(dead) === 0) {
    window.location.href = "index.php";
  }
  if (parseInt(outpost) === 2 && parseInt(curHP) > 0 && parseInt(dead) === 0) {
    window.location.href = "index.php?page=21";
  }
  if (parseInt(serverTime) > parseInt(hungerTime) + 3600) {
    webCall("hunger", dataArr);
  }
  if (parseInt(serverTime) > parseInt(lastSpawn) + (72000 - 72000 * tts)) {
    webCall("itemspawn", dataArr);
  }
}

function renderAvatar(elem, flashStr) {
  var sTags = document.getElementsByTagName("script");
  var mom = sTags[sTags.length - 1].parentNode;
  var renderData = flshToArr(flashStr);
  var c = document.createElement("canvas");
  c.width = "160";
  c.height = "350";
  var ctx = c.getContext("2d");
  var images = [];
  for (let renKey in renderData) {
    if (renderData[renKey].length > 0) {
      images.push(renderData[renKey]);
    }
  }
  var i = 0;
  var compileImage = function () {
    if (i < images.length) {
      var img = new Image();
      img.onload = function () {
        ctx.drawImage(img, 0, 0, 160, 350);
        i++;
        compileImage();
      };
      img.onerror = function () {
        i++;
        compileImage();
      };
      images[i] = images[i].replace(/(_nt)(?=$|_)/, "");
      img.src = images[i];
    }
  };
  compileImage();
  mom.appendChild(c);
  elem.removeChild(elem.firstElementChild);
}

function pickItemImageSubStr(itemData) {
  var itemDataHolder = itemData.split("_");
  var outputStr = itemDataHolder[0];
  for (var i = 0; i < itemDataHolder.length; i++) {
    if (itemDataHolder[i].indexOf("colour") === 0) {
      outputStr += "_" + itemDataHolder[i];
    }
  }
  return outputStr;
}

function updateAllFieldsBase() {
  if (
    window.location.pathname.indexOf("/DF3D/") < 0 &&
    window.location.search.indexOf("page=31") < 0
  ) {
    var weapons = {};
    weapons[0] = userVars["DFSTATS_df_weapon1type"].trim();
    weapons[1] = userVars["DFSTATS_df_weapon2type"].trim();
    weapons[2] = userVars["DFSTATS_df_weapon3type"].trim();

    var sidebarHolder = document.getElementById("sidebar");
    for (var i in weapons) {
      if (weapons[i].length > 0) {
        var weapRealNum = parseInt(i) + 1;
        sidebarHolder.querySelectorAll("div.weapon")[i].innerHTML =
          "<img src='https://files.deadfrontier.com/deadfrontier/inventoryimages/large/" +
          pickItemImageSubStr(weapons[i]) +
          ".png' />";
        if (
          userVars["DFSTATS_df_weapon" + weapRealNum + "ammo"] &&
          userVars["DFSTATS_df_weapon" + weapRealNum + "ammo"] !== ""
        ) {
          var wepAmmo = nf.format(
            userVars[
              "DFSTATS_df_" +
                userVars["DFSTATS_df_weapon" + weapRealNum + "ammo"]
            ]
          );
          sidebarHolder.querySelectorAll("div.weapon")[i].innerHTML +=
            "<div>" + wepAmmo + " rounds</div>";
        }
      } else {
        sidebarHolder.querySelectorAll("div.weapon")[i].innerHTML = "";
      }
    }

    var armour = userVars["DFSTATS_df_armourtype"].split("_");

    if (armour[0] !== "") {
      var armourHealth = "Normal";
      var armourHealthColor = "12FF00";
      var armourhp =
        parseInt(userVars["DFSTATS_df_armourhp"]) /
        parseInt(userVars["DFSTATS_df_armourhpmax"]);
      if (armourhp <= 0) {
        armourHealth = "Broken";
        armourHealthColor = "D20303";
      } else if (armourhp < 0.4) {
        armourHealth = "Damaged";
        armourHealthColor = "FF4800";
      } else if (armourhp < 0.75) {
        armourHealth = "Scratched";
        armourHealthColor = "FFCC00";
      }
      armourHealth +=
        "<br />" +
        userVars["DFSTATS_df_armourhp"] +
        " / " +
        userVars["DFSTATS_df_armourhpmax"];
      if (checkLSBool("general", "statusPercents")) {
        armourHealth += "<br />(" + Math.round(armourhp * 100) + "%)";
      }

      var armorData = sidebarHolder.querySelector("#sidebarArmour");
      armorData.querySelector("img").src =
        "https://files.deadfrontier.com/deadfrontier/inventoryimages/large/" +
        armour[0] +
        ".png";
      armorData.querySelector("div").innerHTML = armourHealth;
      armorData.querySelector("div").style.color = "#" + armourHealthColor;
    } else {
      var armorData = sidebarHolder.querySelector("#sidebarArmour");
      armorData.querySelector("img").src = "";
      armorData.querySelector("div").innerHTML = "";
    }

    var health = "Healthy";
    var healthColor = "12FF00";
    var hp = userVars["DFSTATS_df_hpcurrent"] / userVars["DFSTATS_df_hpmax"];
    if (hp <= 0) {
      health = "DEAD";
      healthColor = "D20303";
    } else if (hp < 0.25) {
      health = "Critical";
      healthColor = "D20303";
    } else if (hp < 0.5) {
      health = "Serious";
      healthColor = "FF4800";
    } else if (hp < 0.75) {
      health = "Injured";
      healthColor = "FFCC00";
    }
    health +=
      "<br />" +
      userVars["DFSTATS_df_hpcurrent"] +
      " / " +
      userVars["DFSTATS_df_hpmax"];
    if (checkLSBool("general", "statusPercents")) {
      health += "<br />(" + Math.round(hp * 100) + "%)";
    }
    for (let playerHealth of document.getElementsByClassName("playerHealth")) {
      playerHealth.innerHTML = health;
      playerHealth.style.color = "#" + healthColor;
    }
    health = "Nourished";
    healthColor = "12FF00";
    hp = userVars["DFSTATS_df_hungerhp"];
    if (hp <= 0) {
      health = "Dying";
      healthColor = "D20303";
    } else if (
      hp <
      Math.floor(
        (50 + Math.floor(userVars["DFSTATS_df_survival"] * 1.5)) * 0.25
      )
    ) {
      health = "Starving";
      healthColor = "D20303";
    } else if (
      hp <
      Math.floor((50 + Math.floor(userVars["DFSTATS_df_survival"] * 1.5)) * 0.5)
    ) {
      health = "Hungry";
      healthColor = "FF4800";
    } else if (
      hp <
      Math.floor(
        (50 + Math.floor(userVars["DFSTATS_df_survival"] * 1.5)) * 0.75
      )
    ) {
      health = "Fine";
      healthColor = "FFCC00";
    }
    health +=
      "<br />" +
      userVars["DFSTATS_df_hungerhp"] +
      " / " +
      (50 + Math.floor(userVars["DFSTATS_df_survival"] * 1.5));
    if (checkLSBool("general", "statusPercents")) {
      health +=
        "<br />(" +
        Math.round(
          (hp /
            Math.floor(
              50 + Math.floor(userVars["DFSTATS_df_survival"] * 1.5)
            )) *
            100
        ) +
        "%)";
    }
    for (let playerNourishment of document.getElementsByClassName(
      "playerNourishment"
    )) {
      playerNourishment.innerHTML = health;
      playerNourishment.style.color = "#" + healthColor;
    }

    var boostOutput = "";
    var expBoost = 0;
    if (
      parseInt(userVars["DFSTATS_df_boostexpuntil"]) >=
      parseInt(userVars["DFSTATS_df_servertime"]) + **********
    ) {
      expBoost += 50;
    }
    if (
      parseInt(userVars["DFSTATS_df_boostexpuntil_ex"]) >=
      parseInt(userVars["DFSTATS_df_servertime"]) + **********
    ) {
      expBoost += 50;
    }
    if (expBoost > 0) {
      boostOutput += "+" + expBoost + "% Exp Boost";
    }
    var damageBoost = 0;
    if (
      parseInt(userVars["DFSTATS_df_boostdamageuntil"]) >=
      parseInt(userVars["DFSTATS_df_servertime"]) + **********
    ) {
      damageBoost += 35;
    }
    if (
      parseInt(userVars["DFSTATS_df_boostdamageuntil_ex"]) >=
      parseInt(userVars["DFSTATS_df_servertime"]) + **********
    ) {
      damageBoost += 35;
    }
    if (damageBoost > 0) {
      if (boostOutput !== "") {
        boostOutput += "<br />";
      }
      boostOutput += "+" + damageBoost + "% Damage Boost";
    }
    var speedBoost = 0;
    if (
      parseInt(userVars["DFSTATS_df_boostspeeduntil"]) >=
      parseInt(userVars["DFSTATS_df_servertime"]) + **********
    ) {
      speedBoost += 35;
    }
    if (
      parseInt(userVars["DFSTATS_df_boostspeeduntil_ex"]) >=
      parseInt(userVars["DFSTATS_df_servertime"]) + **********
    ) {
      speedBoost += 35;
    }
    if (speedBoost > 0) {
      if (boostOutput !== "") {
        boostOutput += "<br />";
      }
      boostOutput += "+" + speedBoost + "% Speed Boost";
    }
    for (let boostTimes of document.querySelectorAll(".boostTimes")) {
      boostTimes.innerHTML = boostOutput;
    }
    var credits = "Credits: " + nf.format(userVars["DFSTATS_df_credits"]);
    for (let heldCredits of document.querySelectorAll(".heldCredits")) {
      heldCredits.textContent = credits;
      heldCredits.dataset.cash = credits;
    }
    var cash = "Cash: $" + nf.format(userVars["DFSTATS_df_cash"]);
    for (let heldCash of document.querySelectorAll(".heldCash")) {
      heldCash.textContent = cash;
      heldCash.dataset.cash = cash;
    }
  } else {
    sidebarHolder = document.getElementById("statusBox");
    armour = userVars["DFSTATS_df_armourtype"].split("_");

    if (armour[0] !== "") {
      var armourHealth = "Normal";
      var armourHealthColor = "12FF00";
      var armourhp =
        parseInt(userVars["DFSTATS_df_armourhp"]) /
        parseInt(userVars["DFSTATS_df_armourhpmax"]);
      if (armourhp <= 0) {
        armourHealth = "Broken";
        armourHealthColor = "D20303";
      } else if (armourhp < 0.4) {
        armourHealth = "Damaged";
        armourHealthColor = "FF4800";
      } else if (armourhp < 0.75) {
        armourHealth = "Scratched";
        armourHealthColor = "FFCC00";
      }
      armourHealth +=
        "<br />" +
        userVars["DFSTATS_df_armourhp"] +
        " / " +
        userVars["DFSTATS_df_armourhpmax"];
      if (checkLSBool("general", "statusPercents")) {
        armourHealth += "<br />(" + Math.round(armourhp * 100) + "%)";
      }

      armorData = sidebarHolder.querySelector("#statusBoxArmour");
      armorData.querySelector("img").src =
        "https://files.deadfrontier.com/deadfrontier/inventoryimages/large/" +
        armour[0] +
        ".png";
      armorData.querySelector("div").innerHTML = armourHealth;
      armorData.querySelector("div").style.color = "#" + armourHealthColor;
    } else {
      var armorData = sidebarHolder.querySelector("#statusBoxArmour");
      armorData.querySelector("img").src = "";
      armorData.querySelector("div").innerHTML = "";
    }

    var health = "Healthy";
    var healthColor = "12FF00";
    var hp = userVars["DFSTATS_df_hpcurrent"] / userVars["DFSTATS_df_hpmax"];
    if (hp <= 0) {
      health = "DEAD";
      healthColor = "D20303";
    } else if (hp < 0.25) {
      health = "Critical";
      healthColor = "D20303";
    } else if (hp < 0.5) {
      health = "Serious";
      healthColor = "FF4800";
    } else if (hp < 0.75) {
      health = "Injured";
      healthColor = "FFCC00";
    }
    health +=
      "<br />" +
      userVars["DFSTATS_df_hpcurrent"] +
      " / " +
      userVars["DFSTATS_df_hpmax"];
    if (checkLSBool("general", "statusPercents")) {
      health += "<br />(" + Math.round(hp * 100) + "%)";
    }
    for (let playerHealth of document.getElementsByClassName("playerHealth")) {
      playerHealth.innerHTML = health;
      playerHealth.style.color = "#" + healthColor;
    }
    health = "Nourished";
    healthColor = "12FF00";
    hp = userVars["DFSTATS_df_hungerhp"];
    if (hp <= 0) {
      health = "Dying";
      healthColor = "D20303";
    } else if (
      hp <
      Math.floor(
        (50 + Math.floor(userVars["DFSTATS_df_survival"] * 1.5)) * 0.25
      )
    ) {
      health = "Starving";
      healthColor = "D20303";
    } else if (
      hp <
      Math.floor((50 + Math.floor(userVars["DFSTATS_df_survival"] * 1.5)) * 0.5)
    ) {
      health = "Hungry";
      healthColor = "FF4800";
    } else if (
      hp <
      Math.floor(
        (50 + Math.floor(userVars["DFSTATS_df_survival"] * 1.5)) * 0.75
      )
    ) {
      health = "Fine";
      healthColor = "FFCC00";
    }
    health +=
      "<br />" +
      userVars["DFSTATS_df_hungerhp"] +
      " / " +
      (50 + Math.floor(userVars["DFSTATS_df_survival"] * 1.5));
    if (checkLSBool("general", "statusPercents")) {
      health +=
        "<br />(" +
        Math.round(
          (hp /
            Math.floor(
              50 + Math.floor(userVars["DFSTATS_df_survival"] * 1.5)
            )) *
            100
        ) +
        "%)";
    }
    for (let playerNourishment of document.getElementsByClassName(
      "playerNourishment"
    )) {
      playerNourishment.innerHTML = health;
      playerNourishment.style.color = "#" + healthColor;
    }
  }
  //renderAvatarUpdate();
  pageLock = false;
}

function updateAllFields() {
  updateAllFieldsBase();
  df_prompt.parentNode.style.display = "none";
  df_prompt.innerHTML = "";
}

function createSlotDisplay() {
  let slotDisplay = document.createElement("div");
  slotDisplay.id = "slotDisplay";

  let slotTitle = document.createElement("div");
  slotTitle.classList.add("slotTitle");
  slotTitle.textContent = "Try your luck!";
  slotDisplay.appendChild(slotTitle);

  for (let i = 1; i <= 3; i++) {
    let individualSlot = document.createElement("div");
    individualSlot.classList.add("slot");

    let innerSlot = document.createElement("div");
    innerSlot.classList.add("slotInner");
    innerSlot.dataset.slot = i;

    for (let i = 0; i <= 30; i++) {
      if (innerSlot.innerHTML.length > 0) {
        innerSlot.innerHTML += "<br />";
      }
      innerSlot.innerHTML += i % 10;
    }

    individualSlot.appendChild(innerSlot);
    slotDisplay.appendChild(individualSlot);
  }

  return slotDisplay;
}

let customColorLoadCache = {};

function renderAvatarUpdate(elem, customVars) {
  if (checkLSBool("general", "displayAvatars")) {
    if (elem) {
      var loadingCircleOfDoom = document.createElement("img");
      loadingCircleOfDoom.width = "100";
      loadingCircleOfDoom.src =
        "hotrods/hotrods_v" + hrV + "/HTML5/images/loading.gif";
      if (window.location.pathname.indexOf("/DF3D/") >= 0) {
        loadingCircleOfDoom.src = "../" + loadingCircleOfDoom.src;
      }
      loadingCircleOfDoom.style.position = "absolute";
      loadingCircleOfDoom.style.left = "30px";
      loadingCircleOfDoom.style.top = "20px";

      loadingCircleOfDoom.id = "loadingcircle";
      elem.appendChild(loadingCircleOfDoom);
    }

    var waitForSetup = setInterval(function () {
      if (noColor) {
        clearInterval(waitForSetup);
        if (!customVars) {
          customVars = userVars;
        }
        var temp;
        var gender = customVars["DFSTATS_df_gender"].toLowerCase();
        customVars["DFSTATS_df_avatar_hair_colour"] =
          customVars["DFSTATS_df_avatar_hair_colour"].charAt(0).toUpperCase() +
          customVars["DFSTATS_df_avatar_hair_colour"].slice(1);
        var renderData = {
          rightshoulder: "",
          leftshoulder: "",
          body: "",
          face: "",
          beard: "",
          trousers: "",
          shirt: "",
          armour: "",
          rightstrap: "",
          leftstrap: "",
          rightammobelt: "",
          leftholster: "",
          rightholster: "",
          knifeleft: "",
          kniferight: "",
          gunbelt: "",
          mask: "",
          hair: "",
          mask2: "",
          coat: "",
          hat: "",
        };
        var special = {
          shotgun: [
            "streetsweeper",
            "usan12",
            "aa12",
            "painshot10",
            "biforcec7",
            "acebarrel",
            "buckblast",
          ],
          rifle: [
            "2unlimitedg19",
            "unlimitedg19",
            "gau19",
            "m60",
            "fmmitrail",
            "fmmag",
            "vulcan",
            "xlgunner8",
            "hammerhead47",
            "scar9000",
            "wraithcannon",
            "uwraithcannon",
          ],
          faceMasks: [
            "hockeymask",
            "insanitymask",
            "eyepatch",
            "facebandana",
            "flyinggoggles",
            "gasmask",
            "rebreather",
            "scavengermask",
            "shinobumask",
            "sunglasses",
            "surgicalmask",
            "tacticalgoggles",
            "slashermask",
            "cultistmask",
            "elitecultistmask",
            "plagueseekermask",
            "festiveplaguemask",
            "revenantmask",
            "gunslingershades",
          ],
        };
        if (gender === "female") {
          special["faceMasks"].push("corpsemask");
        }

        renderData["body"] = customVars["DFSTATS_df_avatar_skin_colour"];
        renderData["face"] =
          customVars["DFSTATS_df_avatar_skin_colour"] +
          customVars["DFSTATS_df_avatar_face"];

        if (
          gender === "male" &&
          typeof customVars["DFSTATS_df_avatar_beard"] !== "undefined" &&
          customVars["DFSTATS_df_avatar_beard"] !== "none" &&
          customVars["DFSTATS_df_avatar_beard"] !== ""
        ) {
          renderData["beard"] = customVars["DFSTATS_df_avatar_beard"];
          if (!noColor.includes(customVars["DFSTATS_df_avatar_beard"])) {
            renderData["beard"] +=
              "_colour" + customVars["DFSTATS_df_avatar_hair_colour"];
          }
        }

        for (var i = 1; i <= 3; i++) {
          var beltWeapon = false;
          var shotgun = false;
          var rifle = false;
          var weaponSlot = customVars["DFSTATS_df_avatar_weapon" + i];
          var weaponType =
            customVars["DFSTATS_df_weapon" + i + "type"].split("_")[0];
          if (special["shotgun"].includes(weaponType)) {
            beltWeapon = true;
            shotgun = true;
          } else if (special["rifle"].includes(weaponType)) {
            beltWeapon = true;
            rifle = true;
          }
          if (beltWeapon && renderData["rightammobelt"] === "") {
            if (shotgun) {
              renderData["rightammobelt"] = "shotgun";
            } else if (rifle) {
              renderData["rightammobelt"] = "rifle";
            }
          }
          if (weaponSlot === "none" || weaponSlot === "") {
            continue;
          }
          switch (weaponSlot) {
            case "none":
            case "chainsaw":
              break;
            case "knife": {
              if (renderData["kniferight"] !== "") {
                renderData["knifeleft"] = "grey";
              } else {
                renderData["kniferight"] = "grey";
              }
              break;
            }
            case "pistol": {
              if (renderData["leftholster"] !== "") {
                renderData["rightholster"] = "grey";
              } else {
                renderData["leftholster"] = "grey";
              }
              renderData["gunbelt"] = "grey";
              break;
            }
            case "wood":
            case "sword":
            case "riflewood":
            case "metal": {
              if (renderData["leftshoulder"] !== "" || beltWeapon) {
                renderData["rightshoulder"] = weaponSlot;
                // strap
                renderData["rightstrap"] = "brown";
              } else {
                renderData["leftshoulder"] = weaponSlot;
                // strap
                renderData["leftstrap"] = "brown";
              }
              break;
            }
            default: {
              if (renderData["leftshoulder"] !== "" || beltWeapon) {
                renderData["rightshoulder"] = weaponSlot;
                // strap
                renderData["rightstrap"] = "black";
              } else {
                renderData["leftshoulder"] = weaponSlot;
                // strap
                renderData["leftstrap"] = "black";
              }
              break;
            }
          }
        }

        let tempMatch;
        let colorData;
        if (customVars["DFSTATS_df_avatar_trousers"] !== "") {
          temp = customVars["DFSTATS_df_avatar_trousers"].split("_");
          renderData["trousers"] = temp[0];
          if (!noColor.includes(temp[0])) {
            tempMatch = false;
            colorData = false;
            if (
              (tempMatch = customVars["DFSTATS_df_avatar_trousers"].match(
                /(?<=_colour)([0-9a-zA-Z\^ ]+)(?=$|_)/
              ))
            ) {
              colorData = tempMatch[0];
            }
            if (
              (tempMatch = customVars["DFSTATS_df_avatar_trousers"].match(
                /(?<=_colour)rgb\(([0-9\,]+)\)(?=$|_)/
              ))
            ) {
              colorData = colorsBytes.join("^");
            }
            if (colorData === false) {
              renderData["trousers"] += "_colourGrey";
            } else {
              renderData["trousers"] += "_colour" + colorData;
            }
          }
        }

        if (customVars["DFSTATS_df_avatar_shirt"] !== "") {
          temp = customVars["DFSTATS_df_avatar_shirt"].split("_");
          renderData["shirt"] = temp[0];
          if (!noColor.includes(temp[0])) {
            tempMatch = false;
            colorData = false;
            if (
              (tempMatch = customVars["DFSTATS_df_avatar_shirt"].match(
                /(?<=_colour)([0-9a-zA-Z\^ ]+)(?=$|_)/
              ))
            ) {
              colorData = tempMatch[0];
            }
            if (
              (tempMatch = customVars["DFSTATS_df_avatar_shirt"].match(
                /(?<=_colour)rgb\(([0-9\,]+)\)(?=$|_)/
              ))
            ) {
              colorData = colorsBytes.join("^");
            }
            if (colorData === false) {
              renderData["shirt"] += "_colourGrey";
            } else {
              renderData["shirt"] += "_colour" + colorData;
            }
          }
        }

        if (
          customVars["DFSTATS_df_armourtype"] !== "" &&
          customVars["DFSTATS_df_hidearmour"] === "0"
        ) {
          temp = customVars["DFSTATS_df_armourtype"].split("_");
          renderData["armour"] = temp[0];
          if (!noColor.includes(temp[0])) {
            tempMatch = false;
            colorData = false;
            if (
              (tempMatch = customVars["DFSTATS_df_armourtype"].match(
                /(?<=_colour)([0-9a-zA-Z\^ ]+)(?=$|_)/
              ))
            ) {
              colorData = tempMatch[0];
            }
            if (
              (tempMatch = customVars["DFSTATS_df_armourtype"].match(
                /(?<=_colour)rgb\(([0-9\,]+)\)(?=$|_)/
              ))
            ) {
              colorData = colorsBytes.join("^");
            }
            if (colorData === false) {
              renderData["armour"] += "_colourGrey";
            } else {
              renderData["armour"] += "_colour" + colorData;
            }
          }
        }

        if (
          customVars["DFSTATS_df_avatar_mask"] !== "" &&
          customVars["DFSTATS_df_avatar_mask"] !== "blocked_slot"
        ) {
          temp = customVars["DFSTATS_df_avatar_mask"].split("_");
          if (special["faceMasks"].includes(temp[0])) {
            renderData["mask"] = temp[0];
            if (!noColor.includes(temp[0])) {
              tempMatch = false;
              colorData = false;
              if (
                (tempMatch = customVars["DFSTATS_df_avatar_mask"].match(
                  /(?<=_colour)([0-9a-zA-Z\^ ]+)(?=$|_)/
                ))
              ) {
                colorData = tempMatch[0];
              }
              if (
                (tempMatch = customVars["DFSTATS_df_avatar_mask"].match(
                  /(?<=_colour)rgb\(([0-9\,]+)\)(?=$|_)/
                ))
              ) {
                colorData = colorsBytes.join("^");
              }
              if (colorData === false) {
                renderData["mask"] += "_colourGrey";
              } else {
                renderData["mask"] += "_colour" + colorData;
              }
            }
          } else {
            renderData["mask2"] = temp[0];
            if (!noColor.includes(temp[0])) {
              tempMatch = false;
              colorData = false;
              if (
                (tempMatch = customVars["DFSTATS_df_avatar_mask"].match(
                  /(?<=_colour)([0-9a-zA-Z\^ ]+)(?=$|_)/
                ))
              ) {
                colorData = tempMatch[0];
              }
              if (
                (tempMatch = customVars["DFSTATS_df_avatar_mask"].match(
                  /(?<=_colour)rgb\(([0-9\,]+)\)(?=$|_)/
                ))
              ) {
                colorData = colorsBytes.join("^");
              }
              if (colorData === false) {
                renderData["mask2"] += "_colourGrey";
              } else {
                renderData["mask2"] += "_colour" + colorData;
              }
            }
          }
        }

        if (!noColor.includes(customVars["DFSTATS_df_avatar_hair"])) {
          renderData["hair"] =
            customVars["DFSTATS_df_avatar_hair"] +
            "_colour" +
            customVars["DFSTATS_df_avatar_hair_colour"];
        }

        if (customVars["DFSTATS_df_avatar_coat"] !== "") {
          temp = customVars["DFSTATS_df_avatar_coat"].split("_");
          renderData["coat"] = temp[0];
          if (!noColor.includes(temp[0])) {
            tempMatch = false;
            colorData = false;
            if (
              (tempMatch = customVars["DFSTATS_df_avatar_coat"].match(
                /(?<=_colour)([0-9a-zA-Z\^ ]+)(?=$|_)/
              ))
            ) {
              colorData = tempMatch[0];
            }
            if (
              (tempMatch = customVars["DFSTATS_df_avatar_coat"].match(
                /(?<=_colour)rgb\(([0-9\,]+)\)(?=$|_)/
              ))
            ) {
              colorData = colorsBytes.join("^");
            }
            if (colorData === false) {
              renderData["coat"] += "_colourGrey";
            } else {
              renderData["coat"] += "_colour" + colorData;
            }
          }
        }

        if (
          customVars["DFSTATS_df_avatar_hat"] !== "blocked_slot" &&
          customVars["DFSTATS_df_avatar_hat"] !== ""
        ) {
          temp = customVars["DFSTATS_df_avatar_hat"].split("_");
          renderData["hat"] = temp[0];
          if (!noColor.includes(temp[0])) {
            tempMatch = false;
            colorData = false;
            if (
              (tempMatch = customVars["DFSTATS_df_avatar_hat"].match(
                /(?<=_colour)([0-9a-zA-Z\^ ]+)(?=$|_)/
              ))
            ) {
              colorData = tempMatch[0];
            }
            if (
              (tempMatch = customVars["DFSTATS_df_avatar_hat"].match(
                /(?<=_colour)rgb\(([0-9\,]+)\)(?=$|_)/
              ))
            ) {
              colorData = colorsBytes.join("^");
            }
            if (colorData === false) {
              if (temp[0] === "militaryhelmet") {
                renderData["hat"] += "_colourDesert Camo";
              } else {
                renderData["hat"] += "_colourGrey";
              }
            } else {
              renderData["hat"] += "_colour" + colorData;
            }
          }
        }

        var c = document.createElement("canvas");
        c.width = "160";
        c.height = "350";
        var ctx = c.getContext("2d");
        var images = [];
        var imageLocation =
          "https://files.deadfrontier.com/deadfrontier/avatars/";
        var dynamicImageLocation = "hotrods/hotrods_avatars/";
        for (let renKey in renderData) {
          if (renderData[renKey].length > 0) {
            let regMatch = /^([\da-zA-Z \s]+)(?=$|_)/.exec(renderData[renKey]);
            let itemCode = regMatch[0].trim();
            let colourData = "";
            if (
              (regMatch = renderData[renKey].match(
                /(?<=_colour)([0-9a-zA-Z\^ ]+)(?=$|_)/
              ))
            ) {
              colourData = regMatch[0];
            }
            if (
              (regMatch = renderData[renKey].match(
                /(?<=_colour)rgb\(([0-9\,]+)\)(?=$|_)/
              ))
            ) {
              let colorsBytes = regMatch[1].split(",");
              colourData = colorsBytes.join("^");
            }
            if (renderData[renKey].indexOf("^") !== -1) {
              let rgbVal = colourData.match(/(\d*)\^(\d*)\^(\d*)/)[0];
              images.push(
                dynamicImageLocation +
                  "hascolor.php?gender=" +
                  gender +
                  "&group=" +
                  renKey +
                  "&renderitem=" +
                  itemCode +
                  "&color=" +
                  rgbVal +
                  "&userID=" +
                  customVars["DFSTATS_id_member"]
              );
            } else {
              images.push(
                imageLocation +
                  gender +
                  "/" +
                  renKey +
                  "/" +
                  itemCode +
                  (colourData.length > 0 ? "_colour" + colourData : "") +
                  ".png"
              );
            }
          }
        }
        var i = 0;
        var compileImage = function () {
          if (i < images.length) {
            var actualCall = images[i];
            var appendToImgCall = "";
            if (images[i].indexOf("^") !== -1) {
              let img = new Image();
              img.addEventListener("load", function () {
                ctx.drawImage(img, 0, 0, c.width, c.height);
                i++;
                compileImage();
              });
              img.addEventListener("error", function () {
                i++;
                compileImage();
              });
              if (window.location.pathname.indexOf("/DF3D/") >= 0) {
                actualCall = "../" + actualCall;
                appendToImgCall = "../";
              }
              if (typeof customColorLoadCache[actualCall] === "undefined") {
                let xhr = new XMLHttpRequest();
                xhr.open("GET", actualCall);
                xhr.setRequestHeader("X-Requested-With", "xmlhttprequest");
                xhr.addEventListener("load", function () {
                  if (xhr.responseText === "false") {
                    i++;
                    compileImage();
                    return;
                  }
                  customColorLoadCache[actualCall] =
                    appendToImgCall +
                    "hotrods/hotrods_avatars/" +
                    xhr.responseText;
                  img.src = appendToImgCall + customColorLoadCache[actualCall];
                });
                xhr.send();
              } else {
                img.src = customColorLoadCache[actualCall];
              }
            } else {
              var img = new Image();
              img.onload = function () {
                ctx.drawImage(img, 0, 0, c.width, c.height);
                i++;
                compileImage();
              };
              img.onerror = function () {
                if (images[i].indexOf(imageLocation) !== -1) {
                  /*
									if(window.location.host !== "fairview.deadfrontier.com")
									{
										images[i] = images[i].replace(imageLocation, "hotrods/hotrods_avatars/render/");
										if(window.location.pathname.indexOf("/DF3D/") >= 0)
										{
											images[i] = "../" + images[i];
										}
									} else
									{
										images[i] = images[i].replace(imageLocation, "https://files.deadfrontier.com/hotrods/hotrods_avatars/render/");
									}*/
                  images[i] = images[i].replace(
                    imageLocation,
                    "https://files.deadfrontier.com/hotrods/hotrods_avatars/render/"
                  );
                } else {
                  i++;
                }
                compileImage();
              };
              img.src = actualCall;
            }
          } else {
            if (elem) {
              elem.innerHTML = "";
              elem.appendChild(c);
            } else {
              for (let characterRender of document.querySelectorAll(
                ".characterRender"
              )) {
                characterRender.innerHTML = "";
                characterRender.appendChild(cloneCanvas(c));
              }
            }
          }
        };
        compileImage();
      }
    }, 10);
  }
}

function loadBuyButton(elem, flshVars, btnText = "buy now") {
  elem.innerHTML =
    "<span>" +
    (flshVars["shop"] === "credit"
      ? nf.format(flshVars["cost"]) + " Credits"
      : "$" + nf.format(flshVars["cost"])) +
    "</span>";
  elem.innerHTML += "<br />";
  var buyButton = document.createElement("button");
  buyButton.textContent = btnText;
  var userCurrency = 0;
  if (flshVars["shop"] === "credit") {
    userCurrency = parseInt(flshVars["user_credits"]);
  } else {
    userCurrency = parseInt(flshVars["cash"]);
  }
  if (
    flshVars["shop"] !== "credit" &&
    parseInt(flshVars["cost"]) > userCurrency
  ) {
    buyButton.disabled = true;
  }
  buyButton.onclick = function () {
    buyButton.disabled = true;
    if (
      flshVars["shop"] === "credit" &&
      parseInt(flshVars["cost"]) > userCurrency
    ) {
      window.location =
        "https://fairview.deadfrontier.com/onlinezombiemmo/index.php?page=29;message=1";
      return;
    }
    var outputBox = document.createElement("div");
    var noButton = document.createElement("button");
    noButton.onclick = function () {
      loadBuyButton(elem, flshVars);
    };
    outputBox.dataset.type = "question";
    outputBox.textContent = "Are you sure?";
    noButton.style.float = "right";
    noButton.textContent = "no";
    var yesButton = document.createElement("button");
    yesButton.onclick = function () {
      yesButton.disabled = true;
      outputBox.textContent = "Loading...";
      var dataVars = {};
      dataVars["templateID"] = flshVars["template_ID"];
      dataVars["sc"] = flshVars["sc"];
      dataVars["itemnum"] = flshVars["itemnum"];
      dataVars["action"] = flshVars["action"];
      if (flshVars["action"] === "switchga") {
        dataVars["type1"] = document.getElementById("ammoYouOwn").value;
        dataVars["type2"] = document.getElementById("ammoYouDoNotOwn").value;
      }
      dataVars["userID"] = flshVars["userID"];
      dataVars["password"] = flshVars["password"];
      webCall("shop", dataVars, function (data, status, xhr) {
        data = flshToArr(data);
        if (
          xhr["responseURL"] &&
          xhr["responseURL"].indexOf("page=36") !== -1
        ) {
          outputBox.dataset.type = "message";
          outputBox.style.left = "20px";
          outputBox.style.right = "20px";
          outputBox.style.paddingLeft = "4px";
          outputBox.style.paddingRight = "4px";
          outputBox.textContent =
            "Please remove your armour and weapons to purchase this";
          outputBox.onclick = function () {
            loadBuyButton(elem, flshVars);
          };
        } else {
          if (
            data["success"] === "true" &&
            typeof data["redirect"] !== "undefined"
          ) {
            if (typeof data["scategory"] !== "undefined") {
              window.location.href =
                data["redirect"] + "&scategory=" + data["scategory"];
            } else {
              window.location.href = data["redirect"];
            }
          } else {
            setTimeout(function () {
              window.location.reload();
            }, 1000);
          }
        }
      });
    };
    yesButton.textContent = "yes";
    yesButton.style.float = "left";
    outputBox.appendChild(yesButton);
    outputBox.appendChild(noButton);

    elem.appendChild(outputBox);
  };
  elem.appendChild(buyButton);
}

function cloneCanvas(oldCanvas) {
  //create a new canvas
  var newCanvas = document.createElement("canvas");
  var context = newCanvas.getContext("2d");
  //set dimensions
  newCanvas.width = oldCanvas.width;
  newCanvas.height = oldCanvas.height;
  //apply the old canvas to the new one
  context.drawImage(oldCanvas, 0, 0);
  //return the new canvas
  return newCanvas;
}

function initData(flashVars, callback) {
  var flashBlock = flashVars.split("&");
  var outputData = {};
  for (var i = 0; i < flashBlock.length; i++) {
    var temp = flashBlock[i].split("=");
    outputData[temp[0]] = temp[1];
  }
  userVars = outputData;
  if (typeof callback !== "undefined") {
    callback();
  }
}

function nChangePage(e) {
  if (e.which !== 1 && e.which !== 2) {
    return;
  }
  var elem = e.currentTarget;
  var sound = false;
  if (elem.dataset.sound && elem.dataset.sound != 0) {
    sound = true;
  }
  var nTab = false;
  if (e.which === 2) {
    nTab = true;
    e.preventDefault();
  }
  pageNum = parseInt(elem.dataset.page);
  modify = elem.dataset.mod;
  if (sound) {
    playSound("outpost");
    setTimeout(function () {
      doPageChange(pageNum, modify, nTab);
    }, 1000);
  } else {
    doPageChange(pageNum, modify, nTab);
  }
}

function doPageChange(pageNum, modify, nTab) {
  if (typeof modify !== "undefined" && modify !== "0") {
    let dataArr = {
      redirect_url: "index.php?page=21",
      templateID: userVars["template_ID"],
      sc: userVars["sc"],
      userID: userVars["userID"],
      password: userVars["password"],
      "default_options[df_positionz]": modify * 1000,
      "default_options[df_positiony]": modify * 1000,
      "default_options[df_positionx]": modify * 1000,
    };
    webCall("modify_values", dataArr, function (webData) {
      if (pageNum) {
        document.location.href = "index.php?page=" + pageNum;
      } else {
        document.location.href = "index.php?action=forum";
      }
    });
  } else if (pageNum) {
    if (nTab) {
      window.open("index.php?page=" + pageNum, "_blank").focus();
    } else {
      document.location.href = "index.php?page=" + pageNum;
    }
  } else {
    if (nTab) {
      window.open("index.php?action=forum", "_blank").focus();
    } else {
      document.location.href = "index.php?action=forum";
    }
  }
  return false;
}

function loadSettings() {
  var settingsBox = document.getElementById("settingsBox");
  settingsBox.src = "hotrods/hotrods_v" + hrV + "/HTML5/pages/settings.html";
  settingsBox.style.display = "block";
}

function loadSidebar(flashErl) {
  var profileVars = flshToArr(flashErl, "");

  var armour = profileVars["DFSTATS_df_armourtype"].split("_");

  var sideOutput = "";
  sideOutput +=
    "<div class='opElem characterRender' style='left: -24px; top: 80px;'></div>";

  sideOutput +=
    "<div class='opElem' style='top: 0px; left: 3px; font-size: 13px; text-align: left; z-index: 1;'>";
  sideOutput += "<a href='index.php?page=25'>Inventory & Equipment</a><br />";
  sideOutput +=
    "<a href='index.php?action=pm'>Message (" +
    profileVars["DFSTATS_newpms"] +
    " New)</a><br />";
  sideOutput += "<a href='index.php?action=profile'>My Profile</a><br />";
  sideOutput += "<a href='index.php?page=62'>Challenges</a><br />";
  sideOutput += "<a href='index.php?page=81'>Masteries</a>";
  sideOutput += "</div>";

  sideOutput +=
    "<div class='opElem' style='top: 80px; left: 0px; width: 95%; text-align: right;'>";
  sideOutput +=
    "<span style='color: #ff0000'>" +
    profileVars["DFSTATS_df_name"] +
    "</span><br />";
  sideOutput +=
    "<span style='color: #0066ff'>" +
    profileVars["DFSTATS_df_clan_name"] +
    "</span><br />";
  sideOutput +=
    "<span style='color: #cccccc'>" +
    profileVars["DFSTATS_df_profession"] +
    " Level " +
    profileVars["DFSTATS_df_level"] +
    "</span><br />";

  var expNeeded = "------";
  if (parseInt(profileVars["DFSTATS_df_level"]) < 415) {
    expNeeded = nf.format(
      profileVars[
        "EXPTABLE_exp_lvl" + (parseInt(profileVars["DFSTATS_df_level"]) + 1)
      ]
    );
  }
  sideOutput +=
    "<span style='color: #cccccc'>" +
    nf.format(profileVars["DFSTATS_df_exp"]) +
    " / " +
    expNeeded +
    "</span><br />";

  var cash = "Cash: $" + nf.format(profileVars["DFSTATS_df_cash"]);
  sideOutput +=
    "<span class='heldCash cashhack' style='position: relative;' data-cash='" +
    cash +
    "'>" +
    cash +
    "</span><br />";

  var credits = "Credits: " + nf.format(profileVars["DFSTATS_df_credits"]);
  sideOutput +=
    "<span class='credits heldCredits cashhack' style='position: relative;' data-cash='" +
    credits +
    "'>" +
    credits +
    "</span><br />";
  sideOutput += "</div>";

  var health = "Healthy";
  var healthColor = "12FF00";
  var hp =
    parseInt(profileVars["DFSTATS_df_hpcurrent"]) /
    parseInt(profileVars["DFSTATS_df_hpmax"]);
  if (hp <= 0) {
    health = "DEAD";
    healthColor = "D20303";
  } else if (hp < 0.25) {
    health = "Critical";
    healthColor = "D20303";
  } else if (hp < 0.5) {
    health = "Serious";
    healthColor = "FF4800";
  } else if (hp < 0.75) {
    health = "Injured";
    healthColor = "FFCC00";
  }
  health +=
    "<br />" +
    profileVars["DFSTATS_df_hpcurrent"] +
    " / " +
    profileVars["DFSTATS_df_hpmax"];
  if (checkLSBool("general", "statusPercents")) {
    health += "<br />(" + Math.round(hp * 100) + "%)";
  }

  sideOutput +=
    "<div class='opElem' style='top: 210px; left: 82px;'><img class='opElem' style='text-align: right;' src='hotrods/hotrods_v" +
    hrV +
    "/HTML5/images/heart.png'>";
  sideOutput +=
    "<div class='opElem playerHealth' style='top: 3px; left: 28px; width: 65px; text-align: center; font-weight: 100; color: #" +
    healthColor +
    ";'>" +
    health +
    "</div></div>";

  health = "Nourished";
  healthColor = "12FF00";
  hp = parseInt(profileVars["DFSTATS_df_hungerhp"]);
  if (hp <= 0) {
    health = "Dying";
    healthColor = "D20303";
  } else if (
    hp <
    Math.floor(
      (50 + Math.floor(profileVars["DFSTATS_df_survival"] * 1.5)) * 0.25
    )
  ) {
    health = "Starving";
    healthColor = "D20303";
  } else if (
    hp <
    Math.floor(
      (50 + Math.floor(profileVars["DFSTATS_df_survival"] * 1.5)) * 0.5
    )
  ) {
    health = "Hungry";
    healthColor = "FF4800";
  } else if (
    hp <
    Math.floor(
      (50 + Math.floor(profileVars["DFSTATS_df_survival"] * 1.5)) * 0.75
    )
  ) {
    health = "Fine";
    healthColor = "FFCC00";
  }
  health +=
    "<br />" +
    profileVars["DFSTATS_df_hungerhp"] +
    " / " +
    (50 + Math.floor(profileVars["DFSTATS_df_survival"] * 1.5));
  if (checkLSBool("general", "statusPercents")) {
    health +=
      "<br />(" +
      Math.round(
        (hp /
          Math.floor(
            50 + Math.floor(profileVars["DFSTATS_df_survival"] * 1.5)
          )) *
          100
      ) +
      "%)";
  }

  sideOutput +=
    "<div class='opElem' style='top: 165px; left: 82px;'><img class='opElem' style='text-align: right;' src='hotrods/hotrods_v" +
    hrV +
    "/HTML5/images/yummytummy.png'>";
  sideOutput +=
    "<div class='opElem playerNourishment' style='top: 3px; left: 28px; width: 65px; text-align: center; font-weight: 100; color: #" +
    healthColor +
    ";'>" +
    health +
    "</div></div>";

  sideOutput +=
    "<div id='sidebarArmour' class='opElem' style='top: 255px; left: 82px; text-align: right;'>";
  sideOutput += "<img src='";
  if (armour[0] !== "") {
    sideOutput +=
      "https://files.deadfrontier.com/deadfrontier/inventoryimages/large/" +
      armour[0] +
      ".png' width='40' />";
    health = "Normal";
    healthColor = "12FF00";
    hp =
      parseInt(profileVars["DFSTATS_df_armourhp"]) /
      parseInt(profileVars["DFSTATS_df_armourhpmax"]);
    if (hp <= 0) {
      health = "Broken";
      healthColor = "D20303";
    } else if (hp < 0.4) {
      health = "Damaged";
      healthColor = "FF4800";
    } else if (hp < 0.75) {
      health = "Scratched";
      healthColor = "FFCC00";
    }
    health +=
      "<br />" +
      profileVars["DFSTATS_df_armourhp"] +
      " / " +
      profileVars["DFSTATS_df_armourhpmax"];
    if (checkLSBool("general", "statusPercents")) {
      health += "<br />(" + Math.round(hp * 100) + "%)";
    }

    sideOutput +=
      "<div class='opElem' style='top: 3px; left: 28px; width: 65px; text-align: center; font-weight: 100; color: #" +
      healthColor +
      ";'>" +
      health +
      "</div>";
  } else {
    sideOutput += "' width='40' />";
    sideOutput +=
      "<div class='opElem' style='top: 3px; left: 28px; width: 65px; text-align: center; font-weight: 100;'></div>";
  }
  sideOutput += "</div>";

  sideOutput +=
    "<div class='opElem boostTimes' style='top: 306px; left: 0px; width: 100%; text-align: left;  font-weight: 100; color: #00ff00;'>";
  var sideOutputExp = 0;
  if (
    parseInt(profileVars["DFSTATS_df_boostexpuntil"]) >=
    parseInt(profileVars["DFSTATS_df_servertime"]) + **********
  ) {
    sideOutputExp += 50;
  }
  if (
    parseInt(profileVars["DFSTATS_df_boostexpuntil_ex"]) >=
    parseInt(profileVars["DFSTATS_df_servertime"]) + **********
  ) {
    sideOutputExp += 50;
  }
  if (sideOutputExp > 0) {
    sideOutput += "+" + sideOutputExp + "% Exp Boost";
  }
  var sideOutputDamage = 0;
  if (
    parseInt(profileVars["DFSTATS_df_boostdamageuntil"]) >=
    parseInt(profileVars["DFSTATS_df_servertime"]) + **********
  ) {
    sideOutputDamage += 35;
  }
  if (
    parseInt(profileVars["DFSTATS_df_boostdamageuntil_ex"]) >=
    parseInt(profileVars["DFSTATS_df_servertime"]) + **********
  ) {
    sideOutputDamage += 35;
  }
  if (sideOutputDamage > 0) {
    if (sideOutput !== "") {
      sideOutput += "<br />";
    }
    sideOutput += "+" + sideOutputDamage + "% Damage Boost";
  }
  var sideOutputSpeed = 0;
  if (
    parseInt(profileVars["DFSTATS_df_boostspeeduntil"]) >=
    parseInt(profileVars["DFSTATS_df_servertime"]) + **********
  ) {
    sideOutputSpeed += 35;
  }
  if (
    parseInt(profileVars["DFSTATS_df_boostspeeduntil_ex"]) >=
    parseInt(profileVars["DFSTATS_df_servertime"]) + **********
  ) {
    sideOutputSpeed += 35;
  }
  if (sideOutputSpeed > 0) {
    if (sideOutput !== "") {
      sideOutput += "<br />";
    }
    sideOutput += "+" + sideOutputSpeed + "% Speed Boost";
  }

  sideOutput += "</div>";

  sideOutput +=
    "<img class='opElem' style='left: 0px; top: 343px;' src='hotrods/hotrods_v" +
    hrV +
    "/HTML5/images/sidebar/weaponholder.png'>";

  sideOutput +=
    "<div class='opElem weapon' style='top: 367px; left: 0px; width: 100%; text-align: center; color: #f00000; font-weight: 100;'></div>";
  sideOutput +=
    "<div class='opElem weapon' style='top: 420px; left: 0px; width: 100%; text-align: center; color: #f00000; font-weight: 100;'></div>";
  sideOutput +=
    "<div class='opElem weapon' style='top: 473px; left: 0px; width: 100%; text-align: center; color: #f00000; font-weight: 100;'></div>";

  var sidebarElem = document.getElementById("sidebar");
  sidebarElem.innerHTML = sideOutput;

  var weapons = [];
  weapons[0] = profileVars["DFSTATS_df_weapon1type"].trim();
  weapons[1] = profileVars["DFSTATS_df_weapon2type"].trim();
  weapons[2] = profileVars["DFSTATS_df_weapon3type"].trim();

  for (var i = 0; i < weapons.length; i++) {
    if (weapons[i] && weapons[i].length > 0) {
      var weapRealNum = parseInt(i) + 1;
      sidebarElem.querySelectorAll("div.weapon")[i].innerHTML =
        "<img src='https://files.deadfrontier.com/deadfrontier/inventoryimages/large/" +
        pickItemImageSubStr(weapons[i]) +
        ".png' />";
      if (
        profileVars["DFSTATS_df_weapon" + weapRealNum + "ammo"] &&
        profileVars["DFSTATS_df_weapon" + weapRealNum + "ammo"] !== ""
      ) {
        var wepAmmo = nf.format(
          profileVars[
            "DFSTATS_df_" +
              profileVars["DFSTATS_df_weapon" + weapRealNum + "ammo"]
          ]
        );
        sidebarElem.querySelectorAll("div.weapon")[i].innerHTML +=
          "<div>" + wepAmmo + " rounds</div>";
      }
    } else {
      sidebarElem.querySelectorAll("div.weapon")[i].innerHTML = "";
    }
  }

  renderAvatarUpdate(
    sidebarElem.querySelector(".characterRender"),
    profileVars
  );

  if (profileVars["checkstuff"] === "1") {
    checkIfLevelUp(
      profileVars[
        "EXPTABLE_exp_lvl" + (parseInt(profileVars["DFSTATS_df_level"]) + 1)
      ],
      profileVars["DFSTATS_df_exp"],
      profileVars["DFSTATS_df_freepoints"],
      profileVars["DFSTATS_df_level"],
      profileVars["password"],
      profileVars["userID"],
      profileVars["sc"],
      profileVars["template_ID"]
    );
  }
  baseGameUpdates(
    profileVars["DFSTATS_df_hpcurrent"],
    profileVars["was_dead"],
    profileVars["DFSTATS_df_minioutpost"],
    profileVars["server_time"],
    profileVars["DFSTATS_df_hungertime"],
    profileVars["DFSTATS_df_lastspawntime"],
    profileVars["password"],
    profileVars["userID"],
    profileVars["sc"],
    profileVars["template_ID"],
    profileVars["tts"]
  );
}

function loadSignature(elem, flashErl) {
  var profileVars = flshToArr(flashErl, "");
  var sigOutput = "";

  var sigWeapons = {};
  if (profileVars["DFSTATS_df_weapon1type"]) {
    sigWeapons[0] = profileVars["DFSTATS_df_weapon1type"].trim();
  }
  if (profileVars["DFSTATS_df_weapon2type"]) {
    sigWeapons[1] = profileVars["DFSTATS_df_weapon2type"].trim();
  }
  if (profileVars["DFSTATS_df_weapon3type"]) {
    sigWeapons[2] = profileVars["DFSTATS_df_weapon3type"].trim();
  }
  var sigArmour;
  if (profileVars["DFSTATS_df_armourtype"]) {
    sigArmour = profileVars["DFSTATS_df_armourtype"].split("_");
  }

  sigOutput +=
    "<div class='opElem characterRender' style='left: 30px; top: -15px;'></div>";
  sigOutput +=
    "<div class='opElem' style='color: #fe0000; left: 4px; top: 2px;'>" +
    profileVars["DFSTATS_df_name"] +
    "</div>";
  if (profileVars["DFSTATS_df_clan_id"] !== "-1") {
    sigOutput +=
      "<div class='opElem' style='color: #0065fe; left: 4px; top: 15px;'>" +
      profileVars["DFSTATS_df_clan_name"] +
      "</div>";
  }
  sigOutput +=
    "<div class='opElem' style='left: 4px; top: 28px;'>" +
    profileVars["DFSTATS_df_profession"] +
    "</div>";
  sigOutput +=
    "<div class='opElem' style='left: 4px; top: 40px;'>Level " +
    profileVars["DFSTATS_df_level"] +
    "</div>";
  var sigTradezone =
    tradezoneNamerShort(parseInt(profileVars["DFSTATS_df_tradezone"])) +
    " Zone";
  sigOutput +=
    "<div class='opElem' style='left: 4px; top: 52px;'>" +
    sigTradezone +
    "</div>";
  sigOutput +=
    "<div class='opElem' style='left: 4px; top: 93px;'><a href='index.php?action=profile;u=" +
    profileVars["DFSTATS_id_member"] +
    "'>profile</a></div>";
  sigOutput +=
    "<div class='opElem' style='left: 4px; top: 112px;'><a href='index.php?page=27&memto=" +
    profileVars["DFSTATS_id_member"] +
    "'>trade</a></div>";

  if (sigWeapons[2] && sigWeapons[2] && sigWeapons[2].length > 0) {
    sigOutput +=
      "<div class='opElem' style='left: 255px; top: 74px;'><img src='https://files.deadfrontier.com/deadfrontier/inventoryimages/large/" +
      pickItemImageSubStr(sigWeapons[2]) +
      ".png' alt='" +
      pickItemImageSubStr(sigWeapons[2]) +
      "'></div>";
    sigOutput +=
      "<div class='opElem' style='left: 255px; top: 108px; width: 160px; text-align: center;'>" +
      profileVars["DFSTATS_df_weapon3name"] +
      "</div>";
  }

  if (sigWeapons[1] && sigWeapons[1][0] && sigWeapons[1].length > 0) {
    sigOutput +=
      "<div class='opElem' style='left: 255px; top: 16px;'><img src='https://files.deadfrontier.com/deadfrontier/inventoryimages/large/" +
      pickItemImageSubStr(sigWeapons[1]) +
      ".png' alt='" +
      pickItemImageSubStr(sigWeapons[1]) +
      "'></div>";
    sigOutput +=
      "<div class='opElem' style='left: 255px; top: 52px; width: 160px; text-align: center;'>" +
      profileVars["DFSTATS_df_weapon2name"] +
      "</div>";
  }

  if (sigWeapons[0] && sigWeapons[0][0] && sigWeapons[0][0].length > 0) {
    sigOutput +=
      "<div class='opElem' style='left: 90px; top: 74px;'><img src='https://files.deadfrontier.com/deadfrontier/inventoryimages/large/" +
      pickItemImageSubStr(sigWeapons[0]) +
      ".png' alt='" +
      pickItemImageSubStr(sigWeapons[0]) +
      "'></div>";
    sigOutput +=
      "<div class='opElem' style='left: 90px; top: 108px; width: 160px; text-align: center;'>" +
      profileVars["DFSTATS_df_weapon1name"] +
      "</div>";
  }

  if (sigArmour) {
    sigOutput +=
      "<div class='opElem' style='left: 150px; top: 10px;'><img src='https://files.deadfrontier.com/deadfrontier/inventoryimages/large/" +
      sigArmour[0] +
      ".png' alt='" +
      sigArmour[0] +
      "'></div>";
    sigOutput +=
      "<div class='opElem' style='left: 98px; top: 52px; width: 160px; text-align: center;'>" +
      profileVars["DFSTATS_df_armourname"] +
      "</div>";
  }

  elem.innerHTML = sigOutput;

  if (checkLSBool("forum", "displayAvatars")) {
    renderAvatarUpdate(elem.querySelector(".characterRender"), profileVars);
  }
}

function allElementsFromPoint(x, y) {
  var element,
    elements = [];
  var old_visibility = [];
  while (true) {
    element = document.elementFromPoint(x, y);
    if (!element || element === document.documentElement) {
      break;
    }
    elements.push(element);
    old_visibility.push(element.style.visibility);
    element.style.visibility = "hidden"; // Temporarily hide the element (without changing the layout)
  }
  for (var k = 0; k < elements.length; k++) {
    elements[k].style.visibility = old_visibility[k];
  }
  elements.reverse();
  return elements;
}

function specificElementFromPoint(x, y, delim, isId) {
  if (typeof isId === "undefined") {
    isId = true;
  }
  var element,
    elements = [];
  var old_visibility = [];
  while (true) {
    element = document.elementFromPoint(x, y);
    if (!element || element === document.documentElement) {
      break;
    }
    elements.push(element);
    old_visibility.push(element.style.visibility);
    element.style.visibility = "hidden"; // Temporarily hide the element (without changing the layout)
    if (isId) {
      if (element.id === delim) {
        break;
      }
    } else {
      if (element.classList.contains(delim)) {
        break;
      }
    }
  }
  for (var k = 0; k < elements.length; k++) {
    elements[k].style.visibility = old_visibility[k];
  }
  return element;
}

function array_replace_recursive(arr) {
  // +   original by: Brett Zamir (http://brett-zamir.me)
  // *     example 1: array_replace_recursive({'citrus' : ["orange"], 'berries' : ["blackberry", "raspberry"]}, {'citrus' : ['pineapple'], 'berries' : ['blueberry']});
  // *     returns 1: {citrus : ['pineapple'], berries : ['blueberry', 'raspberry']}

  var retObj = {},
    i = 0,
    p = "",
    argl = arguments.length;

  if (argl < 2) {
    throw new Error(
      "There should be at least 2 arguments passed to array_replace_recursive()"
    );
  }

  // Although docs state that the arguments are passed in by reference, it seems they are not altered, but rather the copy that is returned (just guessing), so we make a copy here, instead of acting on arr itself
  for (p in arr) {
    retObj[p] = arr[p];
  }

  for (i = 1; i < argl; i++) {
    for (p in arguments[i]) {
      if (retObj[p] && typeof retObj[p] === "object") {
        retObj[p] = array_replace_recursive(retObj[p], arguments[i][p]);
      } else {
        retObj[p] = arguments[i][p];
      }
    }
  }
  return retObj;
}

function tradezoneNamer(tradezone) {
  tradezone = parseInt(tradezone);
  var output = "Unknown";
  switch (tradezone) {
    case 1: {
      output = "North Western";
      break;
    }
    case 2: {
      output = "Northern";
      break;
    }
    case 3: {
      output = "North Eastern";
      break;
    }
    case 4: {
      output = "Western";
      break;
    }
    case 5: {
      output = "Central";
      break;
    }
    case 6: {
      output = "Eastern";
      break;
    }
    case 7: {
      output = "South Western";
      break;
    }
    case 8: {
      output = "Southern";
      break;
    }
    case 9: {
      output = "South Eastern";
      break;
    }
    case 10: {
      output = "Wastelands";
      break;
    }
    case 21: {
      output = "Outpost";
      break;
    }
    case 22: {
      output = "Valcrest";
      break;
    }
  }
  return output;
}

function tradezoneNamerShort(tradezone) {
  tradezone = parseInt(tradezone);
  var output = "Unknown";
  switch (tradezone) {
    case 1: {
      output = "NW";
      break;
    }
    case 2: {
      output = "North";
      break;
    }
    case 3: {
      output = "NE";
      break;
    }
    case 4: {
      output = "West";
      break;
    }
    case 5: {
      output = "Central";
      break;
    }
    case 6: {
      output = "East";
      break;
    }
    case 7: {
      output = "SW";
      break;
    }
    case 8: {
      output = "South";
      break;
    }
    case 9: {
      output = "SE";
      break;
    }
    case 10: {
      output = "Wastelands";
      break;
    }
    case 21: {
      output = "Outpost";
      break;
    }
    case 22: {
      output = "Valcrest";
      break;
    }
  }
  return output;
}

String.prototype.explode = function (sep, n) {
  var arr = this.split(sep, n);
  if (arr[n - 1] != undefined)
    arr[n - 1] += this.substring(arr.join(" ").length);
  return arr;
};

function startsWithVowel(target) {
  var vowels = "aeiouAEIOU";
  return vowels.indexOf(target[0]) !== -1;
}

var timeSincePageLoaded = 0;
function createMovingTimeString(timeRemaning, precision = 4) {
  if (precision < 1) {
    precision = 1;
  } else if (precision > 4) {
    precision = 4;
  }
  timeRemaning -= timeSincePageLoaded;
  if (timeRemaning <= 0) {
    return "EXPIRED";
  }

  return createTimeString(timeRemaning, precision);
}

function createTimeString(timeRemaning, precision = 4) {
  let dLeft = Math.floor(timeRemaning / 86400);
  let hLeft = Math.floor((timeRemaning % 86400) / 3600);
  let mLeft = Math.floor((timeRemaning % 3600) / 60);
  let sLeft = Math.floor(timeRemaning % 60);

  let outputStr = "";
  if (precision > 0) {
    if (dLeft > 0) {
      outputStr += dLeft + " Day";
      if (dLeft > 1) {
        outputStr += "s";
      }
      outputStr += ", ";
    }
  }
  if (precision > 1) {
    outputStr += hLeft;
  }
  if (precision > 2) {
    outputStr += ":";
    if (mLeft < 10) {
      outputStr += "0";
    }
    outputStr += mLeft;
  }
  if (precision > 3) {
    outputStr += ":";
    if (sLeft < 10) {
      outputStr += "0";
    }
    outputStr += sLeft;
  }

  return outputStr;
}

var pageLoadTime = Math.floor(Date.now() / 1000);
var pc_start = Date.now();
setInterval(function () {
  let pc_delta = Date.now() - pc_start;
  if (
    typeof userVars !== "undefined" &&
    typeof userVars["pagetime"] !== "undefined"
  ) {
    pageLoadTime = parseInt(userVars["pagetime"]);
  }
  timeSincePageLoaded = Math.round(pc_delta / 1000);
  let timers = document.querySelectorAll(".timeKeeper");
  for (let i = 0; i < timers.length; i++) {
    let timer = timers[i];
    let remainingSeconds = parseInt(timer.dataset.endtime) - pageLoadTime;
    timer.textContent = createMovingTimeString(
      remainingSeconds,
      timer.dataset.precision ?? 3
    );
  }
}, 1000);

/**
 * Elements must share a parent and properties for this to be effective
 * @param {HTMLElement} myElem
 * @param {HTMLElement} targetElem
 * @param {String} side
 */
function horizontalCenterOnElem(myElem, targetElem, side = "left") {
  let meBR = myElem.getBoundingClientRect();
  let tepBR = targetElem.parentElement.getBoundingClientRect();
  let teBR = targetElem.getBoundingClientRect();

  if (side === "left") {
    myElem.style[side] =
      Math.floor(teBR[side] - tepBR[side] + teBR.width / 2 - meBR.width / 2) +
      "px";
  } else {
    myElem.style[side] =
      Math.floor(tepBR[side] - teBR[side] + teBR.width / 2 - meBR.width / 2) +
      "px";
  }
}

function updateCashElements(elemClass, amt) {
  let cashElems = document.getElementsByClassName(elemClass);
  for (let elem of cashElems) {
    elem.textContent = amt;
    elem.dataset.cash = amt;
  }
}

function bAnyToBInt(value, radix) {
  let size = 10,
    factor = BigInt(radix ** size),
    i = value.length % size || size,
    parts = [value.slice(0, i)];
  while (i < value.length) parts.push(value.slice(i, (i += size)));

  return parts.reduce((r, v) => r * factor + BigInt(parseInt(v, radix)), 0n);
}
