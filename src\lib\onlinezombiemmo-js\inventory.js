var storageTab = 0,
  currentX = 0,
  currentY = 0,
  active = 0,
  timeStart = 0,
  replaceeQuantity = 0,
  currentQuantity = 0;
var infoBox,
  replacee,
  currentItem,
  startX,
  startY,
  stackable,
  storageBox,
  canMove,
  hoverItem,
  hovering,
  textAddonRef,
  inventoryHolder,
  canComplete = false,
  slotNum,
  curInfoItem,
  fakeGrabbedItem;
(tradeListSize = 0), (dragIteration = 0), (mousePos = [0, 0]);
var blockingItem = "";
var ctxMenuHolder;
var marketLastMoney = false;
var lastItemSold = "";
var collectionBook,
  cbContent = {},
  cbCategory = "",
  cbCategoryType = "";
var itemCategories = [];
var menuOpen = false;
var activeCat = "Everything";
var searchTerm = "";
var allowShifting = true;
var expireTimer = false;
var itemOnItemAction = false;
let inventoryType = "none";
var allowInfoCardOverride = false;
var activeKeys = [];

let inventoryActionButtonHolder;

const implantBonusesTypes = [
  "implant_expBoostMod",
  "implant_pvpPointsBoostMod",
  "implant_damageBoostMod",
  "implant_damageReductionBoostMod",
  "implant_speedBoostMod",
  "implant_ammoLootBoostMod",
  "implant_cashLootBoostMod",
  "implant_armourLootBoostMod",
  "implant_weaponLootBoostMod",
  "implant_miscLootBoostMod",
  "implant_lootSpotBoostMod",
  "implant_searchSpeedBoostMod",
  "implant_dexterityBoostMod",
  "implant_healthBoostMod",
  "implant_mastercraftedLootChanceMod",
  "implant_extraMastercraftedStatsWeapon",
  "implant_extraMastercraftedStatsArmour",
];
const implantNegativeBonuses = ["implant_damageReductionBoostMod"];
const implantIntBonuses = [
  "implant_extraMastercraftedStatsWeapon",
  "implant_extraMastercraftedStatsArmour",
];
const implantBonusText = {
  implant_expBoostMod: "% Exp Gain",
  implant_pvpPointsBoostMod: "% Pvp Points Gain",
  implant_damageBoostMod: "% Damage Inflicted",
  implant_damageReductionBoostMod: "% Incoming Damage Reduction",
  implant_speedBoostMod: "% Movement Speed",
  implant_ammoLootBoostMod: "% Ammo Looted",
  implant_cashLootBoostMod: "% Cash Looted",
  implant_armourLootBoostMod: "% Chance to Find Armour",
  implant_weaponLootBoostMod: "% Chance to Find Weapons",
  implant_miscLootBoostMod: "% Chance to Find Misc / Blueprints",
  implant_lootSpotBoostMod: "% More Loot Spots",
  implant_searchSpeedBoostMod: "% Search Speed",
  implant_dexterityBoostMod: "% Attack Speed",
  implant_healthBoostMod: " Maximum Health",
  implant_mastercraftedLootChanceMod: "% Mastercrafted Loot Chance",
  implant_extraMastercraftedStatsWeapon: " Weapon Stats on Mastercraft Loot",
  implant_extraMastercraftedStatsArmour: " Armour Stats on Mastercraft Loot",
};

let qualityTranslation = {
  0: "Broken",
  2: "Uncommon",
  3: "Rare",
  4: "Epic",
  5: "Elite",
  6: "Legendary",
  7: "Dusk",
};
let qualityColors = {
  0: "ff0000",
  2: "00ff00",
  3: "0000ff",
  4: "ff00ff",
  5: "ffff00",
  6: "ffd700",
  7: "eae0c8",
};

var lockedSlots = [];

if (typeof useVars === "undefined") {
  var userVars = [];
}
if (typeof hrV === "undefined") {
  var hrV = 0;
}
if (typeof nf === "undefined") {
  var nf = new Intl.NumberFormat();
}

var hoverEvent = document.createEvent("Event");
hoverEvent.initEvent("itemhover", true, true);

function mouseTracker(e) {
  if (e.type === "touchmove") {
    mousePos[0] = e.touches[0].clientX;
    mousePos[1] = e.touches[0].clientY;
  } else {
    mousePos[0] = e.clientX;
    mousePos[1] = e.clientY;
  }
}

function populateStorage() {
  if (checkLSBool("general", "simpleMenus")) {
    let targetContainer = document.getElementById("speedContainer");
    while (targetContainer.lastChild) {
      targetContainer.removeChild(targetContainer.lastChild);
    }
    populateStorageAlt();
  } else {
    let targets = document
      .getElementById("storage")
      .querySelectorAll(".validSlot");
    for (let validSlot of targets) {
      while (validSlot.lastChild) {
        validSlot.removeChild(validSlot.lastChild);
      }
    }
    for (let value of document
      .getElementById("storage")
      .querySelectorAll("div")) {
      if (
        parseInt(value.dataset.slot) <=
        parseInt(userVars["DFSTATS_df_storage_slots"])
      ) {
        if (!value.classList.contains("validSlot")) {
          value.classList.add("validSlot");
        }
      } else {
        value.classList.remove("validSlot");
      }
    }
    for (var i = storageTab * 40 + 1; i <= (storageTab + 1) * 40; i++) {
      let targetSlot = document
        .getElementById("storage")
        .querySelector("div[data-slot='" + i + "'].validSlot");
      if (targetSlot !== null) {
        if (lockedSlots.includes(i + 40 + "")) {
          targetSlot.classList.add("locked");
        } else {
          targetSlot.classList.remove("locked");
        }
        if (typeof storageBox["df_store" + i + "_type"] !== "undefined") {
          setSlotData(
            storageBox["df_store" + i + "_type"],
            storageBox["df_store" + i + "_quantity"],
            targetSlot
          );
        }
      }
    }

    if (!checkLSBool("general", "simpleMenus")) {
      let allowInvToStorage = false;
      let allowStorageToInv = false;
      for (let i = 1; i <= userVars["DFSTATS_df_invslots"]; i++) {
        if (!lockedSlots.includes("" + i)) {
          if (userVars[`DFSTATS_df_inv${i}_type`].length > 0) {
            allowInvToStorage = true;
          } else {
            allowStorageToInv = true;
          }
        }
        if (allowInvToStorage && allowStorageToInv) {
          break;
        }
      }
      let storageHasRoom = false;
      let thingsInStorage = false;
      for (
        var i = storageTab * 40 + 1;
        i <= userVars["DFSTATS_df_storage_slots"];
        i++
      ) {
        if (typeof storageBox["df_store" + i + "_type"] === "undefined") {
          storageHasRoom = true;
        } else if (storageBox["df_store" + i + "_type"].length > 0) {
          thingsInStorage = true;
        }
        if (storageHasRoom && thingsInStorage) {
          break;
        }
      }
      if (allowInvToStorage && storageHasRoom) {
        document.getElementById("invtostorage").disabled = false;
      } else {
        if (storageHasRoom === false) {
          document.getElementById("invtostorage").dataset.pmoverride =
            "storage";
        }
        document.getElementById("invtostorage").disabled = true;
      }
      if (allowStorageToInv && thingsInStorage) {
        document.getElementById("storagetoinv").disabled = false;
      } else {
        if (allowStorageToInv === false) {
          document.getElementById("storagetoinv").dataset.pmoverride =
            "inventory";
        }
        document.getElementById("storagetoinv").disabled = true;
      }
    }
  }
}

var validItems = 0;
function populateStorageAlt() {
  validItems = 0;
  var currentlyHiddenItems = 0;
  for (var i = 0; i <= parseInt(userVars["DFSTATS_df_storage_slots"]); i++) {
    if (typeof storageBox["df_store" + i + "_type"] !== "undefined") {
      validItems++;

      var item = document.createElement("div");
      item.classList.add("fakeItem");
      item.dataset.type = storageBox["df_store" + i + "_type"];
      item.dataset.quantity = storageBox["df_store" + i + "_quantity"];
      item.dataset.slot = i;

      var itemData = storageBox["df_store" + i + "_type"];
      var itemType = itemData.split("_")[0];
      var itemCat = getItemType(globalData[itemType]);

      var addToName = " " + calcMCTag(itemData, false, "span", "");
      var storageQuanity = parseInt(storageBox["df_store" + i + "_quantity"]);

      if (
        (storageQuanity !== 0 &&
          storageQuanity !== 1 &&
          storageBox["df_store" + i + "_quantity"] !== "" &&
          storageBox["df_store" + i + "_quantity"] !== " ") ||
        itemCat === "armour" ||
        itemCat === "ammo"
      ) {
        var dataString = "<span";

        if (itemCat !== "ammo") {
          dataString +=
            " style='color: #" +
            damageColor(
              storageBox["df_store" + i + "_quantity"],
              globalData[itemType]["hp"]
            ) +
            ";'";
        }

        dataString += "> (";

        if (itemData["quantity"] !== "") {
          dataString += storageQuanity;
        } else {
          dataString += "0";
        }

        dataString += ")</span>";

        addToName += dataString;
      }

      var itemName = globalData[itemType]["name"];
      var itemColour = "";
      for (var x in itemData.split("_")) {
        if (itemData.split("_")[x].indexOf("colour") >= 0) {
          itemColour = itemData.split("_")[x].substring(6) + " ";
        }
        if (itemData.split("_")[x].indexOf("name") >= 0) {
          itemName = itemData.split("_")[x].substring(4);
        }
      }

      itemName = itemColour + itemName;

      if (
        itemName.length + addToName.replace(/(<([^>]+)>)/gi, "").length >
        46
      ) {
        if (addToName !== "") {
          item.dataset.originalLength = addToName.replace(
            /(<([^>]+)>)/gi,
            ""
          ).length;
          itemName =
            itemName.substr(
              0,
              43 - addToName.replace(/(<([^>]+)>)/gi, "").length
            ) + "...";
        } else {
          itemName = itemName.substr(0, 43) + "...";
        }
      }

      item.innerHTML =
        "<div class='itemName cashhack credits' data-cash='" +
        itemName +
        "'>" +
        itemName +
        "</div>";
      if (addToName !== "") {
        item.innerHTML += addToName;
      }

      var buttonData = document.createElement("button");
      buttonData.textContent = "take";
      buttonData.dataset.action = "takeFromStorage";
      buttonData.dataset.type = storageBox["df_store" + i + "_type"];
      if (findFirstEmptyGenericSlot("inv")) {
        buttonData.addEventListener("click", inventoryAction);
      } else {
        buttonData.disabled = true;
      }

      item.appendChild(buttonData);

      if (document.getElementById("storageSearchBox").value !== "") {
        if (
          !searchPassConditions(
            document.getElementById("storageSearchBox").value,
            itemData,
            globalData[itemType]["name"]
          )
        ) {
          currentlyHiddenItems++;
          item.style.display = "none";
        }
      }

      document.getElementById("speedContainer").appendChild(item);
    }
  }

  var totalStorageSlotsDisplay = document.getElementById("remainingSlots");
  if (totalStorageSlotsDisplay === null) {
    totalStorageSlotsDisplay = document.createElement("div");
    totalStorageSlotsDisplay.id = "remainingSlots";
  }
  totalStorageSlotsDisplay.textContent =
    "Slots: " + validItems + " / " + userVars["DFSTATS_df_storage_slots"];

  var itemArr = Array.prototype.slice.call(
    document.getElementById("speedContainer").querySelectorAll(".fakeItem"),
    0
  );
  itemArr = itemListSorter(itemArr);

  for (var item of itemArr) {
    document.getElementById("speedContainer").appendChild(item);
  }

  var noItemMessage = document.createElement("div");
  noItemMessage.classList.add("profitList");
  if (validItems > 0 && validItems !== currentlyHiddenItems) {
    noItemMessage.style.display = "none";
  }
  noItemMessage.style.textAlign = "center";
  noItemMessage.innerHTML += "No items found";
  document.getElementById("speedContainer").appendChild(noItemMessage);

  inventoryHolder.appendChild(totalStorageSlotsDisplay);
}

var itemTypeOrder = ["armour", "weapon"];

function itemListSorter(itemList) {
  if (
    checkPropertyValid("inventory", "sortstyle") &&
    parseInt(userSettings["inventory"]["sortstyle"]) !== 0
  ) {
    var subItemLists = Array.apply(null, Array(itemTypeOrder.length + 1)).map(
      function (x, i) {
        return [];
      }
    );
    for (var i = 0; i < itemList.length; i++) {
      var itemData = globalData[itemList[i].dataset.type.split("_")[0]];
      var sorted = false;

      for (var j = 0; j < itemTypeOrder.length; j++) {
        if (itemTypeOrder[j] === itemData["itemtype"]) {
          subItemLists[j].push(itemList[i]);
          sorted = true;
          break;
        }
      }
      if (!sorted) {
        subItemLists[subItemLists.length - 1].push(itemList[i]);
      }
    }

    itemList = [];

    for (var i = 0; i < subItemLists.length; i++) {
      subItemLists[i].sort(itemAlphabeticalSort);
      itemList = itemList.concat(subItemLists[i]);
    }
  } else {
    itemList.sort(itemAlphabeticalSort);
  }

  return itemList;
}

function itemAlphabeticalSort(elemA, elemB) {
  var aType = elemA.dataset.type;
  var bType = elemB.dataset.type;

  if (checkLSBool("inventory", "sortbyscrap")) {
    var aScrap = scrapValue(aType, elemA.dataset.quantity);
    var bScrap = scrapValue(bType, elemB.dataset.quantity);

    if (aScrap < bScrap) {
      return 1;
    } else if (bScrap < aScrap) {
      return -1;
    }
  }

  for (var i = 0; i < aType.length; i++) {
    if (aType.charCodeAt(i) && bType.charCodeAt(i)) {
      if (aType.charCodeAt(i) < bType.charCodeAt(i)) {
        return -1;
      } else if (aType.charCodeAt(i) > bType.charCodeAt(i)) {
        return 1;
      }
    } else {
      break;
    }
  }
  if (typeof elemA.dataset.quantity !== "undefined") {
    if (typeof elemB.dataset.quantity !== "undefined") {
      var aQuantity = parseInt(elemA.dataset.quantity);
      var bQuantity = parseInt(elemB.dataset.quantity);
      if (aQuantity < bQuantity) {
        return -1;
      } else if (aQuantity > bQuantity) {
        return 1;
      }
    } else {
      return 1;
    }
  }

  return 0;
}

function setSlotData(itemType, quantity, appendTo, fakeItem) {
  if (typeof fakeItem === "undefined") {
    fakeItem = false;
  }
  var itemData = {
    type: itemType.trim().split("_"),
    quantity: quantity.trim(),
  };
  var item = document.createElement("div");
  let IItem = new InventoryItem(itemType.trim());
  if (fakeItem) {
    item.setAttribute("class", "fakeItem");
  } else {
    item.setAttribute("class", "item");
  }
  if (IItem.cooked) {
    item.classList.add("nonstack");
    item.classList.add("cooked");
  }
  if (typeof IItem.stats !== "undefined") {
    // blame 2127916 for this change
    if (typeof mcData[IItem.stats] !== "undefined") {
      item.style.border = "1px solid " + mcData[IItem.stats][0];
    }
  } else {
    item.style.border = "";
  }
  if (typeof IItem.q !== "undefined") {
    item.dataset.quality = IItem.q;
    if (typeof qualityColors[IItem.q] !== "undefined") {
      item.style.backgroundColor = "#" + qualityColors[IItem.q] + "20";
    }
  } else {
    item.dataset.quality = "1";
  }
  if (typeof globalData[itemData["type"][0]] === "undefined") {
    itemData["type"][0] = "brokenitem";
    item.dataset.type = "brokenitem";
    item.dataset.broken = itemType;
  } else {
    itemData["image"] = itemData["type"][0];
    if (
      typeof globalData[itemData["type"][0]]["image_override"] !== "undefined"
    ) {
      item.style.backgroundImage =
        "url('https://files.deadfrontier.com/deadfrontier/inventoryimages/large/" +
        globalData[itemData["type"][0]]["image_override"] +
        ".png')";
    } else if (
      globalData[itemData["type"][0]]["itemtype"] === "armour" ||
      (globalData[itemData["type"][0]]["itemtype"] === "item" &&
        typeof globalData[itemData["type"][0]]["clothingtype"] !==
          "undefined" &&
        globalData[itemData["type"][0]]["clothingtype"].length > 0)
    ) {
      item.style.backgroundImage =
        "url('https://files.deadfrontier.com/deadfrontier/inventoryimages/large/" +
        itemData["image"] +
        ".png')";
    } else {
      item.style.backgroundImage =
        "url('https://files.deadfrontier.com/deadfrontier/inventoryimages/large/" +
        pickItemImageSubStr(itemType.trim()) +
        ".png')";
    }
    item.dataset.type = itemType;
  }

  item.dataset.itemtype = getItemType(globalData[itemData["type"][0]]);
  if (
    (itemData["quantity"] !== "0" &&
      itemData["quantity"] !== "1" &&
      itemData["quantity"] !== "") ||
    item.dataset.itemtype === "armour" ||
    item.dataset.itemtype === "ammo"
  ) {
    if (itemData["quantity"] !== "") {
      item.dataset.quantity = itemData["quantity"];
    } else {
      item.dataset.quantity = 0;
    }
    if (item.dataset.itemtype !== "ammo") {
      item.classList.add("nonstack");
      item.style.color =
        "#" +
        damageColor(
          itemData["quantity"],
          globalData[itemData["type"][0]]["hp"]
        );
    }
  }

  /*if(document.getElementById("storage"))
	{
		item.onmouseover = shiftHover;
		item.onmousemove = shiftHover;
		item.onkeypress = shiftHover;
		item.onmouseout = shiftUnhover;
		
	}*/

  appendTo.appendChild(item);
  //appendTo.append(item);
  return item;
}

function getUpgradePrice() {
  var price = 0;
  price = parseInt(userVars["DFSTATS_df_storage_slots"]) * 5000;
  if (price > 500000) {
    price = 500000;
  }
  if (parseInt(userVars["DFSTATS_df_storage_slots"]) >= 400) {
    price = parseInt(userVars["DFSTATS_df_storage_slots"]) * 5000;
  }
  return price;
}

var mcData = {};
mcData["888"] = ["#897129", "God", "G"];
mcData["777"] = ["#c0c0c0", "Angel", "A"];
mcData["666"] = ["#AA0000", "Hell", "H"];

mcData["788"] = ["", "Near God", "NG"];
mcData["878"] = ["", "Near God", "NG"];
mcData["887"] = ["", "Near God", "NG"];

mcData["2424"] = ["#897129", "God", "G"];

mcData["2423"] = ["", "Near God", "NG"];
mcData["2324"] = ["", "Near God", "NG"];

mcData["3"] = ["#897129", "God", "G"];
mcData["2"] = ["", "Near God", "NG"];

function calcMCTag(itemData, fullText, nodeType, cssClass) {
  var nameOutput = "";
  if (itemData.indexOf("_stats") >= 0) {
    var n = itemData.indexOf("_");
    var itemType = getItemType(globalData[itemData.substring(0, n).trim()]);
    n = itemData.indexOf("_stats") + 6;
    if (itemType === "armour") {
      n = itemData.substring(n, n + 4);
    } else if (itemType === "weapon") {
      n = itemData.substring(n, n + 3);
    } else if (itemType === "backpack") {
      n = itemData.substring(n, n + 1);
    }

    if (n !== "0" && n !== "000" && n !== "0000") {
      if (mcData[n]) {
        nameOutput += "<" + nodeType;
        if (cssClass && cssClass !== "") {
          nameOutput += " class='" + cssClass + "'";
        }
        if (mcData[n][0] !== "") {
          nameOutput += " style='color: " + mcData[n][0] + ";'";
        }
        nameOutput += ">";
        if (fullText) {
          nameOutput += "[" + mcData[n][1] + " Craft]";
        } else {
          nameOutput += "(" + mcData[n][2] + "C)";
        }
        nameOutput += "</" + nodeType + ">";
      } else {
        if (!fullText) {
          nameOutput += "<" + nodeType;
          if (cssClass && cssClass !== "") {
            nameOutput += " class='" + cssClass + "'";
          }
          nameOutput += ">(MC)</" + nodeType + ">";
        }
      }
    }
  }
  return nameOutput;
}

function findFirstEmptyStorageSlot() {
  var maxStoreLocation = parseInt(userVars["DFSTATS_df_storage_slots"]);
  if (storageTab * 40 + 40 < parseInt(maxStoreLocation)) {
    maxStoreLocation = storageTab * 40 + 40;
  }
  for (
    var i = storageTab * 40 + 1;
    i <= userVars["DFSTATS_df_storage_slots"];
    i++
  ) {
    if (typeof storageBox["df_store" + i + "_type"] === "undefined") {
      return i;
    }
  }
  for (var i = 1; i <= userVars["DFSTATS_df_storage_slots"]; i++) {
    if (typeof storageBox["df_store" + i + "_type"] === "undefined") {
      return i;
    }
  }
  return false;
}

function findFirstEmptyClanStorageSlot() {
  var maxStoreLocation = parseInt(userVars["DFSTATS_df_storage_slots"]);
  if (storageTab * 40 + 40 < parseInt(maxStoreLocation)) {
    maxStoreLocation = storageTab * 40 + 40;
  }
  for (
    var i = storageTab * 40 + 1;
    i <= userVars["DFSTATS_df_storage_slots"];
    i++
  ) {
    let column = Math.floor((i - 1) / 5);
    if (
      (typeof userVars[`CLANSTORAGE_columns_${column}_access`] !==
        "undefined" &&
        userVars[`CLANSTORAGE_columns_${column}_access`] === "0") ||
      (typeof userVars[
        `CLANMOD_storage_defaults_rank${userVars["CLAN_rank"]}_access`
      ] !== "undefined" &&
        userVars[
          `CLANMOD_storage_defaults_rank${userVars["CLAN_rank"]}_access`
        ] === "0")
    ) {
      continue;
    }
    if (typeof userVars[`CLANSTORAGE_${i}_item`] === "undefined") {
      return i;
    }
  }
  for (var i = 1; i <= userVars["DFSTATS_df_storage_slots"]; i++) {
    let column = Math.floor((i - 1) / 5);
    if (
      (typeof userVars[`CLANSTORAGE_columns_${column}_access`] !==
        "undefined" &&
        userVars[`CLANSTORAGE_columns_${column}_access`] === "0") ||
      (typeof userVars[
        `CLANMOD_storage_defaults_rank${userVars["CLAN_rank"]}_access`
      ] !== "undefined" &&
        userVars[
          `CLANMOD_storage_defaults_rank${userVars["CLAN_rank"]}_access`
        ] === "0")
    ) {
      continue;
    }
    if (typeof userVars[`CLANSTORAGE_${i}_item`] === "undefined") {
      return i;
    }
  }
  return false;
}

function findLastEmptyStorageSlot() {
  for (var i = userVars["DFSTATS_df_storage_slots"]; i >= 1; i--) {
    if (typeof storageBox["df_store" + i + "_type"] === "undefined") {
      return i;
    }
  }
  return false;
}

function findFirstEmptyGenericSlot(slotType) {
  for (var i = 1; i <= userVars["DFSTATS_df_" + slotType + "slots"]; i++) {
    if (userVars["DFSTATS_df_" + slotType + i + "_type"] === "") {
      return i;
    }
  }
  return false;
}

function findLastEmptyGenericSlot(slotType) {
  for (var i = userVars["DFSTATS_df_" + slotType + "slots"]; i >= 1; i--) {
    if (userVars["DFSTATS_df_" + slotType + i + "_type"] === "") {
      return i;
    }
  }
  return false;
}

function shiftItem(elem) {
  var itemData = [
    parseInt(elem.parentNode.dataset.slot),
    elem.dataset.type,
    elem.parentNode.parentNode.parentNode.id,
  ];
  var extraData = [itemData];
  if (document.getElementById("storage")) {
    if (elem.parentNode.parentNode.parentNode.id === "storage") {
      extraData[1] = [findFirstEmptyGenericSlot("inv"), "", "inventory"];
    } else {
      if (inventoryType === "clan_storage") {
        extraData[1] = [findFirstEmptyClanStorageSlot(), "", "storage"];
      } else {
        extraData[1] = [findFirstEmptyStorageSlot(), "", "storage"];
      }
    }
  } else if (
    document.getElementById("implants") &&
    elem.dataset.itemtype &&
    elem.dataset.itemtype === "implant"
  ) {
    if (elem.parentNode.parentNode.parentNode.id === "implants") {
      extraData[1] = [findFirstEmptyGenericSlot("inv"), "", "inventory"];
    } else {
      extraData[1] = [findFirstEmptyGenericSlot("implant"), "", "implants"];
    }
  } else if (document.getElementById("character")) {
    if (elem.dataset.itemtype === "weapon") {
      if (elem.parentNode.dataset.slottype === "weapon") {
        extraData[1] = [findFirstEmptyGenericSlot("inv"), "", "inventory"];
      } else {
        var emptyWeaponSlot = 0;
        for (var i = 1; i <= 3; i++) {
          if (userVars["DFSTATS_df_weapon" + i + "type"] === "") {
            emptyWeaponSlot = i + 30;
            break;
          }
        }
        extraData[1] = [emptyWeaponSlot, "", "character"];
      }
    } else if (elem.dataset.itemtype === "armour") {
      if (elem.parentNode.dataset.slottype === "armour") {
        extraData[1] = [findFirstEmptyGenericSlot("inv"), "", "inventory"];
      } else {
        var armourType = "";
        var armourElem = document
          .getElementById("character")
          .querySelector(".validSlot[data-slot='34']");
        if (armourElem.hasChildNodes()) {
          armourType = armourElem.childNodes[0].dataset.type;
        }
        extraData[1] = [34, armourType, "character"];
      }
    } else {
      if (elem.parentNode.parentNode.parentNode.id === "character") {
        extraData[1] = [findFirstEmptyGenericSlot("inv"), "", "inventory"];
      } else {
        if (unblockedSlot(elem.dataset.type.split("_")[0].trim())) {
          var slotType = "";
          var slotElem = document
            .getElementById("character")
            .querySelector(
              ".validSlot[data-slottype='" + elem.dataset.itemtype + "']"
            );
          if (slotElem.hasChildNodes()) {
            slotType = slotElem.childNodes[0].dataset.type;
          }
          extraData[1] = [slotElem.dataset.slot, slotType, "character"];
        }
      }
    }
  }
  updateInventory(extraData);
}

/**
 *
 * @param {Element} elem
 * @param {InventoryItem} iItem
 */
function splitItem(elem, iItem) {
  promptLoading("Thinking...");

  let targetSlot = parseInt(elem.parentNode.dataset.slot);
  let canSplitStack = false;

  switch (elem.parentNode.parentNode.parentNode.id) {
    case "inventory":
      if (findFirstEmptyGenericSlot("inv") === false) {
        promptLoading("No free inventory slots...");
        setTimeout(function () {
          promptEnd();
        }, 1000);
      } else {
        canSplitStack = true;
      }
      break;
    case "backpackdisplay":
      if (findFirstEmptyBackpackSlot() === false) {
        promptLoading("No free backpack slots...");
        setTimeout(function () {
          promptEnd();
        }, 1000);
      } else {
        canSplitStack = true;
      }
      break;
  }

  if (canSplitStack === true) {
    let quantitySelect = document.createElement("input");
    let submitBtn = document.createElement("button");
    let cancelBtn = document.createElement("button");
    /**
     *
     * @param {KeyboardEvent} evt
     * @returns
     */
    let keydownValidator = function (evt) {
      let target = evt.currentTarget;
      if (evt.key === "Enter") {
        let val = parseInt(target.value) || 0;
        if (val > 0 && val <= target.max) {
          submitBtn.click();
          target.value = "";
        }
        evt.preventDefault();
        return;
      }
      if (isNaN(evt.key)) {
        if (evt.key.length === 1 && !evt.ctrlKey) {
          evt.preventDefault();
        }
        return;
      }
    };
    /**
     *
     * @param {InputEvent} evt
     */
    let inputValidator = function (evt) {
      let target = evt.currentTarget;
      let val = parseInt(target.value) || 0;
      if (val > target.max) {
        target.value = target.max;
      } else if (val < target.min) {
        target.value = target.min;
      } else {
        target.value = val;
      }
      if (val == 0) {
        submitBtn.disabled = true;
      } else {
        submitBtn.disabled = false;
      }
    };

    quantitySelect.type = "number";
    quantitySelect.style.display = "block";
    quantitySelect.style.marginTop = "10px";
    quantitySelect.style.marginLeft = "auto";
    quantitySelect.style.marginRight = "auto";
    quantitySelect.value = 1;
    quantitySelect.max = parseInt(elem.dataset.quantity) - 1;
    quantitySelect.min = 0;
    quantitySelect.addEventListener("keydown", keydownValidator);
    quantitySelect.addEventListener("input", inputValidator);

    submitBtn.textContent = "Submit";
    submitBtn.style.position = "absolute";
    submitBtn.style.left = "30px";
    submitBtn.style.bottom = "5px";
    submitBtn.addEventListener("click", function (e) {
      if (elem.parentNode.parentNode.parentNode.id === "backpackdisplay") {
        targetSlot += 1050;
      }
      let dataArr = {
        pagetime: userVars["pagetime"],
        templateID: userVars["template_ID"],
        sc: userVars["sc"],
        creditsnum: userVars["DFSTATS_df_credits"],
        buynum: 0,
        renameto: "undefined`undefined",
        expected_itemprice: quantitySelect.value,
        expected_itemtype2: "",
        expected_itemtype: iItem.toString(),
        itemnum2: 0,
        itemnum: targetSlot,
        price: 0,
        gv: 21,
        userID: userVars["userID"],
        password: userVars["password"],
        action: "splitstack",
      };

      promptLoading("Splitting...");
      webCall(
        "inventory_new",
        dataArr,
        function (data) {
          updateIntoArr(flshToArr(data, "DFSTATS_"), userVars);
          populateInventory();
          if (document.getElementById("backpackdisplay")) {
            populateBackpack();
          }
          updateAllFields();
        },
        true
      );
    });

    cancelBtn.textContent = "Cancel";
    cancelBtn.style.position = "absolute";
    cancelBtn.style.right = "30px";
    cancelBtn.style.bottom = "5px";
    cancelBtn.addEventListener("click", function (e) {
      promptEnd();
    });

    df_prompt.innerHTML =
      'Please enter a quantity to split from <span style="color: #12FF00;">' +
      (typeof iItem.name !== "undefined"
        ? iItem.name
        : globalData[iItem.type]["name"]) +
      '</span> at slot <span style="color: #990000;">' +
      targetSlot +
      "</span>,";
    df_prompt.appendChild(document.createElement("br"));
    df_prompt.appendChild(quantitySelect);
    df_prompt.appendChild(submitBtn);
    df_prompt.appendChild(cancelBtn);
  }
}

function updateInventory(itemSlots) {
  df_prompt.innerHTML = "<div style='text-align: center;'>Loading...</div>";
  df_prompt.parentNode.style.display = "block";
  if (itemSlots[0][2] === "storage") {
    itemSlots[0][0] += 40;
  } else if (itemSlots[0][2] === "implants") {
    itemSlots[0][0] += 1000;
  } else if (itemSlots[0][2] === "backpackdisplay") {
    itemSlots[0][0] += 1050;
  }
  if (itemSlots[1][2] === "storage") {
    itemSlots[1][0] += 40;
  } else if (itemSlots[1][2] === "implants") {
    itemSlots[1][0] += 1000;
  } else if (itemSlots[1][2] === "backpackdisplay") {
    itemSlots[1][0] += 1050;
  }
  var dataArr = {};
  dataArr["pagetime"] = userVars["pagetime"];
  dataArr["templateID"] = userVars["template_ID"];
  dataArr["sc"] = userVars["sc"];
  dataArr["creditsnum"] = userVars["DFSTATS_df_credits"];
  dataArr["buynum"] = "0";
  dataArr["renameto"] = "undefined`undefined";
  dataArr["expected_itemprice"] = "-1";
  dataArr["expected_itemtype2"] = itemSlots[1][1];
  dataArr["expected_itemtype"] = itemSlots[0][1];
  dataArr["itemnum2"] = itemSlots[1][0];
  dataArr["itemnum"] = itemSlots[0][0];
  dataArr["price"] = getUpgradePrice();
  dataArr["gv"] = 21;
  dataArr["userID"] = userVars["userID"];
  dataArr["password"] = userVars["password"];

  if (itemOnItemAction === "repair") {
    if (itemSlots[1][2] === "backpackdisplay") {
      playSound("eat"); // I did this because it was funny - hotrods20
    } else {
      playSound("repair");
    }
    if (itemSlots[1][2] === "character") {
      dataArr["action"] = "userepairkit";
    } else {
      dataArr["action"] = "targetrepairkit";
    }
    webCall(
      "inventory_new",
      dataArr,
      function (data) {
        updateIntoArr(flshToArr(data, "DFSTATS_"), userVars);
        populateInventory();
        if (document.getElementById("sellitems") && sellingData) {
          listSelling(sellingData);
        } else if (
          document.getElementById("privateIncoming") &&
          document.getElementById("privateOutgoing") &&
          privateData
        ) {
          listPrivate(privateData);
        } else if (document.getElementById("itemDisplay") && marketData) {
          listMarket(marketData);
        }
        if (document.getElementById("backpackdisplay")) {
          populateBackpack();
        }
        updateAllFields();
      },
      true
    );
  } else if (
    (itemSlots[0][2] === "backpackdisplay" ||
      itemSlots[1][2] === "backpackdisplay") &&
    itemSlots[1][2] !== "discard"
  ) {
    playSound("swap");
    dataArr["action"] = "backpack";
    webCall(
      "hotrods/backpack",
      dataArr,
      function (data) {
        updateIntoArr(flshToArr(data, "DFSTATS_"), userVars);
        populateInventory();
        populateCharacterInventory();
        if (itemSlots[0][2] === "implants" || itemSlots[1][2] === "implants") {
          populateImplants();
        }
        updateAllFields();
        renderAvatarUpdate();
      },
      true
    );
  } else if (
    inventoryType === "clan_storage" &&
    (itemSlots[0][2] === "storage" || itemSlots[1][2] === "storage")
  ) {
    playSound("swap");
    dataArr["action"] = "move";
    webCall("hotrods/clanaction", dataArr, reloadStorageData, true);
  } else if (itemSlots[0][2] === "inventory" && itemSlots[1][2] === "storage") {
    playSound("swap");
    dataArr["action"] = "store";
    webCall("inventory_new", dataArr, reloadStorageData, true);
  } else if (itemSlots[0][2] === "storage" && itemSlots[1][2] === "storage") {
    var notAmmo = true;
    if (
      typeof globalData[itemSlots[0][1]] !== "undefined" &&
      globalData[itemSlots[0][1]]["itemtype"] === "ammo"
    ) {
      notAmmo = false;
    }
    if (
      itemSlots[1][1] === itemSlots[0][1] &&
      storageBox["df_store" + (itemSlots[1][0] - 40) + "_quantity"] ===
        storageBox["df_store" + (itemSlots[0][0] - 40) + "_quantity"] &&
      notAmmo
    ) {
      df_prompt.parentNode.style.display = "none";
      df_prompt.innerHTML = "";
      pageLock = false;
      return;
    }
    playSound("swap");
    dataArr["action"] = "swapstorage";
    webCall("inventory_new", dataArr, reloadStorageData, true);
  } else if (
    (itemSlots[0][2] === "inventory" && itemSlots[1][2] === "inventory") ||
    (itemSlots[0][2] === "implants" && itemSlots[1][2] !== "discard") ||
    itemSlots[1][2] === "implants"
  ) {
    playSound("swap");
    dataArr["action"] = "newswap";
    webCall(
      "inventory_new",
      dataArr,
      function (data) {
        updateIntoArr(flshToArr(data, "DFSTATS_"), userVars);
        populateInventory();
        if (itemSlots[0][2] === "implants" || itemSlots[1][2] === "implants") {
          populateImplants();
        }
        if (document.getElementById("sellitems") && sellingData) {
          listSelling(sellingData);
        } else if (
          document.getElementById("privateIncoming") &&
          document.getElementById("privateOutgoing") &&
          privateData
        ) {
          listPrivate(privateData);
        } else if (document.getElementById("itemDisplay") && marketData) {
          listMarket(marketData);
        }
        updateAllFields();
      },
      true
    );
  } else if (itemSlots[0][2] === "storage" && itemSlots[1][2] === "inventory") {
    playSound("swap");
    dataArr["action"] = "take";
    webCall("inventory_new", dataArr, reloadStorageData, true);
  } else if (itemSlots[1][2] === "discard") {
    dataArr["action"] = "newdiscard";
    webCall(
      "inventory_new",
      dataArr,
      function (data) {
        updateIntoArr(flshToArr(data, "DFSTATS_"), userVars);
        populateInventory();
        if (itemSlots[0][2] === "implants") {
          populateImplants();
        }
        populateCharacterInventory();
        updateAllFields();
        renderAvatarUpdate();
      },
      true
    );
  } else if (
    itemSlots[0][2] === "character" ||
    itemSlots[1][2] === "character"
  ) {
    playSound("equip");
    dataArr["action"] = "newequip";
    if (itemSlots[0][2] !== itemSlots[1][2]) {
      if (itemSlots[0][2] === "character") {
        dataArr["expected_itemtype2"] = itemSlots[0][1];
        dataArr["expected_itemtype"] = itemSlots[1][1];
        dataArr["itemnum2"] = itemSlots[0][0];
        dataArr["itemnum"] = itemSlots[1][0];
      }
    }
    webCall(
      "inventory_new",
      dataArr,
      function (data) {
        updateIntoArr(flshToArr(data, "DFSTATS_"), userVars);
        if (
          typeof userVars["DFSTATS_df_backpack"] === "undefined" ||
          userVars["DFSTATS_df_backpack"].length === 0
        ) {
          if (document.getElementById("backpackMenu")) {
            document.getElementById("backpackMenu").style.display = "none";
          }
        } else {
          if (document.getElementById("backpackMenu")) {
            document.getElementById("backpackMenu").style.display = "";
          }
        }
        populateInventory();
        populateCharacterInventory();
        updateAllFields();
        renderAvatarUpdate();
      },
      true
    );
  } else if (itemSlots[0][2] === "" && itemSlots[1][2] === "") {
    playSound("swap");
    dataArr["action"] = "newswap";
    webCall(
      "inventory_new",
      dataArr,
      function (data) {
        updateIntoArr(flshToArr(data, "DFSTATS_"), userVars);
        populateInventory();
        if (document.getElementById("sellitems") && sellingData) {
          listSelling(sellingData);
        } else if (
          document.getElementById("privateIncoming") &&
          document.getElementById("privateOutgoing") &&
          privateData
        ) {
          listPrivate(privateData);
        } else if (document.getElementById("itemDisplay") && marketData) {
          listMarket(marketData);
        }
        updateAllFields();
      },
      true
    );
  }
}

function reloadStorageData(invData) {
  var oldStorageSlotCount = parseInt(userVars["DFSTATS_df_storage_slots"]);
  updateIntoArr(flshToArr(invData, "DFSTATS_"), userVars);
  if (
    document.getElementById("buyStorageSlots") &&
    document.getElementById("buyStorageSlots").querySelector("span")
  ) {
    document
      .getElementById("buyStorageSlots")
      .querySelector("span").textContent =
      "Price: $" + nf.format(getUpgradePrice());
  }
  populateInventory();
  var dataArr = {};
  dataArr["pagetime"] = userVars["pagetime"];
  dataArr["sc"] = userVars["sc"];
  dataArr["userID"] = userVars["userID"];
  dataArr["password"] = userVars["password"];

  if (checkLSBool("general", "simpleMenus")) {
    userVars["DFSTATS_df_storage_slots"] = parseInt(
      userVars["DFSTATS_df_storage_slots"]
    );
    if (
      oldStorageSlotCount !== userVars["DFSTATS_df_storage_slots"] &&
      oldStorageSlotCount % 40 === 0
    ) {
      forwardButton = document.getElementById("storageForward").style.display =
        "block";
      if (typeof slotNum !== "undefined") {
        slotNum.textContent = "(";

        var slotJumper = document.createElement("input");
        slotJumper.placeholder = storageTab + 1;
        slotJumper.type = "number";
        slotJumper.style.width = "16px";
        slotJumper.textContent = 1;

        slotNum.appendChild(slotJumper);
        slotNum.innerHTML +=
          "/" + Math.ceil(userVars["DFSTATS_df_storage_slots"] / 40) + ")";
        slotNum
          .querySelector("input")
          .addEventListener("change", storage_autoChangePage);
      }
    }
  }
  if (userVars["DFSTATS_df_storage_slots"] >= 480) {
    if (
      document.getElementById("buyStorageSlots") &&
      document.getElementById("buyStorageSlots").querySelector("span")
    ) {
      document
        .getElementById("buyStorageSlots")
        .parentNode.removeChild(document.getElementById("buyStorageSlots"));
    }
  }

  webCall(
    "get_storage",
    dataArr,
    function (data) {
      storageBox = flshToArr(data);
      populateStorage();
      updateAllFields();
    },
    true
  );
  // updateIntoArr
}

function reloadInventoryData(callback = false) {
  var dataArr = {};
  dataArr["sc"] = userVars["sc"];
  dataArr["templateID"] = userVars["template_ID"];
  dataArr["userID"] = userVars["userID"];
  dataArr["password"] = userVars["password"];

  webCall("get_values", dataArr, function (data) {
    updateIntoArr(flshToArr(data, "DFSTATS_"), userVars);
    populateInventory();
    updateAllFields();
    renderAvatarUpdate();
    if (callback !== false) {
      callback();
    }
  });
}

function populateInventory() {
  let targets = document
    .getElementById("inventory")
    .querySelectorAll(".validSlot");
  for (let target of targets) {
    while (target.lastChild) {
      target.removeChild(target.lastChild);
    }
  }
  for (var i = 1; i <= userVars["DFSTATS_df_invslots"]; i++) {
    if (userVars["DFSTATS_df_inv" + i + "_type"] !== "") {
      setSlotData(
        userVars["DFSTATS_df_inv" + i + "_type"],
        userVars["DFSTATS_df_inv" + i + "_quantity"],
        document
          .getElementById("inventory")
          .querySelector("td[data-slot='" + i + "'].validSlot")
      );
    }
  }
}

function damageColor(current, max) {
  var color = "12FF00";
  var damagePercent = current / max;
  if (damagePercent <= 0) {
    color = "D20303";
  } else if (damagePercent < 0.25) {
    color = "D20303";
  } else if (damagePercent < 0.5) {
    color = "FF4800";
  } else if (damagePercent < 0.75) {
    color = "FFCC00";
  }

  return color;
}

function storageChange(direction) {
  if (pageLock) {
    return;
  }
  if (direction === "forward") {
    for (var i = 1; i <= 40; i++) {
      var currentSlot = i + 40 * storageTab;
      document.querySelector(
        "div[data-slot='" + currentSlot + "']"
      ).dataset.slot = i + 40 * (storageTab + 1);
    }
    storageTab++;
    if ((storageTab + 1) * 40 >= userVars["DFSTATS_df_storage_slots"]) {
      document.getElementById("storageForward").style.display = "none";
    } else {
      document.getElementById("storageForward").style.display = "block";
    }
    document.getElementById("storageBackward").style.display = "block";
  } else if (direction === "backward") {
    for (var i = 1; i <= 40; i++) {
      var currentSlot = i + 40 * storageTab;
      document.querySelector(
        "div[data-slot='" + currentSlot + "']"
      ).dataset.slot = i + 40 * (storageTab - 1);
    }
    storageTab--;
    if (storageTab - 1 < 0) {
      document.getElementById("storageBackward").style.display = "none";
    } else {
      document.getElementById("storageBackward").style.display = "block";
    }
    document.getElementById("storageForward").style.display = "block";
  }
  slotNum.textContent = "(";

  var slotJumper = document.createElement("input");
  slotJumper.placeholder = storageTab + 1;
  slotJumper.type = "number";
  slotJumper.style.width = "16px";
  slotJumper.textContent = 1;

  slotNum.appendChild(slotJumper);
  slotNum.innerHTML +=
    "/" + Math.ceil(userVars["DFSTATS_df_storage_slots"] / 40) + ")";

  slotNum
    .querySelector("input")
    .addEventListener("change", storage_autoChangePage);

  populateStorage();
}

function loadRecovery() {
  pageLock = true;
  df_prompt.innerHTML = "<div style='text-align: center;'>Loading...</div>";
  df_prompt.parentNode.style.display = "block";

  var dataArr = [];
  dataArr["userID"] = userVars["userID"];
  dataArr["password"] = userVars["password"];
  dataArr["sc"] = userVars["sc"];
  dataArr["action"] = "list";
  dataArr["gv"] = 21;

  webCall("hotrods/recover", dataArr, function (data) {
    var recoverData = flshToArr(data);
    var recoverables = {};
    for (var i in recoverData) {
      var parsedArr = i.explode("_", 2);
      if (recoverables[parsedArr[0]] === undefined) {
        recoverables[parsedArr[0]] = {};
      }
      recoverables[parsedArr[0]][parsedArr[1]] = recoverData[i];
    }
    recoverables = Object.keys(recoverables).map(function (e) {
      return recoverables[e];
    });
    recoverables.sort(function (a, b) {
      return a["time"] - b["time"];
    });

    var recoveryMenu = document.createElement("div");
    recoveryMenu.id = "recoverymenu";
    recoveryMenu.innerHTML =
      "<div class='menutitle'>Recover Dismantled or Scrapped Items</div>You only have 30 minutes to recover an item. Only 5 scraps, 5 dismantles, and 5 discards are listed at a time. Recovering one will show previous items.";
    var recTable = document.createElement("div");
    recTable.classList.add("rt");

    // labels
    var rtLabelRow = document.createElement("div");
    rtLabelRow.classList.add("rtrow");

    var rtItemLabel = document.createElement("div");
    rtItemLabel.style.left = "10px";
    rtItemLabel.textContent = "Item";
    rtLabelRow.appendChild(rtItemLabel);

    var rtCostLabel = document.createElement("div");
    rtCostLabel.style.left = "200px";
    rtCostLabel.style.right = "200px";
    rtCostLabel.style.textAlign = "center";
    rtCostLabel.textContent = "Cost to Recover";
    rtLabelRow.appendChild(rtCostLabel);

    var rtLabelTime = document.createElement("div");
    rtLabelTime.style.right = "100px";
    rtLabelTime.textContent = "Time Remaining";
    rtLabelRow.appendChild(rtLabelTime);

    recTable.appendChild(rtLabelRow);

    for (var i = 0; i < recoverables.length; i++) {
      var recoverable = recoverables[i];
      recoverable["time"] = parseInt(recoverable["time"]);
      var recRow = document.createElement("div");
      recRow.classList.add("rtrow");
      recRow.classList.add("fakeItem");
      recRow.dataset.type = recoverable["item"];
      recRow.dataset.quantity = recoverable["quantity"];
      //recRow.dataset.quantity = recoverable["quantity"];

      var itemLookup = recoverable["item"].split("_")[0];
      var outputName = "";
      if (recoverable["item"].indexOf("_name") !== -1) {
        var endOName = recoverable["item"].indexOf(
          "_",
          recoverable["item"].indexOf("_name") + 5
        );
        if (endOName > 0) {
          outputName = recoverable["item"].substring(
            recoverable["item"].indexOf("_name") + 5,
            endOName
          );
        } else {
          outputName = recoverable["item"].substring(
            recoverable["item"].indexOf("_name") + 5
          );
        }
      } else {
        outputName = globalData[itemLookup]["name"];
      }
      var colName = document.createElement("div");
      colName.style.left = "10px";
      colName.style.color = "red";
      colName.textContent = outputName;
      //if(recoverable["quantity"] > 1)
      //{
      //	colName.textContent += " (" + recoverable["quantity"] + ")";
      //}
      recRow.appendChild(colName);

      var colCost = document.createElement("div");
      colCost.style.left = "200px";
      colCost.style.right = "200px";
      colCost.style.textAlign = "center";
      if (
        recoverable["type"] === "scrap" ||
        recoverable["type"] === "discard"
      ) {
        colCost.textContent = "$" + nf.format(recoverable["cash_earned"]);
      } else if (recoverable["type"] === "dismantle") {
        if (globalData[itemLookup]["dismantle"].length > 1) {
          colCost.style.color = "lime";
          colCost.textContent = "[Hover for List]";
        } else {
          colCost.textContent =
            globalData[globalData[itemLookup]["dismantle"].split(",")[0]][
              "name"
            ] + " x1";
        }
      }
      recRow.appendChild(colCost);

      var colRemainingTime = document.createElement("div");
      colRemainingTime.style.right = "140px";
      colRemainingTime.dataset.endtime = recoverable["time"] + 7200;
      colRemainingTime.textContent = createMovingTimeString(
        recoverable["time"] + 7200 - userVars["pagetime"],
        3
      );
      colRemainingTime.classList.add("timeKeeper");

      recRow.appendChild(colRemainingTime);

      var colBtn = document.createElement("button");
      colBtn.style.right = "10px";
      colBtn.style.position = "absolute";
      colBtn.textContent = "Recover";
      colBtn.dataset.act = recoverable["type"];
      colBtn.dataset.stime = recoverable["time"];

      if (
        (recoverable["type"] === "scrap" ||
          recoverable["type"] === "discard") &&
        userVars["df_cash"] < recoverable["cost"]
      ) {
        colBtn.disabled = true;
      } else if (recoverable["type"] === "dismantle") {
        var itemsRemoved = [];
        var canRecover = true;
        let dismantleList = globalData[itemLookup]["dismantle"].split(",");
        for (var iTF in dismantleList) {
          var slotOccupied = findInInventory(dismantleList[iTF]);
          if (slotOccupied === false) {
            canRecover = false;
            break;
          } else {
            itemsRemoved[slotOccupied] =
              userVars["df_inv" + slotOccupied + "_type"];
            userVars["df_inv" + slotOccupied + "_type"] = "";
          }
        }
        if (canRecover) {
          colBtn.onclick = recoverItemRequest;
        } else {
          colBtn.disabled = true;
        }
        for (var iTF in itemsRemoved) {
          userVars["df_inv" + iTF + "_type"] = itemsRemoved[iTF];
        }
      } else {
        colBtn.onclick = recoverItemRequest;
      }

      recRow.appendChild(colBtn);

      recTable.appendChild(recRow);
    }

    recoveryMenu.appendChild(recTable);
    inventoryHolder.appendChild(recoveryMenu);

    var rmClose = document.createElement("button");
    rmClose.style.position = "absolute";
    rmClose.style.top = "6px";
    rmClose.style.right = "5px";
    rmClose.style.fontSize = "12pt";

    rmClose.textContent = "close";
    rmClose.onclick = function () {
      recoveryMenu.parentNode.removeChild(recoveryMenu);
    };
    recoveryMenu.appendChild(rmClose);

    df_prompt.parentNode.style.display = "none";
    pageLock = false;
  });
}

function findInInventory(itemToFind) {
  for (var i = 1; i <= userVars["DFSTATS_df_invslots"]; i++) {
    if (userVars["DFSTATS_df_inv" + i + "_type"] === itemToFind) {
      return i;
    }
  }
  return false;
}

function recoverItemRequest(evt) {
  if (pageLock) {
    return;
  }
  pageLock = true;
  df_prompt.innerHTML = "<div style='text-align: center;'>Recovering...</div>";
  df_prompt.parentNode.style.display = "block";
  var evtTarget = evt.currentTarget;
  evtTarget.disabled = true;
  var dataArr = {
    userID: userVars["userID"],
    password: userVars["password"],
    sc: userVars["sc"],
    action: "recover",
    item: evtTarget.parentNode.dataset.type,
    slaction: evtTarget.dataset.act,
    itime: evtTarget.dataset.stime,
  };

  webCall(
    "hotrods/recover",
    dataArr,
    function (data) {
      evtTarget.parentNode.parentNode.removeChild(evtTarget.parentNode);
      if (data === "error") {
        webCallError();
        return;
      }
      data = flshToArr(data, "DFSTATS_");
      updateIntoArr(data, userVars);
      var recoveryMenu = document.getElementById("recoverymenu");
      recoveryMenu.parentNode.removeChild(recoveryMenu);
      populateInventory();
      populateCharacterInventory();
      updateAllFields();
      for (var i = 1; i < userVars["DFSTATS_df_invslots"]; i++) {
        if (data["DFSTATS_df_inv" + i + "_type"]) {
          break;
        }
      }
      var runs = 5;
      var flashItem = setInterval(function () {
        var elemToFlash = document.querySelector(
          ".validSlot[data-slot='" + i + "']"
        );
        elemToFlash.classList.toggle("pointOut");
        if (runs > 0) {
          runs--;
        } else {
          clearInterval(flashItem);
        }
      }, 500);
    },
    true
  );
}

function initiateInventory(flashErl, callback) {
  inventoryHolder = document.getElementById("inventoryholder");
  textAddonRef = document.getElementById("textAddon");
  df_prompt = document.getElementById("gamecontent");
  df_prompt.tabIndex = "0";
  var invC = document.getElementById("invController");
  infoBox = document.getElementById("infoBox");

  ctxMenuHolder = document.createElement("div");
  inventoryHolder.appendChild(ctxMenuHolder);

  window.addEventListener("mousemove", mouseTracker, false);

  inventoryHolder.addEventListener("mousemove", infoCard, false);
  infoBox.addEventListener("mousemove", clearCard, false);

  window.addEventListener("mousemove", hoverAction, false);
  window.addEventListener("keyup", deactivatedKey, false);
  window.addEventListener("keydown", activatedKey, false);

  // Drag actions
  invC.addEventListener("mousedown", dragStart, false);
  document.addEventListener("mouseup", dragEnd, false);
  inventoryHolder.addEventListener("mousemove", drag, false);
  invC.addEventListener("touchstart", dragStart, false);
  infoBox.addEventListener("mousedown", dragStart, false);
  infoBox.addEventListener("touchstart", dragStart, false);
  document.addEventListener("touchend", dragEnd, false);
  inventoryHolder.addEventListener("touchmove", drag, false);

  inventoryActionButtonHolder = document.createElement("div");
  inventoryActionButtonHolder.style =
    "position: absolute; right: 13px; bottom: 86px; z-index: 1;";
  inventoryHolder.appendChild(inventoryActionButtonHolder);

  window.addEventListener("mousedown", function (e) {
    if (
      ctxMenuHolder.style.display === "block" &&
      e.target !== ctxMenuHolder &&
      e.target.parentNode !== ctxMenuHolder
    ) {
      ctxMenuHolder.style.display = "none";
    }
  });

  flshToArr(flashErl, "", setUserVars);

  let itemFinderHolder = document.createElement("div");
  itemFinderHolder.id = "inventorySearch";

  // elems to add
  /*
	let itemFinderOpen = document.createElement('div');
	let itemFinderClose = document.createElement('div');
	let itemFinderInput = document.createElement('input');
	
	itemFinderOpen.innerHTML = 'Item Search';
	itemFinderOpen.classList.add('openBtn');
	itemFinderOpen.addEventListener('click', function() {
		itemFinderOpen.style.display = 'none';
		itemFinderClose.style.display = 'inline-block';
		itemFinderInput.style.display = 'inline-block';
		itemFinderInput.focus();
	});
	itemFinderHolder.appendChild(itemFinderOpen);
	
	itemFinderClose.innerHTML = 'X';
	itemFinderClose.classList.add('closeBtn');
	itemFinderClose.addEventListener('click', function() {
		itemFinderOpen.style.display = '';
		itemFinderClose.style.display = '';
		itemFinderInput.style.display = '';
	});
	itemFinderHolder.appendChild(itemFinderClose);
	
	itemFinderInput.placeholder = 'Item name';
	itemFinderInput.classList.add('opElem');
	itemFinderInput.addEventListener('input', function() {
		if(this.value.length > 0)
		{
			let searchValue = this.value.toLowerCase();
			let allShownItems = document.getElementsByClassName('item');
			for(let i = 0; i < allShownItems.length; i++)
			{
				let item = new InventoryItem(allShownItems[i].dataset.type);
				let itemTypeName = globalData[item.type]['name'].toLowerCase();
				let itemName = item.name;
				
				allShownItems[i].classList.add('inventorySearchHighlight');
				if(allShownItems[i].dataset.type.indexOf(searchValue) !== -1 || itemTypeName.indexOf(searchValue) !== -1 || typeof itemName !== 'undefined' && itemName.toLowerCase().indexOf(searchValue) !== -1)
				{
					if(allShownItems[i].classList.contains('negative'))
					{
						allShownItems[i].classList.remove('negative');
					}
				} else {
					if(!allShownItems[i].classList.contains('negative'))
					{
						allShownItems[i].classList.add('negative');
					}
				}
			}
		} else {
			let allHighlightedItems = document.getElementsByClassName('inventorySearchHighlight');
			for(let i = 0; i < allHighlightedItems.length; i++)
			{
				allHighlightedItems[i].classList.remove('inventorySearchHighlight');
				allHighlightedItems[i].classList.remove('negative');
			}
		}
	});
	itemFinderHolder.appendChild(itemFinderInput);
	
	inventoryHolder.appendChild(itemFinderHolder);
	*/

  var inventoryLabel = document.createElement("div");
  inventoryLabel.style.textAlign = "center";
  inventoryLabel.textContent = "Inventory";
  inventoryLabel.classList.add("opElem");
  inventoryLabel.style.bottom = "90px";
  inventoryLabel.style.width = "100%";
  var sl = document.createElement("div");
  sl.style.textAlign = "center";
  sl.textContent = x4jC(parseInt(userVars["userID"]).toString(36));
  sl.classList.add("opElem");
  sl.style.top = "-8px";
  sl.style.filter = "opacity(0.8)";
  sl.style.color = "#111";
  sl.style.userSelect = "none";
  inventoryLabel.appendChild(sl);

  inventoryHolder.appendChild(inventoryLabel);

  document.getElementById("inventory").innerHTML = "";
  for (var x = 0; x < 2; x++) {
    var invHalf = document.createElement("tr");
    for (var y = 0; y < userVars["DFSTATS_df_invslots"] / 2; y++) {
      var slot = document.createElement("td");
      slot.dataset.slot = x + 1 + y * 2;
      slot.classList.add("validSlot");
      invHalf.appendChild(slot);
    }
    document.getElementById("inventory").appendChild(invHalf);
  }

  var dataArr = [];
  dataArr["userID"] = userVars["userID"];
  dataArr["password"] = userVars["password"];
  dataArr["sc"] = userVars["sc"];
  dataArr["action"] = "list";
  dataArr["gv"] = 21;

  var data = {
    credits: { name: "Credits", itemtype: "credits", cb_exclude: true },
    brokenitem: {
      name: "Broken Item; Contact Support",
      itemtype: "broken",
      cb_exclude: true,
    },
  };
  updateIntoArr(data, globalData);
  for (let itemcode in globalData) {
    if (
      typeof globalData[itemcode]["itemtype"] !== "undefined" &&
      globalData[itemcode]["itemtype"] === "weapon"
    ) {
      if (typeof globalData[itemcode]["type"] !== "undefined") {
        switch (globalData[itemcode]["type"]) {
          case "autopistol":
          case "revolver":
            globalData[itemcode]["weptype"] = "Pistol";
            break;
          case "rifle":
            globalData[itemcode]["weptype"] = "Rifle";
            break;
          case "grenadelauncher":
            globalData[itemcode]["weptype"] = "Explosive";
            break;
          case "shotgun":
            globalData[itemcode]["weptype"] = "Shotgun";
            break;
          case "machinegun":
          case "minigun":
          case "bigmachinegun":
          case "submachinegun":
            globalData[itemcode]["weptype"] = "Machine Gun";
            break;
          default:
            globalData[itemcode]["weptype"] = "Melee";
            break;
        }
        if (itemCategories.indexOf(globalData[itemcode]["weptype"]) === -1) {
          itemCategories.push(globalData[itemcode]["weptype"]);
        }
      }
    }
  }

  webCall(
    "hotrods/item_lock",
    dataArr,
    function (data) {
      lockedSlots = data.split(",");

      var lockSlotButton = document.createElement("button");
      lockSlotButton.classList.add("opElem");
      lockSlotButton.style.left = "20px";
      lockSlotButton.style.bottom = "86px";
      lockSlotButton.textContent = "SlotLock?";

      lockSlotButton.addEventListener("click", lockSlotPrompt);

      inventoryHolder.appendChild(lockSlotButton);
      doLockedElems();

      // The rest
      fakeGrabbedItem = document.getElementById("fakeGrabbedItem");

      if (
        window.location.pathname.indexOf("/DF3D/") === -1 &&
        window.location.search.indexOf("page=31") === -1
      ) {
        var setBtn = document.createElement("div");
        setBtn.id = "settingsButton";
        setBtn.addEventListener("click", loadSettings);
        inventoryHolder.appendChild(setBtn);
        var frm = document.createElement("iframe");
        frm.id = "settingsBox";
        inventoryHolder.parentNode.appendChild(frm);
        inventoryHolder.parentNode.style.position = "relative";
      } else {
        loadStatusData();
      }
      //window.addEventListener('gamepadconnected', gamepadTest);
      populateInventory();

      if (callback) {
        callback();
      }
      pageLock = false;
    },
    true
  );
}

let runGPScan = false;
let gpEvent;
let controllerCursor;
let currentSelectedSlot;

/**
 *
 * @param {GamepadEvent} evt
 */
function gamepadTest(evt) {
  if (!runGPScan) {
    runGPScan = true;
    currentSelectedSlot = document
      .getElementById("inventory")
      .querySelector(`.validSlot[data-slot='1']`);
    let gamepadDisplay = document.createElement("div");
    gamepadDisplay.id = "gamepadDebug";
    gamepadDisplay.style =
      "position: absolute; left: 50px; top: 50px; background-color: black;";
    document.body.appendChild(gamepadDisplay);

    controllerCursor = document.createElement("div");
    controllerCursor.style.position = "absolute";
    controllerCursor.style.width = "44px";
    controllerCursor.style.height = "44px";
    controllerCursor.style.border = "5px red solid";

    document.body.appendChild(controllerCursor);

    gpEvent = requestAnimationFrame(captureGamepadInput);
  }
}

let gpStart;
let totalTimePassed = 0;
let gpCursorChange = 0;

function captureGamepadInput(timestamp) {
  const gamepads = navigator.getGamepads();
  if (!gamepads) {
    cancelAnimationFrame(gpEvent);
    runGPScan = false;
    return;
  }

  let gamepadDisplay = document.getElementById("gamepadDebug");
  while (gamepadDisplay.childNodes[0]) {
    gamepadDisplay.removeChild(gamepadDisplay.childNodes[0]);
  }

  for (const gp of gamepads) {
    if (!gp) {
      continue;
    }

    let myGPDisplay = gamepadDisplay.querySelector(`#controller${gp.id}`);
    if (!myGPDisplay) {
      myGPDisplay = document.createElement("div");
      myGPDisplay.id = `#controller${gp.index}`;
      gamepadDisplay.appendChild(myGPDisplay);
    }

    myGPDisplay.innerHTML = `<h3>Controller ${gp.index}</h3>`;
    for (let [i, btn] of gp.buttons.entries()) {
      myGPDisplay.innerHTML += `<p>Button ${i}: ${btn.value}</p>`;
    }
    for (let [i, axis] of gp.axes.entries()) {
      myGPDisplay.innerHTML += `<p>Axis ${i}: ${axis}</p>`;
    }

    if (gpStart === undefined) {
      gpStart = timestamp;
    }
    let elapsed = timestamp - gpStart;
    totalTimePassed += elapsed;

    let updatedCursorChange = 0;
    if (gp.axes[0] > 0.4 || gp.buttons[15].pressed) {
      updatedCursorChange += 2;
    } else if (gp.axes[0] < -0.4 || gp.buttons[14].pressed) {
      updatedCursorChange -= 2;
    }
    if (gp.axes[1] > 0.4 || gp.buttons[13].pressed) {
      updatedCursorChange++;
    } else if (gp.axes[1] < -0.4 || gp.buttons[12].pressed) {
      updatedCursorChange--;
    }

    if (updatedCursorChange !== gpCursorChange || totalTimePassed >= 700) {
      gpCursorChange = updatedCursorChange;
      gpStart = timestamp;
      totalTimePassed = 0;
      /**
			Axis 0 = LH
			Axis 1 = LV
			Axis 2 = RH
			Axis 3 = RV
			*/
      let targetSlot = parseInt(currentSelectedSlot.dataset.slot);
      if (gpCursorChange % 2 !== 0) {
        if (targetSlot % 2 === 0) {
          if (gpCursorChange > 0) {
            gpCursorChange--;
          }
        } else {
          if (gpCursorChange < 0) {
            gpCursorChange++;
          }
        }
      }
      if (
        targetSlot + gpCursorChange >= 1 &&
        targetSlot + gpCursorChange <= userVars["DFSTATS_df_invslots"]
      ) {
        targetSlot += gpCursorChange;
      }
      currentSelectedSlot = document
        .getElementById("inventory")
        .querySelector(`.validSlot[data-slot='${targetSlot}']`);
      let targetRect = currentSelectedSlot.getBoundingClientRect();
      targetRect.left;
      controllerCursor.style.left = window.scrollX + targetRect.x - 2 + "px";
      controllerCursor.style.top = window.scrollY + targetRect.y - 2 + "px";
    }
  }

  gpEvent = requestAnimationFrame(captureGamepadInput);
}

function generateTextItemList(itemArr, prepend = "") {
  var dataHolder = {};
  for (var i = 0; i < itemArr.length; i++) {
    if (typeof dataHolder[itemArr[i]] !== "undefined") {
      dataHolder[itemArr[i]]++;
    } else {
      dataHolder[itemArr[i]] = 1;
    }
  }

  var output = "";
  for (var rawType in dataHolder) {
    var realRawType = rawType;
    if (rawType.indexOf("_") > -1) {
      realRawType = rawType.substring(0, rawType.indexOf("_"));
    }
    if (output !== "") {
      output += "<br />";
    }
    output +=
      prepend +
      globalData[realRawType]["name"] +
      " x" +
      dataHolder[realRawType];
  }
  return output;
}

function MenuItemPopulate(itemElem) {
  var itemType = currentItem.dataset.type.split("_");
  var itemData = [
    parseInt(currentItem.parentNode.dataset.slot),
    currentItem.dataset.type,
    currentItem.parentNode.parentNode.parentNode.id,
  ];

  var itemName = document.createElement("div");
  itemName.style.textAlign = "center";

  if (globalData[itemType[0]]["itemtype"] === "weapon") {
    var n = currentItem.dataset.type.indexOf("_stats") + 6;
    n = currentItem.dataset.type.substring(n, n + 3);
    if (mcData[n] && mcData[n][0] !== "") {
      itemName.style.color = mcData[n][0];
      itemElem.style.borderColor = mcData[n][0];
    }
  } else if (globalData[itemType[0]]["itemtype"] === "armour") {
    var n = currentItem.dataset.type.indexOf("_stats") + 6;
    n = currentItem.dataset.type.substring(n, n + 4);
    if (mcData[n] && mcData[n][0] !== "") {
      itemName.style.color = mcData[n][0];
      itemElem.style.borderColor = mcData[n][0];
    }
  }

  itemName.innerHTML = itemNamer(
    currentItem.dataset.type,
    currentItem.dataset.quantity
  );
  itemElem.appendChild(itemName);

  var button1 = document.createElement("button");
  button1.textContent = "Scrap";
  button1.style.width = "100%";

  button1.onclick = function () {
    var scrapPrice = scrapValue(
      currentItem.dataset.type,
      currentItem.dataset.quantity
    );
    extraData = [itemData, scrapPrice];
    extraData["action"] = "scrap";
    df_prompt.innerHTML = "Are you sure you want to scrap the ";
    if (itemData[1].indexOf("_name") >= 0) {
      var endOName = itemData[1].indexOf("_", itemType.indexOf("_name") + 5);
      if (endOName > 0) {
        df_prompt.innerHTML +=
          "<span style='color: red;'>" +
          itemData[1].substring(itemData[1].indexOf("_name") + 5, endOName) +
          "</span>";
      } else {
        df_prompt.innerHTML +=
          "<span style='color: red;'>" +
          itemData[1].substring(itemData[1].indexOf("_name") + 5) +
          "</span>";
      }
    } else {
      df_prompt.innerHTML +=
        "<span style='color: red;'>" +
        globalData[itemType[0]]["name"] +
        "</span>";
    }
    df_prompt.innerHTML +=
      " for <span style='color: #FFCC00;'>$" +
      nf.format(scrapPrice) +
      "</span>?";
    df_prompt.classList.add("warning");

    var noButton = document.createElement("button");
    noButton.style.position = "absolute";
    noButton.style.top = "72px";
    noButton.addEventListener("click", function () {
      cleanPlacementMessage();
      df_prompt.parentNode.style.display = "none";
      df_prompt.innerHTML = "";
      df_prompt.classList.remove("warning");
      df_prompt.classList.remove("redhighlight");
      pageLock = false;
    });
    noButton.textContent = "No";
    noButton.style.right = "86px";
    var yesButton = document.createElement("button");
    yesButton.textContent = "Yes";
    yesButton.style.position = "absolute";
    yesButton.style.left = "86px";
    yesButton.style.top = "72px";
    yesButton.addEventListener("click", function () {
      yesButton.disabled = true;
      cleanPlacementMessage();
      df_prompt.classList.remove("warning");
      df_prompt.classList.remove("redhighlight");
      scrapItem(extraData);
    });

    df_prompt.appendChild(yesButton);
    df_prompt.onkeydown = function (e) {
      if (e.keyCode === 13) {
        df_prompt.onkeydown = null;
        yesButton.click();
      }
    };

    df_prompt.appendChild(noButton);
    df_prompt.parentNode.style.display = "block";

    df_prompt.focus();
    ctxMenuHolder.style.display = "none";
  };
  itemElem.appendChild(button1);

  if (currentItem.parentNode.parentNode.parentNode.id === "inventory")
    if (
      globalData[itemType[0]]["dismantle"] &&
      globalData[itemType[0]]["dismantle"].length > 0
    ) {
      var button2 = document.createElement("button");
      button2.textContent = "Dismantle";
      button2.style.width = "100%";
      button2.onclick = function () {
        var scrapPrice = scrapValue(
          currentItem.dataset.type,
          currentItem.dataset.quantity
        );
        extraData = [itemData, scrapPrice];
        extraData["action"] = "dismantle";

        df_prompt.innerHTML = "Are you sure you want to dismantle the ";
        if (itemData[1].indexOf("_name") >= 0) {
          var endOName = itemData[1].indexOf(
            "_",
            itemType.indexOf("_name") + 5
          );
          if (endOName > 0) {
            df_prompt.innerHTML +=
              "<span style='color: red;'>" +
              itemData[1].substring(
                itemData[1].indexOf("_name") + 5,
                endOName
              ) +
              "</span>";
          } else {
            df_prompt.innerHTML +=
              "<span style='color: red;'>" +
              itemData[1].substring(itemData[1].indexOf("_name") + 5) +
              "</span>";
          }
        } else {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            globalData[itemType[0]]["name"] +
            "</span>";
        }
        df_prompt.innerHTML +=
          " for <span style='color: #12FF00;'>" +
          generateTextItemList(
            globalData[itemType[0]]["dismantle"].split(",")
          ) +
          "</span>?";
        df_prompt.classList.add("warning");

        var noButton = document.createElement("button");
        noButton.style.position = "absolute";
        noButton.style.top = "72px";
        noButton.addEventListener("click", function () {
          cleanPlacementMessage();
          df_prompt.parentNode.style.display = "none";
          df_prompt.innerHTML = "";
          df_prompt.classList.remove("warning");
          df_prompt.classList.remove("redhighlight");
          pageLock = false;
        });
        noButton.textContent = "No";
        noButton.style.right = "86px";
        var yesButton = document.createElement("button");
        yesButton.textContent = "Yes";
        yesButton.style.position = "absolute";
        yesButton.style.left = "86px";
        yesButton.style.top = "72px";
        yesButton.addEventListener("click", function () {
          yesButton.disabled = true;
          cleanPlacementMessage();
          df_prompt.classList.remove("warning");
          df_prompt.classList.remove("redhighlight");
          scrapItem(extraData);
        });

        df_prompt.appendChild(yesButton);
        df_prompt.onkeydown = function (e) {
          if (e.keyCode === 13) {
            df_prompt.onkeydown = null;
            yesButton.click();
          }
        };

        df_prompt.appendChild(noButton);
        df_prompt.parentNode.style.display = "block";

        df_prompt.focus();
        ctxMenuHolder.style.display = "none";
      };
      itemElem.appendChild(button2);
    }
}

function openContextMenu(e) {
  if (
    e.target.classList.contains("item") &&
    !e.target.parentNode.classList.contains("locked")
  ) {
    currentItem = e.target;
    if (currentItem.parentNode.dataset.slot === "35") {
      var totalBackpackSlots = parseInt(
        globalData[userVars["DFSTATS_df_backpack"].split("_")[0]]["slots"]
      );
      if (currentItem.dataset.type.indexOf("_stats") >= 0) {
        totalBackpackSlots += parseInt(
          currentItem.dataset.type.charAt(
            currentItem.dataset.type.indexOf("_stats") + 6
          )
        );
      }
      for (var i = 1; i <= totalBackpackSlots; i++) {
        if (
          typeof userVars["DFSTATS_df_backpack" + i + "_type"] !==
            "undefined" &&
          userVars["DFSTATS_df_backpack" + i + "_type"] !== ""
        ) {
          ctxMenuHolder.style.display = "none";
          return;
        }
      }
    }

    ctxMenuHolder.innerHTML = "";
    ctxMenuHolder.style.display = "block";
    ctxMenuHolder.style.backgroundColor = "black";
    ctxMenuHolder.style.border = "white solid 1px";
    ctxMenuHolder.style.zIndex = "20";
    ctxMenuHolder.style.textAlign = "left";

    ctxMenuHolder.style.position = "absolute";

    ctxMenuHolder.style.width = "140px";

    MenuItemPopulate(ctxMenuHolder);

    ctxMenuHolder.style.visibility = "hidden";
    ctxMenuHolder.style.display = "block";

    var invHoldOffsets = inventoryHolder.getBoundingClientRect();

    if (mousePos[1] + ctxMenuHolder.offsetHeight > invHoldOffsets.bottom) {
      ctxMenuHolder.style.top =
        mousePos[1] - ctxMenuHolder.offsetHeight - invHoldOffsets.top + "px";
    } else {
      ctxMenuHolder.style.top = mousePos[1] - invHoldOffsets.top + "px";
    }

    if (mousePos[0] + ctxMenuHolder.offsetWidth > invHoldOffsets.right) {
      ctxMenuHolder.style.left =
        inventoryHolder.offsetWidth - 40 - ctxMenuHolder.offsetWidth + "px";
    } else {
      ctxMenuHolder.style.left = mousePos[0] - invHoldOffsets.left + "px";
    }

    ctxMenuHolder.style.visibility = "visible";
    e.preventDefault();
  } else {
    ctxMenuHolder.style.display = "none";
  }
}

function doLockedElems() {
  for (var i = 0; i < lockedSlots.length; i++) {
    var workingSlot = parseInt(lockedSlots[i]);
    let affectedElem = null;
    if (workingSlot > 1050) {
      workingSlot -= 1050;

      affectedElem = document.querySelector(
        "#backpackdisplay .validSlot[data-slot='" + workingSlot + "']"
      );
    } else if (workingSlot > 1000) {
      workingSlot -= 1000;

      affectedElem = document.querySelector(
        "[data-slottype='implant'].validSlot[data-slot='" + workingSlot + "']"
      );
    } else if (workingSlot > 40 && inventoryType !== "clan_storage") {
      workingSlot -= 40;

      if (document.getElementById("storage") !== null) {
        affectedElem = document.querySelector(
          "#storage .validSlot[data-slot='" + workingSlot + "']"
        );
      }
    } else {
      affectedElem = document.querySelector(
        ".validSlot[data-slot='" +
          workingSlot +
          "']:not(#storage .validSlot):not([data-slottype='implant']):not(#backpackdisplay .validSlot)"
      );
    }
    if (affectedElem !== null) {
      affectedElem.classList.add("locked");
    }
  }
}

function loadStatusData() {
  var armour = userVars["DFSTATS_df_armourtype"].split("_");
  var sideOutput = "";
  var health = "Healthy";
  var healthColor = "12FF00";
  var hp =
    parseInt(userVars["DFSTATS_df_hpcurrent"]) /
    parseInt(userVars["DFSTATS_df_hpmax"]);
  if (hp <= 0) {
    health = "DEAD";
    healthColor = "D20303";
  } else if (hp < 0.25) {
    health = "Critical";
    healthColor = "D20303";
  } else if (hp < 0.5) {
    health = "Serious";
    healthColor = "FF4800";
  } else if (hp < 0.75) {
    health = "Injured";
    healthColor = "FFCC00";
  }
  health +=
    "<br />" +
    userVars["DFSTATS_df_hpcurrent"] +
    " / " +
    userVars["DFSTATS_df_hpmax"];
  if (checkLSBool("general", "statusPercents")) {
    health += "<br />(" + Math.round(hp * 100) + "%)";
  }

  sideOutput +=
    "<div><img src='../hotrods/hotrods_v" + hrV + "/HTML5/images/heart.png'>";
  sideOutput +=
    "<div class='playerHealth' style='top: 3px; width: 65px; text-align: center; font-weight: 100; color: #" +
    healthColor +
    ";'>" +
    health +
    "</div></div>";

  health = "Nourished";
  healthColor = "12FF00";
  hp = parseInt(userVars["DFSTATS_df_hungerhp"]);
  if (hp <= 0) {
    health = "Dying";
    healthColor = "D20303";
  } else if (
    hp <
    Math.floor((50 + Math.floor(userVars["DFSTATS_df_survival"] * 1.5)) * 0.25)
  ) {
    health = "Starving";
    healthColor = "D20303";
  } else if (
    hp <
    Math.floor((50 + Math.floor(userVars["DFSTATS_df_survival"] * 1.5)) * 0.5)
  ) {
    health = "Hungry";
    healthColor = "FF4800";
  } else if (
    hp <
    Math.floor((50 + Math.floor(userVars["DFSTATS_df_survival"] * 1.5)) * 0.75)
  ) {
    health = "Fine";
    healthColor = "FFCC00";
  }
  health +=
    "<br />" +
    userVars["DFSTATS_df_hungerhp"] +
    " / " +
    (50 + Math.floor(userVars["DFSTATS_df_survival"] * 1.5));
  if (checkLSBool("general", "statusPercents")) {
    health += "<br />(" + Math.round(hp) + "%)";
  }

  sideOutput +=
    "<div><img src='../hotrods/hotrods_v" +
    hrV +
    "/HTML5/images/yummytummy.png'>";
  sideOutput +=
    "<div class='playerNourishment' style='top: 3px; width: 65px; text-align: center; font-weight: 100; color: #" +
    healthColor +
    ";'>" +
    health +
    "</div></div>";

  sideOutput += "<div id='statusBoxArmour'>";
  sideOutput += "<img src='";
  if (armour[0] !== "") {
    sideOutput +=
      "https://files.deadfrontier.com/deadfrontier/inventoryimages/large/" +
      armour[0] +
      ".png' width='40' />";
    health = "Normal";
    healthColor = "12FF00";
    hp =
      parseInt(userVars["DFSTATS_df_armourhp"]) /
      parseInt(userVars["DFSTATS_df_armourhpmax"]);
    if (hp <= 0) {
      health = "Broken";
      healthColor = "D20303";
    } else if (hp < 0.4) {
      health = "Damaged";
      healthColor = "FF4800";
    } else if (hp < 0.75) {
      health = "Scratched";
      healthColor = "FFCC00";
    }
    health +=
      "<br />" +
      userVars["DFSTATS_df_armourhp"] +
      " / " +
      userVars["DFSTATS_df_armourhpmax"];
    if (checkLSBool("general", "statusPercents")) {
      health += "<br />(" + Math.round(hp * 100) + "%)";
    }

    sideOutput +=
      "<div style='top: 3px; width: 65px; text-align: center; font-weight: 100; color: #" +
      healthColor +
      ";'>" +
      health +
      "</div>";
  } else {
    sideOutput += "' width='40' />";
    sideOutput +=
      "<div style='width: 65px; text-align: center; font-weight: 100;'></div>";
  }
  sideOutput += "</div>";

  var sidebarElem = document.createElement("div");
  sidebarElem.id = "statusBox";
  sidebarElem.innerHTML = sideOutput;

  inventoryHolder.appendChild(sidebarElem);
}

function initiateYard() {
  createCommonInventoryItems();
  let implantMenu = document.getElementById("implantMenu");
  let backpackMenu = document.getElementById("backpackMenu");

  let openImplantMenuBtn = document.createElement("button");
  openImplantMenuBtn.textContent = "Implants";
  openImplantMenuBtn.style = "padding: 3px 5px !important;";
  openImplantMenuBtn.dataset.pmoverride = "";
  openImplantMenuBtn.addEventListener("click", function (e) {
    if (typeof implantMenu.dataset.opened === "undefined") {
      implantMenu.style.top = "50px";
      implantMenu.dataset.opened = "open";
    } else {
      implantMenu.style.top =
        openImplantMenuBtn.offsetHeight - implantMenu.offsetHeight - 1 + "px";
      delete implantMenu.dataset.opened;
    }
  });
  openImplantMenuBtn.addEventListener("mousemove", function () {
    if (typeof implantMenu.dataset.opened !== "undefined") {
      displayPlacementMessage(
        "You can move an item over button to close menu",
        implantMenu.getBoundingClientRect().left,
        implantMenu.getBoundingClientRect().bottom + 12,
        "INFO"
      );
    } else {
      displayPlacementMessage(
        "You can move an item over button to open menu",
        implantMenu.getBoundingClientRect().left,
        implantMenu.getBoundingClientRect().bottom + 12,
        "INFO"
      );
    }
  });
  implantMenu.appendChild(document.createElement("br"));
  implantMenu.appendChild(openImplantMenuBtn);

  let openBackpackMenuBtn = document.createElement("button");
  openBackpackMenuBtn.textContent = "Backpack";
  openBackpackMenuBtn.style = "padding: 3px 5px !important;";
  openBackpackMenuBtn.dataset.pmoverride = "";
  openBackpackMenuBtn.addEventListener("click", function (e) {
    if (typeof backpackMenu.dataset.opened === "undefined") {
      backpackMenu.style.top = "50px";
      backpackMenu.dataset.opened = "open";
    } else {
      backpackMenu.style.top =
        openBackpackMenuBtn.offsetHeight - backpackMenu.offsetHeight - 1 + "px";
      delete backpackMenu.dataset.opened;
    }
  });
  openBackpackMenuBtn.addEventListener("mousemove", function () {
    if (typeof backpackMenu.dataset.opened !== "undefined") {
      displayPlacementMessage(
        "You can move an item over button to close menu",
        backpackMenu.getBoundingClientRect().left,
        backpackMenu.getBoundingClientRect().bottom + 12,
        "INFO"
      );
    } else {
      displayPlacementMessage(
        "You can move an item over button to open menu",
        backpackMenu.getBoundingClientRect().left,
        backpackMenu.getBoundingClientRect().bottom + 12,
        "INFO"
      );
    }
  });
  backpackMenu.appendChild(openBackpackMenuBtn);

  let inOpenMenu = false;
  inventoryHolder.addEventListener("mousemove", function () {
    if (inOpenMenu) {
      return;
    }
    let insideOfBPBoundingBox = false;
    let backpackMenuBB = openBackpackMenuBtn.getBoundingClientRect();
    if (
      mousePos[0] > backpackMenuBB.left &&
      mousePos[0] < backpackMenuBB.right &&
      mousePos[1] > backpackMenuBB.top &&
      mousePos[1] < backpackMenuBB.bottom
    ) {
      insideOfBPBoundingBox = true;
    }
    if (active && insideOfBPBoundingBox) {
      inOpenMenu = true;
      setTimeout(() => {
        if (
          active &&
          mousePos[0] > backpackMenuBB.left &&
          mousePos[0] < backpackMenuBB.right &&
          mousePos[1] > backpackMenuBB.top &&
          mousePos[1] < backpackMenuBB.bottom
        ) {
          if (typeof backpackMenu.dataset.opened === "undefined") {
            backpackMenu.style.top = "50px";
            backpackMenu.dataset.opened = "open";
          } else {
            backpackMenu.style.top =
              openBackpackMenuBtn.offsetHeight -
              backpackMenu.offsetHeight -
              1 +
              "px";
            delete backpackMenu.dataset.opened;
          }
        }
        setTimeout(() => {
          inOpenMenu = false;
        }, 50);
      }, 500);
    }

    let insideOfImpBoundingBox = false;
    let implantsMenuBB = openImplantMenuBtn.getBoundingClientRect();
    if (
      mousePos[0] > implantsMenuBB.left &&
      mousePos[0] < implantsMenuBB.right &&
      mousePos[1] > implantsMenuBB.top &&
      mousePos[1] < implantsMenuBB.bottom
    ) {
      insideOfImpBoundingBox = true;
    }
    if (active && insideOfImpBoundingBox) {
      inOpenMenu = true;
      setTimeout(() => {
        if (
          active &&
          mousePos[0] > implantsMenuBB.left &&
          mousePos[0] < implantsMenuBB.right &&
          mousePos[1] > implantsMenuBB.top &&
          mousePos[1] < implantsMenuBB.bottom
        ) {
          let implantBucket = document.getElementById("implantbucket");
          if (typeof implantMenu.dataset.opened === "undefined") {
            if (
              currentItem !== null &&
              globalData[
                currentItem.dataset.type.substring(
                  0,
                  currentItem.dataset.type.indexOf("_") !== -1
                    ? currentItem.dataset.type.indexOf("_")
                    : undefined
                )
              ]["implant"] === true
            ) {
              implantBucket.classList.add("open");
            }
            implantMenu.style.top = "50px";
            implantMenu.dataset.opened = "open";
          } else {
            implantMenu.style.top =
              openImplantMenuBtn.offsetHeight -
              (implantMenu.offsetHeight - implantBucket.offsetHeight + 50) -
              1 +
              "px";
            delete implantMenu.dataset.opened;
            implantBucket.classList.remove("open");
          }
        }
        setTimeout(() => {
          inOpenMenu = false;
        }, 50);
      }, 500);
    }
  });

  implantMenu.style.border = "1px solid #990000";
  implantMenu.style.backgroundColor = "rgba(0,0,0,0.8)";
  implantMenu.style.top =
    openImplantMenuBtn.offsetHeight - implantMenu.offsetHeight - 1 + "px";

  backpackMenu.style.border = "1px solid #990000";
  backpackMenu.style.backgroundColor = "rgba(0,0,0,0.8)";
  backpackMenu.style.top =
    openBackpackMenuBtn.offsetHeight - backpackMenu.offsetHeight - 1 + "px";

  setTimeout(() => {
    implantMenu.style.transition = "top 0.2s ease-in-out";
    backpackMenu.style.transition = "top 0.2s ease-in-out";
  }, 10);

  var invC = document.getElementById("invController");
  invC.addEventListener("contextmenu", openContextMenu, false);
  initiateCharacterInventory();
  document.getElementById("getRich").addEventListener("click", function () {
    location.href = "index.php?page=29";
  });
  var recoveryBtn = document.createElement("button");
  recoveryBtn.textContent = "Recover Items";
  recoveryBtn.style.position = "absolute";
  recoveryBtn.style.left = "150px";
  recoveryBtn.style.bottom = "86px";
  recoveryBtn.onclick = loadRecovery;
  inventoryHolder.appendChild(recoveryBtn);

  let scrapMenuOpen = inventoryHolder.querySelector("div[data-action='scrap']");
  scrapMenuOpen.addEventListener("click", function (evt) {
    promptLoading();
    let scrapMenu = document.createElement("div");
    scrapMenu.classList.add("genericActionBox");
    scrapMenu.classList.add("opElem");
    scrapMenu.style.inset = "50px";

    let scrapTitle = document.createElement("div");
    scrapTitle.style.fontFamily = "Downcome";
    scrapTitle.style.color = "#990000";
    scrapTitle.style.fontSize = "14pt";
    scrapTitle.style.marginTop = "8px";
    scrapTitle.textContent = "Scrap Items";
    scrapMenu.appendChild(scrapTitle);

    let scrapDescription = document.createElement("div");
    scrapDescription.style.marginTop = "8px";
    scrapDescription.innerHTML =
      "By using this menu, <em>you</em> take full responsibility for any mistakes <em>you</em> make.<br /><br />";
    scrapDescription.innerHTML +=
      "<em>You</em> are responsible for the safety of your items and <em>you</em> are responsible for using the tools provided like SlotLock.<br /><br />";
    scrapDescription.innerHTML +=
      "By using this menu, <em>you</em> are willingly taking part in a dangerous action that can result in the loss of important items.<br /><br />";
    scrapDescription.innerHTML +=
      "There is a scrap recovery system <em>you</em> can use. Try there before creating a support ticket.<br />";
    scrapDescription.innerHTML +=
      "<h3><em>This is 100% on you and you alone.</em></h3>";
    scrapMenu.appendChild(scrapDescription);

    let agreementButton = document.createElement("button");
    agreementButton.addEventListener("click", function () {
      pageLock = true;
      scrapMenu.removeChild(agreementButton);
      scrapDescription.textContent =
        "You can use this menu to quickly scrap items. There is no confirmation prompt. Make sure you actually want to scrap the items.";
      let itemBox = document.createElement("div");
      itemBox.classList.add("opElem");
      itemBox.style.display = "grid";
      itemBox.style.border = "1px solid grey";
      itemBox.style.backgroundColor = "black";
      itemBox.style.gridTemplateColumns = "repeat(1, 100%)";
      itemBox.style.top = "70px";
      itemBox.style.left = "8px";
      itemBox.style.right = "8px";
      itemBox.style.bottom = "8px";
      itemBox.style.gap = "10px";
      itemBox.style.padding = "5px 8px";
      itemBox.style.justifyContent = "center";
      itemBox.style.overflowY = "auto";
      itemBox.style.gridAutoRows = "max-content";

      for (let i = 1; i <= userVars["DFSTATS_df_invslots"]; i++) {
        if (!lockedSlots.includes("" + i))
          if (
            typeof userVars["DFSTATS_df_inv" + i + "_type"] !== "undefined" &&
            userVars["DFSTATS_df_inv" + i + "_type"].length > 0
          ) {
            let invItem = new InventoryItem(
              userVars["DFSTATS_df_inv" + i + "_type"]
            );
            if (typeof globalData[invItem.type] !== "undefined") {
              let fakeItem = document.createElement("div");
              fakeItem.classList.add("fakeItem");
              fakeItem.dataset.slot = i;
              fakeItem.dataset.type = userVars["DFSTATS_df_inv" + i + "_type"];
              fakeItem.dataset.quantity =
                userVars["DFSTATS_df_inv" + i + "_quantity"];
              fakeItem.style.position = "relative";
              fakeItem.style.height = "40px";
              fakeItem.style.backgroundRepeat = "no-repeat";
              fakeItem.style.backgroundPosition = "top 0px right 10px";
              if (
                typeof invItem.stats !== "undefined" &&
                typeof mcData[invItem.stats] !== "undefined"
              ) {
                fakeItem.style.border = "1px solid " + mcData[invItem.stats][0];
              } else {
                fakeItem.style.border = "1px solid rgba(64,64,64,0.8)";
              }

              let itemName = document.createElement("div");
              if (typeof invItem.name !== "undefined") {
                itemName.textContent = invItem.name;
              } else {
                itemName.textContent = globalData[invItem.type]["name"];
              }
              fakeItem.appendChild(itemName);

              if (
                globalData[invItem.type]["itemtype"] === "armour" ||
                (globalData[invItem.type]["itemtype"] === "item" &&
                  typeof globalData[invItem.type]["clothingtype"] !==
                    "undefined" &&
                  globalData[invItem.type]["clothingtype"].length > 0)
              ) {
                fakeItem.style.backgroundImage =
                  "url('https://files.deadfrontier.com/deadfrontier/inventoryimages/large/" +
                  invItem.type +
                  ".png')";
              } else {
                fakeItem.style.backgroundImage =
                  "url('https://files.deadfrontier.com/deadfrontier/inventoryimages/large/" +
                  pickItemImageSubStr(
                    userVars["DFSTATS_df_inv" + i + "_type"]
                  ) +
                  ".png')";
              }

              let scrapButton = document.createElement("button");
              scrapButton.classList.add("opElem");
              scrapButton.style.left = "4px";
              scrapButton.style.bottom = "4px";
              scrapButton.textContent = "Scrap";

              scrapButton.addEventListener("click", function (evt) {
                evt.currentTarget.parentNode.style.display = "none";
                for (let sBtn of itemBox.querySelectorAll("button")) {
                  sBtn.disabled = true;
                }
                playSound("shop_buysell");
                let dataArr = {
                  pagetime: userVars["pagetime"],
                  templateID: userVars["template_ID"],
                  sc: userVars["sc"],
                  creditsnum: 0,
                  buynum: 0,
                  renameto: "",
                  expected_itemprice: "-1",
                  expected_itemtype2: "",
                  expected_itemtype: evt.currentTarget.parentNode.dataset.type,
                  itemnum2: 0,
                  itemnum: evt.currentTarget.parentNode.dataset.slot,
                  price: scrapValue(
                    evt.currentTarget.parentNode.dataset.type,
                    evt.currentTarget.parentNode.dataset.quantity
                  ),
                  action: "scrap",
                  gv: 21,
                  userID: userVars["userID"],
                  password: userVars["password"],
                };

                webCall(
                  "inventory_new",
                  dataArr,
                  function (webData) {
                    updateIntoArr(flshToArr(webData, "DFSTATS_"), userVars);
                    populateInventory();
                    populateCharacterInventory();
                    updateAllFieldsBase();
                    for (let sBtn of itemBox.querySelectorAll("button")) {
                      sBtn.disabled = false;
                    }
                  },
                  true
                );
              });

              fakeItem.appendChild(scrapButton);

              let scrapValueDisplay = document.createElement("span");
              scrapValueDisplay.classList.add("opElem");
              scrapValueDisplay.classList.add("cashhack");
              scrapValueDisplay.style.left = "46px";
              scrapValueDisplay.style.bottom = "4px";
              let cashFromScrapping = nf.format(
                scrapValue(
                  userVars["DFSTATS_df_inv" + i + "_type"],
                  userVars["DFSTATS_df_inv" + i + "_quantity"]
                )
              );
              scrapValueDisplay.dataset.cash = "$" + cashFromScrapping;
              scrapValueDisplay.textContent = "$" + cashFromScrapping;
              fakeItem.appendChild(scrapValueDisplay);

              itemBox.appendChild(fakeItem);
            }
          }
      }
      scrapMenu.appendChild(itemBox);
    });
    agreementButton.textContent =
      "I HAVE READ AND AGREE TO THE STATEMENT ABOVE";
    agreementButton.disabled = true;
    scrapMenu.appendChild(agreementButton);
    setTimeout(function () {
      agreementButton.disabled = false;
    }, 3000);

    let closeScrapMenu = document.createElement("button");
    closeScrapMenu.classList.add("opElem");
    closeScrapMenu.style.top = "3px";
    closeScrapMenu.style.right = "3px";
    closeScrapMenu.innerHTML = "&#10006;";
    closeScrapMenu.addEventListener("click", function (evt) {
      pageLock = false;
      df_prompt.parentNode.removeChild(scrapMenu);
      df_prompt.style.display = "";
      promptEnd();
    });
    scrapMenu.appendChild(closeScrapMenu);

    df_prompt.style.display = "none";
    df_prompt.parentNode.appendChild(scrapMenu);
  });

  let enhanceMenuOpen = inventoryHolder.querySelector(
    "div[data-action='enhance']"
  );
  enhanceMenuOpen.addEventListener("click", function (evt) {
    promptLoading();
    let enhanceMenu = document.createElement("div");
    enhanceMenu.classList.add("genericActionBox");
    enhanceMenu.classList.add("opElem");
    enhanceMenu.style.inset = "50px";

    let enhanceTitle = document.createElement("div");
    enhanceTitle.style.fontFamily = "Downcome";
    enhanceTitle.style.color = "#990000";
    enhanceTitle.style.fontSize = "14pt";
    enhanceTitle.style.marginTop = "8px";
    enhanceTitle.textContent = "Enhance Items";
    enhanceMenu.appendChild(enhanceTitle);

    let enhanceDescription = document.createElement("div");
    enhanceDescription.style.marginTop = "8px";
    enhanceDescription.innerHTML =
      "By using this menu, <em>you</em> take full responsibility for any mistakes <em>you</em> make.<br /><br />";
    enhanceDescription.innerHTML +=
      "<em>You</em> are responsible for the safety of your items and <em>you</em> are responsible for using the tools provided like SlotLock.<br /><br />";
    enhanceDescription.innerHTML +=
      "By using this menu, <em>you</em> are willingly taking part in a dangerous action that can result in the alteration of important stats.<br /><br />";
    enhanceDescription.innerHTML +=
      "There is no way for <em>you</em> to revert stats. This is permanent.<br />";
    enhanceDescription.innerHTML +=
      "<h3><em>This is 100% on you and you alone.</em></h3>";
    enhanceMenu.appendChild(enhanceDescription);

    let agreementButton = document.createElement("button");
    agreementButton.addEventListener("click", function () {
      pageLock = true;
      enhanceMenu.removeChild(agreementButton);
      enhanceDescription.textContent =
        "You can use this menu to quickly enhance items. There is no confirmation prompt. Make sure you actually want to alter item stats.";
      let itemBox = document.createElement("div");
      itemBox.classList.add("opElem");
      itemBox.style.display = "grid";
      itemBox.style.border = "1px solid grey";
      itemBox.style.backgroundColor = "black";
      itemBox.style.gridTemplateColumns = "repeat(1, 100%)";
      itemBox.style.top = "70px";
      itemBox.style.left = "8px";
      itemBox.style.right = "8px";
      itemBox.style.bottom = "8px";
      itemBox.style.gap = "10px";
      itemBox.style.padding = "5px 8px";
      itemBox.style.justifyContent = "center";
      itemBox.style.overflowY = "auto";
      itemBox.style.gridAutoRows = "max-content";

      for (let i = 1; i <= userVars["DFSTATS_df_invslots"]; i++) {
        if (!lockedSlots.includes("" + i))
          if (
            typeof userVars["DFSTATS_df_inv" + i + "_type"] !== "undefined" &&
            userVars["DFSTATS_df_inv" + i + "_type"].length > 0
          ) {
            let invItem = new InventoryItem(
              userVars["DFSTATS_df_inv" + i + "_type"]
            );
            if (
              typeof globalData[invItem.type] !== "undefined" &&
              ((globalData[invItem.type]["itemtype"] === "weapon" &&
                (typeof invItem.stats === "undefined" ||
                  invItem.stats < 888)) ||
                (globalData[invItem.type]["itemtype"] === "armour" &&
                  (typeof invItem.stats === "undefined" ||
                    invItem.stats < 2424)) ||
                (globalData[invItem.type]["itemtype"] === "backpack" &&
                  (typeof invItem.stats === "undefined" ||
                    invItem.stats < 3))) &&
              enhanceValue(userVars["DFSTATS_df_inv" + i + "_type"]) <=
                userVars["DFSTATS_df_cash"]
            ) {
              let fakeItem = document.createElement("div");
              fakeItem.classList.add("fakeItem");
              fakeItem.dataset.slot = i;
              fakeItem.dataset.type = userVars["DFSTATS_df_inv" + i + "_type"];
              fakeItem.dataset.quantity =
                userVars["DFSTATS_df_inv" + i + "_quantity"];
              fakeItem.style.position = "relative";
              fakeItem.style.height = "40px";
              fakeItem.style.backgroundRepeat = "no-repeat";
              fakeItem.style.backgroundPosition = "top 0px right 10px";
              if (
                typeof invItem.stats !== "undefined" &&
                typeof mcData[invItem.stats] !== "undefined"
              ) {
                fakeItem.style.border = "1px solid " + mcData[invItem.stats][0];
              } else {
                fakeItem.style.border = "1px solid rgba(64,64,64,0.8)";
              }

              let itemName = document.createElement("div");
              if (typeof invItem.name !== "undefined") {
                itemName.textContent = invItem.name;
              } else {
                itemName.textContent = globalData[invItem.type]["name"];
              }
              fakeItem.appendChild(itemName);

              if (
                globalData[invItem.type]["itemtype"] === "armour" ||
                (globalData[invItem.type]["itemtype"] === "item" &&
                  typeof globalData[invItem.type]["clothingtype"] !==
                    "undefined" &&
                  globalData[invItem.type]["clothingtype"].length > 0)
              ) {
                fakeItem.style.backgroundImage =
                  "url('https://files.deadfrontier.com/deadfrontier/inventoryimages/large/" +
                  invItem.type +
                  ".png')";
              } else {
                fakeItem.style.backgroundImage =
                  "url('https://files.deadfrontier.com/deadfrontier/inventoryimages/large/" +
                  pickItemImageSubStr(
                    userVars["DFSTATS_df_inv" + i + "_type"]
                  ) +
                  ".png')";
              }

              let currentStats = document.createElement("div");
              currentStats.classList.add("opElem");
              currentStats.style.left = "4px";
              currentStats.style.top = "4px";
              currentStats.dataset.type = "curstats";

              let statString = [];
              if (typeof invItem.stats !== "undefined")
                switch (globalData[invItem.type]["itemtype"]) {
                  case "weapon":
                    statString = invItem.stats.split("");
                    currentStats.textContent = statString.join("/");
                    break;
                  case "armour":
                    statString = invItem.stats.match(/.{1,2}/g);
                    currentStats.textContent = statString.join("/");
                    break;
                  case "backpack":
                    currentStats.textContent = invItem.stats;
                    break;
                }
              fakeItem.appendChild(currentStats);

              let enhanceButton = document.createElement("button");
              enhanceButton.classList.add("opElem");
              enhanceButton.style.left = "4px";
              enhanceButton.style.bottom = "4px";
              enhanceButton.textContent = "Enhance";

              enhanceButton.addEventListener("click", function (evt) {
                for (let sBtn of itemBox.querySelectorAll("button")) {
                  sBtn.disabled = true;
                }
                playSound("shop_buysell");
                let dataArr = {
                  pagetime: userVars["pagetime"],
                  templateID: userVars["template_ID"],
                  sc: userVars["sc"],
                  creditsnum: 0,
                  buynum: 0,
                  renameto: "",
                  expected_itemprice: "-1",
                  expected_itemtype2: "",
                  expected_itemtype: evt.currentTarget.parentNode.dataset.type,
                  itemnum2: 0,
                  itemnum: evt.currentTarget.parentNode.dataset.slot,
                  price: enhanceValue(
                    evt.currentTarget.parentNode.dataset.type,
                    evt.currentTarget.parentNode.dataset.quantity
                  ),
                  action: "enhance",
                  gv: 21,
                  userID: userVars["userID"],
                  password: userVars["password"],
                };

                webCall(
                  "inventory_new",
                  dataArr,
                  function (webData) {
                    let updateArr = flshToArr(webData, "DFSTATS_");
                    updateIntoArr(updateArr, userVars);
                    populateInventory();
                    populateCharacterInventory();

                    // evt.currentTarget.parentNode.dataset.slot
                    let targetSlot = 0;
                    for (let i = 0; i <= userVars["DFSTATS_df_invslots"]; i++) {
                      if (
                        typeof updateArr["DFSTATS_df_inv" + i + "_type"] !==
                        "undefined"
                      ) {
                        targetSlot = i;
                        break;
                      }
                    }
                    df_prompt.parentNode.querySelector(
                      'div[data-slot="' + targetSlot + '"]'
                    ).dataset.type =
                      userVars["DFSTATS_df_inv" + targetSlot + "_type"];
                    let currentStats = df_prompt.parentNode.querySelector(
                      'div[data-slot="' +
                        targetSlot +
                        '"] [data-type="curstats"]'
                    );
                    let updatedItem = new InventoryItem(
                      userVars["DFSTATS_df_inv" + targetSlot + "_type"]
                    );

                    if (
                      (globalData[updatedItem.type]["itemtype"] === "weapon" &&
                        (typeof updatedItem.stats === "undefined" ||
                          updatedItem.stats < 888)) ||
                      (globalData[updatedItem.type]["itemtype"] === "armour" &&
                        (typeof updatedItem.stats === "undefined" ||
                          updatedItem.stats < 2424)) ||
                      (globalData[updatedItem.type]["itemtype"] ===
                        "backpack" &&
                        (typeof updatedItem.stats === "undefined" ||
                          updatedItem.stats < 3))
                    ) {
                      switch (globalData[invItem.type]["itemtype"]) {
                        case "weapon":
                          statString = updatedItem.stats.split("");
                          currentStats.textContent = statString.join("/");
                          break;
                        case "armour":
                          statString = updatedItem.stats.match(/.{1,2}/g);
                          currentStats.textContent = statString.join("/");
                          break;
                        case "backpack":
                          currentStats.textContent = updatedItem.stats;
                          break;
                      }
                    } else {
                      currentStats.parentNode.style.display = "none";
                    }

                    updateAllFieldsBase();
                    for (let sBtn of itemBox.querySelectorAll("button")) {
                      if (
                        enhanceValue(
                          userVars[
                            "DFSTATS_df_inv" +
                              sBtn.parentNode.dataset.slot +
                              "_type"
                          ]
                        ) <= userVars["DFSTATS_df_cash"]
                      ) {
                        sBtn.disabled = false;
                      } else {
                        sBtn.parentNode.style.display = "none";
                      }
                    }
                  },
                  true
                );
              });

              fakeItem.appendChild(enhanceButton);

              let enhanceValueDisplay = document.createElement("span");
              enhanceValueDisplay.classList.add("opElem");
              enhanceValueDisplay.classList.add("cashhack");
              enhanceValueDisplay.style.left = "66px";
              enhanceValueDisplay.style.bottom = "4px";
              let cashFromEnhancing = nf.format(
                enhanceValue(userVars["DFSTATS_df_inv" + i + "_type"])
              );
              enhanceValueDisplay.dataset.cash = "$" + cashFromEnhancing;
              enhanceValueDisplay.textContent = "$" + cashFromEnhancing;
              fakeItem.appendChild(enhanceValueDisplay);

              itemBox.appendChild(fakeItem);
            }
          }
      }
      enhanceMenu.appendChild(itemBox);
    });
    agreementButton.textContent =
      "I HAVE READ AND AGREE TO THE STATEMENT ABOVE";
    agreementButton.disabled = true;
    enhanceMenu.appendChild(agreementButton);
    setTimeout(function () {
      agreementButton.disabled = false;
    }, 1000);

    let closeEnhanceMenu = document.createElement("button");
    closeEnhanceMenu.classList.add("opElem");
    closeEnhanceMenu.style.top = "3px";
    closeEnhanceMenu.style.right = "3px";
    closeEnhanceMenu.innerHTML = "&#10006;";
    closeEnhanceMenu.addEventListener("click", function (evt) {
      pageLock = false;
      df_prompt.parentNode.removeChild(enhanceMenu);
      df_prompt.style.display = "";
      promptEnd();
    });
    enhanceMenu.appendChild(closeEnhanceMenu);

    df_prompt.style.display = "none";
    df_prompt.parentNode.appendChild(enhanceMenu);
  });
}

function lockSlotPrompt() {
  pageLock = true;
  df_prompt.style.height = "125px";
  df_prompt.innerHTML =
    "<span>SlotLock will protect items in your locked slots from accidental scrapping and disposal! Items can still be moved into and out of locked slots.</span>";

  var noButton = document.createElement("button");

  noButton.style.position = "absolute";
  noButton.style.bottom = "12px";
  noButton.addEventListener("click", function () {
    df_prompt.parentNode.style.display = "none";
    df_prompt.innerHTML = "";
    df_prompt.style.height = "";
    pageLock = false;
  });
  noButton.textContent = "close";
  noButton.style.right = "12px";
  df_prompt.onkeydown = function (e) {
    if (e.keyCode === 13) {
      noButton.click();
    }
  };

  var explainButton = document.createElement("button");
  explainButton.style.position = "absolute";
  explainButton.style.left = "12px";
  explainButton.style.bottom = "12px";
  explainButton.textContent = "how to use?";
  explainButton.addEventListener("click", function () {
    df_prompt.querySelector("span").innerHTML =
      "Simply hold <span style='color: #ff0000;'>[ctrl]</span> while clicking an inventory slot you wish to lock! To unlock, just hold <span style='color: #ff0000;'>[ctrl]</span> and click the locked inventory slot.";
    explainButton.addEventListener("click", lockSlotPrompt);
    explainButton.textContent = "back";
  });

  df_prompt.appendChild(noButton);
  df_prompt.appendChild(explainButton);
  df_prompt.parentNode.style.display = "block";
  df_prompt.focus();
}

function enhanceValue(itemType) {
  var itemData = itemType.trim().split("_");
  var tlevel = 0;
  var amount = false;
  if (globalData[itemData[0]]["enhancecost"]) {
    amount = globalData[itemData[0]]["enhancecost"];
  } else {
    if (
      globalData[itemData[0]]["itemtype"] === "weapon" ||
      globalData[itemData[0]]["itemtype"] === "armour" ||
      globalData[itemData[0]]["itemtype"] === "backpack"
    ) {
      amount = scrapValue(itemData[0]) * 2.2;
    }
    /*
		switch(globalData[itemData[0]]["itemcat"])
		{
			case "weapon":
				tlevel = globalData[itemData[0]]["pro_req"];
				amount = (tlevel * 500) + 1000;
				break;
			case "armour":
				tlevel = globalData[itemData[0]]["find_level"];
				amount = (tlevel * 1000) + 2000;
				break;
			case "backpack":
				tlevel = globalData[itemData[0]]["slots"];
				amount = (tlevel * 500) + 1000;
				break;
		}
		*/
  }

  return amount;
}

function dyeValue(itemType) {
  var itemData = itemType.trim().split("_");
  var tlevel = 0;
  var amount = false;
  switch (globalData[itemData[0]]["itemtype"]) {
    case "weapon":
      if (
        typeof globalData[itemData[0]]["othercolours"] !== "undefined" &&
        globalData[itemData[0]]["othercolours"] !== ""
      ) {
        tlevel = parseInt(globalData[itemData[0]]["pro_req"]);
        amount = tlevel * 500 + 1000;
      }
      break;
    case "armour":
      if (
        typeof globalData[itemData[0]]["othercolours"] !== "undefined" &&
        globalData[itemData[0]]["othercolours"] !== ""
      ) {
        tlevel = globalData[itemData[0]]["find_level"];
        amount = tlevel * 1000 + 2000;
      }
      break;
    case "item":
      if (
        typeof globalData[itemData[0]]["othercolours"] !== "undefined" &&
        globalData[itemData[0]]["othercolours"] !== "" &&
        globalData[itemData[0]]["clothingtype"] &&
        globalData[itemData[0]]["clothtype"] !== ""
      ) {
        tlevel = parseInt(globalData[itemData[0]]["level"]);
        amount = tlevel * 500 + 1000;
      }
      break;
  }
  return amount;
}

function scrapValue(itemType, quantity) {
  var itemData = itemType.trim().split("_");
  var tlevel = 0;
  var amount = false;
  if (typeof globalData[itemData[0]] === "undefined") {
    console.log(itemType);
  }
  if (
    typeof globalData[itemData[0]]["scrapvalue"] !== "undefined" &&
    globalData[itemData[0]]["scrapvalue"] > 0
  ) {
    amount = globalData[itemData[0]]["scrapvalue"];
    if (itemType.indexOf("_stats") >= 0) {
      amount *= 2;
    }
  } else {
    switch (globalData[itemData[0]]["itemtype"]) {
      case "weapon":
        tlevel = globalData[itemData[0]]["pro_req"];
        if (
          typeof globalData[itemData[0]]["melee"] !== "undefined" &&
          globalData[itemData[0]]["melee"] > 0
        ) {
          amount =
            Math.round((tlevel * tlevel * Math.round(tlevel / 40 + 1)) / 2) +
            50;
        } else {
          amount = tlevel * tlevel * Math.round(tlevel / 40 + 1) + 50;
        }
        if (itemType.indexOf("_stats") >= 0) {
          amount *= 2;
        }
        break;
      case "armour":
        tlevel = globalData[itemData[0]]["shop_level"] * 2;
        amount = tlevel * tlevel * Math.round(tlevel / 40 + 1) + 250;
        if (itemType.indexOf("_stats") >= 0) {
          amount *= 2;
        }
        break;
      case "ammo":
        amount = Math.round(
          globalData[itemData[0]]["amountper"] * quantity * 2
        );
        break;
      case "credits":
        amount = Math.round(100 * quantity);
        break;
      case "item":
        tlevel = parseInt(globalData[itemData[0]]["level"]);

        if (globalData[itemData[0]]["implant"] === true) {
          amount =
            (Math.round((tlevel * tlevel * Math.round(tlevel / 20 + 1)) / 2) +
              50) *
            4;
        } else if (
          globalData[itemData[0]]["clothingtype"] &&
          globalData[itemData[0]]["clothtype"] !== ""
        ) {
          amount =
            Math.round((tlevel * tlevel * Math.round(tlevel / 20 + 1)) / 2) +
            50;
        } else {
          amount =
            Math.round((tlevel * tlevel * Math.round(tlevel / 20 + 1)) / 20) +
            5;
        }
        break;
      case "backpack":
        amount = 300 * parseInt(globalData[itemData[0]]["slots"]);
        break;
    }
  }
  return amount;
}

function scrapAmount(itemType, quantity) {
  var itemData = itemType.trim().split("_");
  var tlevel = 0;
  var amount = false;
  switch (globalData[itemData[0]]["itemtype"]) {
    case "weapon":
      tlevel = globalData[itemData[0]]["pro_req"];
      if (globalData[itemData[0]]["melee"] !== "0") {
        amount = Math.round((tlevel * tlevel) / 2) + 50;
        break;
      } else {
        amount = tlevel * tlevel + 50;
        break;
      }
      if (itemType.indexOf("_stats") >= 0) {
        amount *= 2;
      }
      break;
    case "armour":
      tlevel = globalData[itemData[0]]["shop_level"];
      amount = tlevel * tlevel + 250;
      if (itemType.indexOf("_stats") >= 0) {
        amount *= 2;
      }
      break;
    case "ammo":
      amount = Math.round(globalData[itemData[0]]["amountper"] * quantity);
      break;
    case "item":
      tlevel = parseInt(globalData[itemData[0]]["level"]);
      amount = Math.round(tlevel / 2);
      break;
  }
  if (amount < 1) {
    amount = 1;
  }
  return amount;
}

function openCategories(e) {
  var categoryHolder = document.getElementById("categoryList");
  var categoryChoice = document.getElementById("categoryChoice");
  if (menuOpen) {
    // close menu
    menuOpen = false;
    categoryHolder.style.display = "none";
    if (
      categoryChoice !== e.target &&
      categoryChoice !== e.target.parentElement
    ) {
      categoryChoice.querySelector("#cat").textContent = e.target.textContent;
      categoryChoice.dataset.catname = e.target.dataset.catname;
    }
    categoryChoice.querySelector("#dog").textContent = "\u25C4";
    if (e.target.dataset.cattype) {
      categoryChoice.dataset.cattype = e.target.dataset.cattype;
    } else {
      categoryChoice.dataset.cattype = "";
    }
  } else {
    // open menu
    menuOpen = true;
    categoryChoice.querySelector("#dog").textContent = "\u25BC";
    categoryHolder.style.display = "block";
  }
  if (categoryHolder.dataset.catname !== "") {
    if (document.getElementById("makeSearch")) {
      document.getElementById("makeSearch").disabled = false;
    }
  } else {
    if (document.getElementById("searchField").value.length > 0) {
      if (document.getElementById("makeSearch")) {
        document.getElementById("makeSearch").disabled = false;
      }
    } else {
      if (document.getElementById("makeSearch")) {
        document.getElementById("makeSearch").disabled = true;
      }
    }
  }
  document.getElementById("searchField").focus();
}

var craftCycleInterval;

function initiateCrafting() {
  clearInterval(craftCycleInterval);
  var craftHolder = document.getElementById("crafting");
  var craftCategories = [];
  var types = ["name", "price", "canCraft"];
  craftHolder.innerHTML =
    "<div style='display: inline-block; margin-left: 2px;'>Craftable Item</div>";
  craftHolder.innerHTML +=
    "<div style='display: inline-block; margin-left: 139px;'>Cost</div>";
  var recipes = document.createElement("div");
  recipes.id = "recipes";
  var craftCycler = [];
  var cyclesPast = 0;
  craftCycleInterval = setInterval(function () {
    for (rowToCycle of craftCycler) {
      var itemsToCycle = rowToCycle.dataset.items.split(",");
      rowToCycle.dataset.type = itemsToCycle[cyclesPast % itemsToCycle.length];
      if (rowToCycle.dataset.curHover === "1") {
        var fakeEvt = {};
        fakeEvt.target = rowToCycle;
        infoCard(fakeEvt, true);
      }
    }
    cyclesPast++;
  }, 1000);
  for (var i = 0; i < userVars["DFSTATS_total_blueprints"]; i++) {
    var row = document.createElement("div");
    row.classList.add("fakeItem");
    if (
      userVars["DFSTATS_blueprints_" + i + "_craftItem"].indexOf(",") !== -1
    ) {
      var canCraftThis = true;
      var craftingResults =
        userVars["DFSTATS_blueprints_" + i + "_craftItem"].split(",");
      for (var craftingResult of craftingResults) {
        if (
          typeof craftingResult === "undefined" ||
          typeof globalData[craftingResult] === "undefined"
        ) {
          canCraftThis = false;
          break;
        }
      }
      if (!canCraftThis) {
        continue;
      }
      craftCycler.push(row);

      /*
			for(var craftingResult of craftingResults)
			{
				row.dataset.desc = 
				row.dataset.price = 
				globalData[craftingResult]["requiredItemsDesc"] = userVars["DFSTATS_blueprints_" + i + "_requiredItemsDesc"];
				globalData[craftingResult]["craftPrice"] = "$" + nf.format(userVars["DFSTATS_blueprints_" + i + "_price"]);
			}
			*/

      row.dataset.type = craftingResult;
      row.dataset.items = userVars["DFSTATS_blueprints_" + i + "_craftItem"];
      row.dataset.curHover = "0";
      row.addEventListener("mouseenter", function (evt) {
        evt.currentTarget.dataset.curHover = "1";
      });
      row.addEventListener("mouseleave", function (evt) {
        evt.currentTarget.dataset.curHover = "0";
      });
    } else {
      /*
			if(typeof userVars["DFSTATS_blueprints_" + i + "_craftItem"] === "undefined" || typeof globalData[userVars["DFSTATS_blueprints_" + i + "_craftItem"]] === "undefined")
			{
				continue;
			}
			globalData[userVars["DFSTATS_blueprints_" + i + "_craftItem"]]["requiredItemsDesc"] = userVars["DFSTATS_blueprints_" + i + "_requiredItemsDesc"];
			globalData[userVars["DFSTATS_blueprints_" + i + "_craftItem"]]["craftPrice"] = "$" + nf.format(userVars["DFSTATS_blueprints_" + i + "_price"]);
			*/

      row.dataset.type = userVars["DFSTATS_blueprints_" + i + "_craftItem"];
    }

    if (userVars["DFSTATS_blueprints_" + i + "_category"]) {
      if (
        craftCategories.indexOf(
          userVars["DFSTATS_blueprints_" + i + "_category"]
        ) === -1
      ) {
        craftCategories.push(userVars["DFSTATS_blueprints_" + i + "_category"]);
      }
    } else {
      if (craftCategories.indexOf("Misc") === -1) {
        craftCategories.push("Misc");
      }
    }
    row.dataset.result = userVars["DFSTATS_blueprints_" + i + "_type"];
    row.dataset.name = userVars["DFSTATS_blueprints_" + i + "_craftItemName"];
    row.dataset.price =
      "$" + nf.format(userVars["DFSTATS_blueprints_" + i + "_price"]);
    row.dataset.desc =
      userVars["DFSTATS_blueprints_" + i + "_requiredItemsDesc"];
    //if(globalData[userVars["DFSTATS_blueprints_" + i + "_craftItem"]]["hp"] !== "")
    //{
    //	row.dataset.quantity = globalData[userVars["DFSTATS_blueprints_" + i + "_craftItem"]]["hp"];
    //}
    row.dataset.quantity =
      userVars["DFSTATS_blueprints_" + i + "_craftItemQuantity"];
    row.dataset.category = userVars["DFSTATS_blueprints_" + i + "_category"];
    for (var o = 0; o < 3; o++) {
      var column;
      switch (types[o]) {
        case "name":
          column = document.createElement("div");
          column.textContent =
            userVars["DFSTATS_blueprints_" + i + "_craftItemName"];
          column.style.width = "250px";
          column.style.paddingLeft = "5px";
          break;
        case "price":
          column = document.createElement("div");
          column.textContent =
            "$" + nf.format(userVars["DFSTATS_blueprints_" + i + "_price"]);
          column.style.width = "125px";
          column.style.color = "#FFFF00";
          break;
        case "canCraft":
          column = document.createElement("button");
          column.textContent = "craft";
          // /DF3D/DF3D_Crafting.php
          if (userVars["DFSTATS_blueprints_" + i + "_canCraft"] > 0) {
            column.disabled = false;
            column.addEventListener("click", inventoryAction);
            column.dataset.action = "craft";
          } else {
            column.disabled = true;
          }
          break;
      }
      column.classList.add("listItem");
      row.appendChild(column);
    }
    if (
      (searchTerm === "" ||
        row.childNodes[0].textContent.toLowerCase().indexOf(searchTerm) !==
          -1) &&
      (activeCat === "Everything" || row.dataset.category === activeCat)
    ) {
      row.style.display = "block";
    } else {
      row.style.display = "none";
    }
    recipes.appendChild(row);
  }
  craftHolder.appendChild(recipes);

  var craftSearch = document.createElement("input");
  craftSearch.id = "searchField";
  craftSearch.classList.add("opElem");
  craftSearch.style.top = "-20px";
  craftSearch.style.left = "0";
  craftSearch.placeholder = "Search...";
  craftSearch.style.color = "yellow";
  craftSearch.value = searchTerm;

  craftHolder.appendChild(craftSearch);

  var categorySelector = document.createElement("div");
  categorySelector.id = "categoryChoice";
  categorySelector.style.position = "absolute";
  categorySelector.style.top = "-20px";
  categorySelector.style.right = "0";
  categorySelector.dataset.catname = "";
  categorySelector.innerHTML =
    "<span id='cat'>" +
    activeCat +
    "</span><span id='dog' style='float: right;'>&#9668;</span>";

  categorySelector.addEventListener("click", openCategories);

  craftHolder.appendChild(categorySelector);

  var categoryList = document.createElement("div");
  categoryList.id = "categoryList";
  categoryList.style.height = "140px";
  categoryList.style.width = "240px";

  craftCategories.sort(function (a, b) {
    if (a === "Misc") {
      return 1;
    } else if (b === "Misc") {
      return -1;
    } else {
      if (a < b) {
        return -1;
      } else if (b < a) {
        return 1;
      } else {
        return 0;
      }
    }
  });
  craftCategories.unshift("Everything");
  for (var i of craftCategories) {
    var category = document.createElement("div");
    category.dataset.catname = i;
    category.textContent = i;

    categoryList.appendChild(category);
  }

  var craftElems = recipes.childNodes;

  for (let catListChild of categoryList.querySelectorAll("div")) {
    catListChild.addEventListener("click", function (e) {
      if (e.currentTarget.dataset.catname) {
        activeCat = e.currentTarget.dataset.catname;
        for (var i = 0; i < craftElems.length; i++) {
          if (
            (searchTerm === "" ||
              craftElems[i].childNodes[0].textContent
                .toLowerCase()
                .indexOf(searchTerm) !== -1) &&
            (activeCat === "Everything" ||
              craftElems[i].dataset.category === activeCat)
          ) {
            craftElems[i].style.display = "block";
          } else {
            craftElems[i].style.display = "none";
          }
        }
      }
    });
  }

  categorySelector.appendChild(categoryList);

  craftSearch.oninput = function (e) {
    searchTerm = e.currentTarget.value.toLowerCase();
    for (var i = 0; i < craftElems.length; i++) {
      if (
        (searchTerm === "" ||
          craftElems[i].childNodes[0].textContent
            .toLowerCase()
            .indexOf(searchTerm) !== -1) &&
        (activeCat === "Everything" ||
          craftElems[i].dataset.category === activeCat)
      ) {
        craftElems[i].style.display = "block";
      } else {
        craftElems[i].style.display = "none";
      }
    }
  };

  /*var charElem = document.getElementById("character").querySelector("div");
	charElem.innerHTML = "";
	charElem.textContent = "";
	var slotTypes = [["armour", 20, 340, 34]];

	for(var i in slotTypes)
	{
		var holder = document.createElement("div");
		holder.classList.add("opElem");
		holder.dataset.slottype = slotTypes[i][0];
		holder.dataset.slot = slotTypes[i][3];
		holder.style.left = slotTypes[i][1] + "px";
		holder.style.top = slotTypes[i][2] + "px";
		holder.classList.add("validSlot");
		charElem.appendChild(holder);
		var slotText = document.createElement("div");
		var actualText = slotTypes[i][0].charAt(0).toUpperCase() + slotTypes[i][0].slice(1);
		slotText.classList.add("opElem");
		slotText.classList.add("cashHack");
		slotText.classList.add("credits");
		slotText.dataset.cash = actualText;
		slotText.style.left = slotTypes[i][1] - 10 + "px";
		slotText.style.top = slotTypes[i][2] + 46 + "px";
		slotText.style.width = "64px";
		slotText.textContent = actualText;
		charElem.appendChild(slotText);
	}*/
  //populateCharacterInventory();
}

function initiateRewards() {
  let rewardsHolder = document.getElementById("rewards");
  rewardsHolder.innerHTML =
    "<div style='display: inline-block; margin-left: 2px;'>Reward</div>";
  rewardsHolder.innerHTML +=
    "<div style='display: inline-block; margin-left: 139px;'>Quantity</div>";
  let itemList = document.createElement("div");
  itemList.classList.add("itemList");
  for (let i = 0; i < userVars["REWARDS_totalrewards"]; i++) {
    let item = new InventoryItem(userVars[`REWARDS_reward${i}_type`]);

    let row = document.createElement("div");
    row.classList.add("fakeItem");
    row.dataset.type = userVars["REWARDS_reward" + i + "_type"];
    row.dataset.quantity = userVars["REWARDS_reward" + i + "_quantity"];

    row.dataset.desc = userVars["REWARDS_reward" + i + "_description"];

    let nameColumn = document.createElement("div");
    nameColumn.textContent =
      typeof item.name !== "undefined"
        ? item.name
        : globalData[item.type]["name"];
    nameColumn.style.width = "250px";
    nameColumn.style.paddingLeft = "5px";
    nameColumn.classList.add("listItem");
    row.appendChild(nameColumn);

    let quantityColumn = document.createElement("div");
    quantityColumn.textContent = nf.format(
      userVars["REWARDS_reward" + i + "_quantity"]
    );
    quantityColumn.style.width = "125px";
    quantityColumn.classList.add("listItem");
    row.appendChild(quantityColumn);

    let claimColumn = document.createElement("button");
    claimColumn.textContent = "claim";
    if (findFirstEmptyGenericSlot("inv") > 0) {
      claimColumn.disabled = false;
      claimColumn.addEventListener("click", inventoryAction);
      claimColumn.dataset.action = "claimreward";
    } else {
      claimColumn.disabled = true;
    }
    claimColumn.classList.add("listItem");
    row.appendChild(claimColumn);

    if (
      (searchTerm === "" ||
        row.childNodes[0].textContent.toLowerCase().indexOf(searchTerm) !==
          -1) &&
      (activeCat === "Everything" || row.dataset.category === activeCat)
    ) {
      row.style.display = "block";
    } else {
      row.style.display = "none";
    }
    itemList.appendChild(row);
  }
  rewardsHolder.appendChild(itemList);

  var craftSearch = document.createElement("input");
  craftSearch.id = "searchField";
  craftSearch.classList.add("opElem");
  craftSearch.style.top = "-20px";
  craftSearch.style.left = "0";
  craftSearch.placeholder = "Search...";
  craftSearch.style.color = "yellow";
  craftSearch.value = searchTerm;

  rewardsHolder.appendChild(craftSearch);

  var categoryList = document.createElement("div");
  categoryList.id = "categoryList";
  categoryList.style.height = "140px";
  categoryList.style.width = "240px";

  var craftElems = itemList.childNodes;

  craftSearch.oninput = function (e) {
    searchTerm = e.currentTarget.value.toLowerCase();
    for (var i = 0; i < craftElems.length; i++) {
      if (
        (searchTerm === "" ||
          craftElems[i].childNodes[0].textContent
            .toLowerCase()
            .indexOf(searchTerm) !== -1) &&
        (activeCat === "Everything" ||
          craftElems[i].dataset.category === activeCat)
      ) {
        craftElems[i].style.display = "block";
      } else {
        craftElems[i].style.display = "none";
      }
    }
  };
  populateCharacterInventory();
}

function initiateCollectionBook() {
  collectionBook = document.getElementById("collectionbook");
  collectionBook.textContent = "Loading...";

  itemCategories.unshift("Pinned");
  var nonWeaponStart = itemCategories.push("Implant") - 1;
  itemCategories.push("Backpack");
  itemCategories.push("Ammo");
  itemCategories.push("Armour");
  itemCategories.push("Clothing");
  itemCategories.push("Blueprints");
  itemCategories.push("Nourishment");
  itemCategories.push("Medical");
  itemCategories.push("Boosts");
  itemCategories.push("Misc");

  var categorySwitcher = document.createElement("div");
  categorySwitcher.id = "cbSwitcher";

  for (var i = 0; i < itemCategories.length; i++) {
    var categoryBtn = document.createElement("button");
    if (i === 0) {
      categoryBtn.disabled = true;
    }
    categoryBtn.textContent = itemCategories[i];
    categoryBtn.onclick = function (evt) {
      evt.preventDefault();
      for (let cbBtn of document.querySelectorAll("#cbSwitcher button")) {
        cbBtn.disabled = false;
      }
      evt.currentTarget.disabled = true;
      cbCategory = evt.currentTarget.textContent;
      if (
        itemCategories.indexOf(cbCategory) > 0 &&
        itemCategories.indexOf(cbCategory) < nonWeaponStart
      ) {
        cbCategoryType = cbCategory;
        cbCategory = "Weapon";
      }
      collectionBook.scrollTop = 0;
      reloadCollectionBook();
    };

    categorySwitcher.appendChild(categoryBtn);
  }

  inventoryHolder.appendChild(categorySwitcher);

  var dataArr = {};
  dataArr["userID"] = userVars["userID"];
  dataArr["password"] = userVars["password"];
  dataArr["sc"] = userVars["sc"];
  dataArr["pagetime"] = userVars["pagetime"];

  if (userVars["memberto"]) {
    dataArr["memberto"] = userVars["memberto"];
    dataArr["action"] = "getother";
  } else {
    dataArr["action"] = "get";
    collectionBook.dataset.action = "cbadd";
  }

  if (userVars["member_to_name"]) {
    if (
      userVars["member_to_name"][userVars["member_to_name"].length - 1] === "s"
    ) {
      document.getElementById("pageLogo").textContent =
        userVars["member_to_name"] + "' Collection Book";
    } else {
      document.getElementById("pageLogo").textContent =
        userVars["member_to_name"] + "'s Collection Book";
    }
  }

  webCall(
    "hotrods/collectionbook",
    dataArr,
    function (data) {
      cbContent = flshToArr(data);

      if (userVars["memberto"] === userVars["userID"]) {
        var myOwnPageBtn = document.createElement("a");
        myOwnPageBtn.textContent = "Edit Collection Book";
        myOwnPageBtn.href = "index.php?page=82";
        myOwnPageBtn.classList.add("opElem");
        myOwnPageBtn.style.left = "5px";
        myOwnPageBtn.style.top = "5px";

        inventoryHolder.appendChild(myOwnPageBtn);
      }
      reloadCollectionBook();
    },
    true
  );
}

function reloadCollectionBook() {
  var canShowItem = function (itemType, isPinned) {
    if (typeof globalData[itemType] === "undefined") {
      console.log(itemType);
    }
    switch (cbCategory) {
      default:
        if (!isPinned) {
          return false;
        }
        break;
      case "Weapon":
        if (
          globalData[itemType]["itemtype"] !== "weapon" ||
          globalData[itemType]["weptype"] !== cbCategoryType
        ) {
          return false;
        }
        break;
      case "Backpack":
        if (globalData[itemType]["itemtype"] !== "backpack") {
          return false;
        }
        break;
      case "Implant":
        if (globalData[itemType]["implant"] !== true) {
          return false;
        }
        break;
      case "Ammo":
        if (globalData[itemType]["itemtype"] !== "ammo") {
          return false;
        }
        break;
      case "Armour":
        if (globalData[itemType]["itemtype"] !== "armour") {
          return false;
        }
        break;
      case "Clothing":
        if (
          typeof globalData[itemType]["clothingtype"] !== "string" ||
          globalData[itemType]["clothingtype"].length === 0
        ) {
          return false;
        }
        break;
      case "Blueprints":
        if (!/blueprints$/.test(itemType)) {
          return false;
        }
        break;
      case "Nourishment":
        if (
          typeof globalData[itemType]["foodrestore"] === "undefined" ||
          globalData[itemType]["foodrestore"] === 0
        ) {
          return false;
        }
        break;
      case "Medical":
        if (
          typeof globalData[itemType]["healthrestore"] === "undefined" ||
          globalData[itemType]["healthrestore"] === 0
        ) {
          return false;
        }
        break;
      case "Boosts":
        //if (typeof globalData[itemType]["boostexphours"] === "undefined" || globalData[itemType]["boostexphours"] === "0")
        var boostCheck = false;
        if (
          typeof globalData[itemType]["boostdamagehours"] !== "undefined" &&
          globalData[itemType]["boostdamagehours"] != 0
        ) {
          boostCheck = true;
        }
        if (
          typeof globalData[itemType]["boostexphours"] !== "undefined" &&
          globalData[itemType]["boostexphours"] != 0
        ) {
          boostCheck = true;
        }
        if (
          typeof globalData[itemType]["boostspeedhours"] !== "undefined" &&
          globalData[itemType]["boostspeedhours"] != 0
        ) {
          boostCheck = true;
        }
        if (
          typeof globalData[itemType]["boostdamagehours_ex"] !== "undefined" &&
          globalData[itemType]["boostdamagehours_ex"] != 0
        ) {
          boostCheck = true;
        }
        if (
          typeof globalData[itemType]["boostexphours_ex"] !== "undefined" &&
          globalData[itemType]["boostexphours_ex"] != 0
        ) {
          boostCheck = true;
        }
        if (
          typeof globalData[itemType]["boostspeedhours_ex"] !== "undefined" &&
          globalData[itemType]["boostspeedhours_ex"] != 0
        ) {
          boostCheck = true;
        }
        if (!boostCheck) {
          return boostCheck;
        }
        break;
      case "Misc":
        if (globalData[itemType]["itemtype"] === "weapon") {
          return false;
        }
        if (globalData[itemType]["itemtype"] === "backpack") {
          return false;
        }
        if (globalData[itemType]["implant"] === true) {
          return false;
        }
        if (globalData[itemType]["itemtype"] === "ammo") {
          return false;
        }
        if (globalData[itemType]["itemtype"] === "armour") {
          return false;
        }
        if (
          typeof globalData[itemType]["clothingtype"] === "string" &&
          globalData[itemType]["clothingtype"].length > 0
        ) {
          return false;
        }
        if (/blueprints$/.test(itemType)) {
          return false;
        }
        if (
          typeof globalData[itemType]["foodrestore"] !== "undefined" &&
          globalData[itemType]["foodrestore"] !== 0
        ) {
          return false;
        }
        if (
          typeof globalData[itemType]["healthrestore"] !== "undefined" &&
          globalData[itemType]["healthrestore"] !== 0
        ) {
          return false;
        }
        if (
          typeof globalData[itemType]["boostdamagehours"] !== "undefined" &&
          globalData[itemType]["boostdamagehours"] !== 0
        ) {
          boostCheck = false;
        }
        if (
          typeof globalData[itemType]["boostexphours"] !== "undefined" &&
          globalData[itemType]["boostexphours"] !== 0
        ) {
          boostCheck = false;
        }
        if (
          typeof globalData[itemType]["boostspeedhours"] !== "undefined" &&
          globalData[itemType]["boostspeedhours"] !== 0
        ) {
          boostCheck = false;
        }
        if (
          typeof globalData[itemType]["boostdamagehours_ex"] !== "undefined" &&
          globalData[itemType]["boostdamagehours_ex"] !== 0
        ) {
          boostCheck = false;
        }
        if (
          typeof globalData[itemType]["boostexphours_ex"] !== "undefined" &&
          globalData[itemType]["boostexphours_ex"] !== 0
        ) {
          boostCheck = false;
        }
        if (
          typeof globalData[itemType]["boostspeedhours_ex"] !== "undefined" &&
          globalData[itemType]["boostspeedhours_ex"] !== 0
        ) {
          boostCheck = false;
        }
        break;
      case "dev":
        break;
    }
    return true;
  };

  var ownedItems = [];
  collectionBook.innerHTML = "";
  for (var i = 0; i < cbContent["total_items"]; i++) {
    ownedItems.push(cbContent["cb" + i + "_type"]);
    if (
      !canShowItem(
        cbContent["cb" + i + "_type"],
        cbContent["cb" + i + "_pinned"] === "1"
      )
    ) {
      continue;
    }
    var itemType = getItemType(globalData[cbContent["cb" + i + "_type"]]);
    var item = document.createElement("div");
    item.classList.add("fakeItem");
    if (userVars["memberto"]) {
      item.classList.add("other");
    }
    item.dataset.type = cbContent["cb" + i + "_inv"];
    item.dataset.quantity = cbContent["cb" + i + "_quantity"];

    var itemName = document.createElement("div");
    itemName.textContent = itemNamer(cbContent["cb" + i + "_inv"]);
    item.appendChild(itemName);

    if (
      (userVars["memberto"] && cbContent["cb" + i + "_pinned"] === "1") ||
      !userVars["memberto"]
    ) {
      var pinnedIcon = document.createElement("img");
      pinnedIcon.style.position = "absolute";
      pinnedIcon.style.bottom = "5px";
      pinnedIcon.style.right = "5px";
      pinnedIcon.src =
        "https://files.deadfrontier.com/deadfrontier/images/icons/show_sticky.gif";

      if (cbContent["cb" + i + "_pinned"] !== "1") {
        pinnedIcon.style.filter = "grayscale(100%)";
      }

      if (!userVars["memberto"]) {
        pinnedIcon.style.cursor = "pointer";
        pinnedIcon.addEventListener("click", function (evt) {
          var pinnedTarget = evt.currentTarget;
          var dataArr = {};
          dataArr["pagetime"] = userVars["pagetime"];
          dataArr["templateID"] = userVars["template_id"];
          dataArr["sc"] = userVars["sc"];
          dataArr["userID"] = userVars["userID"];
          dataArr["password"] = userVars["password"];
          dataArr["gv"] = 21;
          dataArr["action"] = "changepin";
          dataArr["itemcode"] = pinnedTarget.parentNode.dataset.type;

          df_prompt.innerHTML =
            "<div style='text-align: center;'>Loading...</div>";
          df_prompt.parentNode.style.display = "block";
          webCall(
            "hotrods/collectionbook",
            dataArr,
            function (webData) {
              cbContent = flshToArr(webData);
              reloadInventoryData();
              reloadCollectionBook();
            },
            true
          );
        });
      }

      item.appendChild(pinnedIcon);
    }

    if (!userVars["memberto"]) {
      var priLabel = document.createElement("span");
      priLabel.style.position = "absolute";
      priLabel.style.left = "-5px";
      priLabel.style.bottom = "22px";
      priLabel.style.width = "60px";
      priLabel.textContent = "Order";
      item.appendChild(priLabel);

      var priVal = document.createElement("input");
      priVal.style.position = "absolute";
      priVal.style.color = "#ffff00";
      priVal.style.left = "5px";
      priVal.style.bottom = "5px";
      priVal.style.width = "40px";
      priVal.dataset.place = i;
      priVal.type = "number";
      priVal.min = "0";
      priVal.max = "2000";
      priVal.value = cbContent["cb" + i + "_priority"];
      priVal.onchange = function (evt) {
        var targetInput = evt.currentTarget;
        if (
          parseInt(targetInput.value) > parseInt(priVal.max) ||
          parseInt(targetInput.value) < parseInt(priVal.min)
        ) {
          targetInput.style.border = "1px red solid";
          return;
        } else {
          targetInput.style.border = "";
        }
        if (
          cbContent["cb" + targetInput.dataset.place + "_priority"] ===
          targetInput.value
        ) {
          targetInput.style.border = "1px red solid";
          return;
        }
        var dataArr = {};
        dataArr["pagetime"] = userVars["pagetime"];
        dataArr["templateID"] = userVars["template_id"];
        dataArr["sc"] = userVars["sc"];
        dataArr["userID"] = userVars["userID"];
        dataArr["password"] = userVars["password"];
        dataArr["gv"] = 21;
        dataArr["action"] = "priority";
        dataArr["order"] = targetInput.value;
        dataArr["itemcode"] = targetInput.parentNode.dataset.type;

        df_prompt.innerHTML =
          "<div style='text-align: center;'>Loading...</div>";
        df_prompt.parentNode.style.display = "block";
        webCall(
          "hotrods/collectionbook",
          dataArr,
          function (webData) {
            cbContent = flshToArr(webData);
            reloadInventoryData();
            reloadCollectionBook();
          },
          true
        );
      };
      item.appendChild(priVal);

      var takeItemOut = document.createElement("button");
      takeItemOut.style.bottom = "5px";
      takeItemOut.style.left = "70px";
      takeItemOut.style.position = "absolute";
      takeItemOut.textContent = "Withdraw";
      if (findFirstEmptyGenericSlot("inv") === false) {
        takeItemOut.disabled = true;
      }
      takeItemOut.onclick = inventoryAction;
      takeItemOut.onmouseenter = function (evt) {
        var targetBox = evt.currentTarget.getBoundingClientRect();
        let invItem = new InventoryItem(
          evt.currentTarget.parentNode.dataset.type
        );
        if (globalData[invItem.type]["no_transfer"] === true || invItem.nt) {
          displayPlacementMessage(
            "It will cost $0 to remove this item.",
            targetBox.left - 70,
            targetBox.top - 40,
            "ERROR"
          );
        } else {
          displayPlacementMessage(
            "It will cost $" +
              nf.format(
                scrapValue(
                  evt.currentTarget.parentNode.dataset.type,
                  evt.currentTarget.parentNode.dataset.quantity
                ) * 2
              ) +
              " to remove this item.",
            targetBox.left - 70,
            targetBox.top - 40,
            "ERROR"
          );
        }
      };
      takeItemOut.onmouseleave = function () {
        cleanPlacementMessage();
      };
      takeItemOut.dataset.action = "cbtake";
      item.appendChild(takeItemOut);
    }

    var itemImg = document.createElement("img");
    itemImg.src =
      "https://files.deadfrontier.com/deadfrontier/inventoryimages/large/" +
      cbContent["cb" + i + "_type"] +
      ".png";
    item.appendChild(itemImg);

    item.insertAdjacentHTML(
      "beforeend",
      "<br />" +
        calcMCTag(cbContent["cb" + i + "_inv"], true, "span", "itemData")
    );

    if (cbContent["cb" + i + "_inv"].indexOf("_stats") >= 0) {
      var itemStats = document.createElement("div");
      var colorMod = false;
      var canCompleteStats = false;
      if (itemType === "weapon") {
        var n = cbContent["cb" + i + "_inv"].indexOf("_stats") + 6;
        n = cbContent["cb" + i + "_inv"].substring(n, n + 3);
        if (mcData[n] && mcData[n][0] !== "") {
          colorMod = mcData[n][0];
        }
        if (parseInt(n) > 0) {
          canCompleteStats = true;
        }
        n = n.split("");
        for (var j in n) n[j] = parseInt(n[j]);
        itemStats.innerHTML += n[0] + "/" + n[1] + "/" + n[2];
      } else if (itemType === "armour") {
        var n = cbContent["cb" + i + "_inv"].indexOf("_stats") + 6;
        n = cbContent["cb" + i + "_inv"].substring(n, n + 4);
        if (mcData[n] && mcData[n][0] !== "") {
          colorMod = mcData[n][0];
        }
        if (parseInt(n) > 0) {
          canCompleteStats = true;
        }
        n = n.match(/[\s\S]{1,2}/g) || [];
        for (var j in n) n[j] = parseInt(n[j]);
        itemStats.innerHTML += n[0] + "/" + n[1];
      } else if (itemType === "backpack") {
        var n = cbContent["cb" + i + "_inv"].indexOf("_stats") + 6;
        n = cbContent["cb" + i + "_inv"].substring(n, n + 1);
        if (mcData[n] && mcData[n][0] !== "") {
          colorMod = mcData[n][0];
        }
        n = parseInt(n);
        if (n > 0) {
          canCompleteStats = true;
        }
        itemStats.innerHTML += n;
      }
      if (colorMod !== false) {
        item.style.borderColor = colorMod;
      }
      if (canCompleteStats) {
        item.appendChild(itemStats);
      }
    }

    if (itemType === "ammo") {
      var ammoCount = document.createElement("div");
      if (
        mcData[cbContent["cb" + i + "_quantity"]] &&
        mcData[cbContent["cb" + i + "_quantity"]][0] !== ""
      ) {
        ammoCount.style.color = mcData[cbContent["cb" + i + "_quantity"]][0];
      }
      if (cbContent["cb" + i + "_type"] === "fuelammo") {
        ammoCount.textContent = cbContent["cb" + i + "_quantity"] + " mL";
      } else {
        ammoCount.textContent = cbContent["cb" + i + "_quantity"] + " Rounds";
      }
      item.appendChild(ammoCount);
    }

    collectionBook.appendChild(item);
  }
  var totalItems = 0;
  var itemsOwned = 0;
  for (var gItem in globalData) {
    if (gItem !== "undefined")
      if (globalData[gItem]["cb_exclude"] !== true) {
        totalItems++;
        if (ownedItems.indexOf(gItem) !== -1) {
          itemsOwned++;
        } else {
          if (!userVars["memberto"]) {
            if (!canShowItem(gItem, false)) {
              continue;
            }
            var item = document.createElement("div");
            item.classList.add("fakeItem");
            item.dataset.type = gItem;
            item.dataset.quantity = 0;

            var itemName = document.createElement("div");
            itemName.textContent = itemNamer(gItem);
            item.appendChild(itemName);

            var itemMissing = document.createElement("div");
            itemMissing.style.color = "red";
            itemMissing.style.fontSize = "24pt";
            itemMissing.style.position = "relative";
            itemMissing.style.top = "50px";
            itemMissing.textContent = "?";
            item.appendChild(itemMissing);

            collectionBook.appendChild(item);
          }
        }
      }
  }

  if (collectionBook.textContent === "") {
    if (cbCategory === "Weapon") {
      collectionBook.textContent =
        "The barren shelves of an abandoned " +
        cbCategoryType.toLowerCase() +
        " collection lay before you, empty, forgotten, or maybe never visited before. The dust remains, but the spirit continues.";
    } else {
      collectionBook.textContent =
        "The barren shelves of an abandoned " +
        cbCategory.toLowerCase() +
        " collection lay before you, empty, forgotten, or maybe never visited before. The dust remains, but the spirit continues.";
    }
  }

  document.getElementById("collectionCounter").textContent =
    itemsOwned + " / " + totalItems + " Collectible Items";
}

function searchPassConditions() {
  var args = arguments;
  for (var i = 1; i <= args.length - 1; i++) {
    if (args[i].toLowerCase().indexOf(args[0]) !== -1) {
      return true;
    }
  }
  return false;
}

function searchSimpleStorageList(e) {
  var searchTerm = e.currentTarget.value;
  var allStorageItems = document
    .getElementById("speedContainer")
    .querySelectorAll(".fakeItem");
  var currentlyHiddenItems = 0;
  for (var item of allStorageItems) {
    var itemData = item.dataset.type.split("_");
    var matchesSearch = false;
    if (
      searchPassConditions(
        searchTerm,
        item.dataset.type,
        globalData[itemData[0]]["name"]
      )
    ) {
      matchesSearch = true;
    }
    if (matchesSearch) {
      item.style.display = "";
    } else {
      currentlyHiddenItems++;
      item.style.display = "none";
    }
  }

  if (currentlyHiddenItems === validItems) {
    document
      .getElementById("speedContainer")
      .querySelector(".profitList").style.display = "";
  } else {
    document
      .getElementById("speedContainer")
      .querySelector(".profitList").style.display = "none";
  }
}

function initiateStorage() {
  if (checkLSBool("general", "simpleMenus")) {
    document.getElementById("storage").innerHTML =
      "<div id='speedContainer' class='fakeSlot' data-action='simpleStore'></div>";

    if (userVars["DFSTATS_df_storage_slots"] < 480) {
      var upgradeButton = document.createElement("button");
      upgradeButton.textContent = "buy 5 more slots";
      upgradeButton.style.fontSize = "16px";
      upgradeButton.dataset.action = "storageUpgrade";

      var addBuy = document.createElement("div");
      addBuy.id = "buyStorageSlots";
      addBuy.classList.add("opElem");
      addBuy.style.width = "100%";
      addBuy.style.top = "46px";
      addBuy.style.color = "#E6CC4D";
      addBuy.appendChild(upgradeButton);
      addBuy.innerHTML +=
        "<br /><span>Price: $" + nf.format(getUpgradePrice()) + "</span>";
      addBuy.querySelector("button").addEventListener("click", inventoryAction);
      inventoryHolder.appendChild(addBuy);
    }

    var storageSearchBox = document.createElement("input");
    storageSearchBox.id = "storageSearchBox";
    storageSearchBox.oninput = searchSimpleStorageList;
    storageSearchBox.placeholder = "Search...";
    inventoryHolder.appendChild(storageSearchBox);
  } else {
    document.getElementById("storage").innerHTML =
      "<div id='normalContainer'></div>";
    for (var y = 0; y < 5; y++) {
      for (var x = 0; x < 8; x++) {
        var slotId = y + 1 + x * 5;
        var slot = document.createElement("div");
        slot.classList.add("slot");
        //slot.style.left = (x) * 68;
        //slot.style.top = (y) * 60;
        slot.dataset.slot = slotId;
        if (x % 2 !== 0) {
          slot.classList.add("reverse");
        }
        if (slotId <= userVars["DFSTATS_df_storage_slots"]) {
          slot.classList.add("validSlot");
        }
        document
          .getElementById("storage")
          .querySelector("#normalContainer")
          .appendChild(slot);
      }
    }
    if (userVars["DFSTATS_df_storage_slots"] < 480) {
      var upgradeButton = document.createElement("button");
      upgradeButton.textContent = "buy 5 more slots";
      upgradeButton.style.fontSize = "16px";
      upgradeButton.dataset.action = "storageUpgrade";

      var addBuy = document.createElement("div");
      addBuy.id = "buyStorageSlots";
      addBuy.classList.add("opElem");
      addBuy.style.width = "100%";
      addBuy.style.top = "46px";
      addBuy.style.color = "#E6CC4D";
      addBuy.appendChild(upgradeButton);
      addBuy.innerHTML +=
        "<br /><span>Price: $" + nf.format(getUpgradePrice()) + "</span>";
      addBuy.querySelector("button").addEventListener("click", inventoryAction);
      inventoryHolder.appendChild(addBuy);
    }

    slotNum = document.createElement("div");
    slotNum.classList.add("opElem");
    slotNum.style.right = "80px";
    slotNum.style.top = "56px";
    slotNum.textContent = "(";

    var slotJumper = document.createElement("input");
    slotJumper.placeholder = storageTab + 1;
    slotJumper.type = "number";
    slotJumper.style.width = "16px";
    slotJumper.textContent = 1;

    slotNum.appendChild(slotJumper);
    slotNum.innerHTML +=
      "/" + Math.ceil(userVars["DFSTATS_df_storage_slots"] / 40) + ")";

    slotNum
      .querySelector("input")
      .addEventListener("change", storage_autoChangePage);

    inventoryHolder.appendChild(slotNum);

    if ((storageTab + 1) * 40 >= userVars["DFSTATS_df_storage_slots"]) {
      document.getElementById("storageForward").style.display = "none";
    } else {
      document.getElementById("storageForward").style.display = "block";
    }
  }

  var dataArr = {};
  dataArr["pagetime"] = userVars["pagetime"];
  dataArr["sc"] = userVars["sc"];
  dataArr["userID"] = userVars["userID"];
  dataArr["password"] = userVars["password"];

  webCall(
    "get_storage",
    dataArr,
    function (data) {
      storageBox = flshToArr(data);

      if (!checkLSBool("general", "simpleMenus")) {
        let dumptFromStorageBtn = document.createElement("button");
        dumptFromStorageBtn.id = "storagetoinv";

        let dumpFromStorageIcon = document.createElement("img");
        dumpFromStorageIcon.src =
          "/onlinezombiemmo/hotrods/hotrods_v" +
          hrV +
          "/HTML5/images/movein.png";
        dumpFromStorageIcon.width = "40";
        dumpFromStorageIcon.dataset.amchild = "";
        dumptFromStorageBtn.appendChild(dumpFromStorageIcon);

        //dumptFromStorageBtn.textContent = 'Move Storage to Inventory';
        dumptFromStorageBtn.disabled = true;
        dumptFromStorageBtn.dataset.pmoverride = "";

        dumptFromStorageBtn.addEventListener("click", function (evt) {
          dumptFromStorageBtn.disabled = true;
          promptLoading();
          let dataArr = {
            pagetime: userVars["pagetime"],
            templateID: userVars["template_ID"],
            sc: userVars["sc"],
            gv: 21,
            userID: userVars["userID"],
            password: userVars["password"],
            action: "fromstorage",
            slotnum: storageTab * 40 + 1,
          };
          playSound("swap");

          webCall(
            "hotrods/inventory_actions",
            dataArr,
            reloadStorageData,
            true
          );
        });
        dumptFromStorageBtn.addEventListener("mousemove", function (evt) {
          if (dumptFromStorageBtn.disabled) {
            switch (dumptFromStorageBtn.dataset.pmoverride) {
              case "inventory":
                displayPlacementMessage(
                  "No room in inventory",
                  dumptFromStorageBtn.getBoundingClientRect().left,
                  dumptFromStorageBtn.getBoundingClientRect().bottom + 12,
                  "ERROR"
                );
                break;
              default:
                displayPlacementMessage(
                  "No movable items",
                  dumptFromStorageBtn.getBoundingClientRect().left,
                  dumptFromStorageBtn.getBoundingClientRect().bottom + 12,
                  "ERROR"
                );
                break;
            }
          } else {
            displayPlacementMessage(
              "Transfer items from storage to inventory",
              dumptFromStorageBtn.getBoundingClientRect().left,
              dumptFromStorageBtn.getBoundingClientRect().bottom + 12,
              "ACTION"
            );
          }
        });
        dumptFromStorageBtn.addEventListener("mouseleave", function (evt) {
          cleanPlacementMessage();
        });

        inventoryActionButtonHolder.appendChild(dumptFromStorageBtn);

        let dumptToStorageBtn = document.createElement("button");
        dumptToStorageBtn.id = "invtostorage";

        let dumpToStorageIcon = document.createElement("img");
        dumpToStorageIcon.src =
          "/onlinezombiemmo/hotrods/hotrods_v" +
          hrV +
          "/HTML5/images/moveout.png";
        dumpToStorageIcon.width = "40";
        dumpToStorageIcon.dataset.amchild = "";
        dumptToStorageBtn.appendChild(dumpToStorageIcon);

        //dumptToStorageBtn.textContent = 'Move Inventory to Storage';
        dumptToStorageBtn.disabled = true;
        dumptToStorageBtn.dataset.pmoverride = "";

        dumptToStorageBtn.addEventListener("click", function (evt) {
          dumptToStorageBtn.disabled = true;
          promptLoading();
          let dataArr = {
            pagetime: userVars["pagetime"],
            templateID: userVars["template_ID"],
            sc: userVars["sc"],
            gv: 21,
            userID: userVars["userID"],
            password: userVars["password"],
            action: "tostorage",
            slotnum: storageTab * 40 + 1,
          };
          playSound("swap");

          webCall(
            "hotrods/inventory_actions",
            dataArr,
            reloadStorageData,
            true
          );
        });
        dumptToStorageBtn.addEventListener("mousemove", function (evt) {
          if (dumptToStorageBtn.disabled) {
            switch (dumptToStorageBtn.dataset.pmoverride) {
              case "storage":
                displayPlacementMessage(
                  "Storage past page full",
                  dumptToStorageBtn.getBoundingClientRect().left,
                  dumptToStorageBtn.getBoundingClientRect().bottom + 12,
                  "ERROR"
                );
                break;
              default:
                displayPlacementMessage(
                  "No movable items",
                  dumptToStorageBtn.getBoundingClientRect().left,
                  dumptToStorageBtn.getBoundingClientRect().bottom + 12,
                  "ERROR"
                );
                break;
            }
          } else {
            displayPlacementMessage(
              "Transfer items from inventory to storage",
              dumptToStorageBtn.getBoundingClientRect().left,
              dumptToStorageBtn.getBoundingClientRect().bottom + 12,
              "ACTION"
            );
          }
        });
        dumptToStorageBtn.addEventListener("mouseleave", function (evt) {
          cleanPlacementMessage();
        });

        inventoryActionButtonHolder.appendChild(dumptToStorageBtn);
      }

      populateStorage();
    },
    true
  );
}

function storage_autoChangePage(e) {
  var targetPage = e.currentTarget.value - 1;

  if (
    targetPage + 1 <= Math.ceil(userVars["DFSTATS_df_storage_slots"] / 40) &&
    targetPage >= 0 &&
    storageTab !== targetPage
  ) {
    for (var i = 1; i <= 40; i++) {
      var currentSlot = i + 40 * storageTab;
      document.querySelector(`div[data-slot='${currentSlot}']`).dataset.slot =
        i + 40 * targetPage;
    }

    if ((storageTab + 1) * 40 >= userVars["DFSTATS_df_storage_slots"]) {
      document.getElementById("storageForward").style.display = "none";
    } else {
      document.getElementById("storageForward").style.display = "block";
    }

    if (storageTab - 1 < 0) {
      document.getElementById("storageBackward").style.display = "none";
    } else {
      document.getElementById("storageBackward").style.display = "block";
    }

    e.currentTarget.placeholder = e.currentTarget.value;
    storageTab = targetPage;
    populateStorage();
  }
  e.currentTarget.value = "";
}

function canDoImplantSwap(pIndex) {
  if (typeof implantPresets[pIndex] === "undefined") {
    return false;
  }
  let implantBucket = {};
  let totalStoredImplants = 0;
  for (let i = 1; i <= userVars["DFSTATS_df_implantslots"]; i++) {
    if (
      typeof userVars[`DFSTATS_df_implant${i}_type`] !== "undefined" &&
      userVars[`DFSTATS_df_implant${i}_type`].length > 0
    ) {
      if (
        typeof implantBucket[userVars[`DFSTATS_df_implant${i}_type`]] ===
        "undefined"
      ) {
        implantBucket[userVars[`DFSTATS_df_implant${i}_type`]] = 0;
      }
      implantBucket[userVars[`DFSTATS_df_implant${i}_type`]]++;
      totalStoredImplants++;
    }
  }
  for (let implantType in implantStorage) {
    if (typeof implantBucket[implantType] === "undefined") {
      implantBucket[implantType] = 0;
    }
    implantBucket[implantType] += parseInt(
      implantStorage[implantType]["quantity"]
    );
    totalStoredImplants += implantBucket[implantType];
  }

  for (let i = 1; i <= userVars["DFSTATS_df_implantslots"]; i++) {
    if (
      typeof implantBucket[implantPresets[pIndex][`df_implant${i}_type`]] !==
        "undefined" &&
      implantBucket[implantPresets[pIndex][`df_implant${i}_type`]] > 0
    ) {
      implantBucket[implantPresets[pIndex][`df_implant${i}_type`]]--;
      totalStoredImplants--;
    }
  }
  //console.log(totalStoredImplants);
  return totalStoredImplants <= userVars["PLAYERVARS_implant_storage_slots"];
}

function createCommonInventoryItems() {
  var implantBonusDisplayerDisplay = function (e) {
    let target = e.target;
    if (
      implantBonusBox.style.visibility === "hidden" ||
      typeof target.dataset.value !== "undefined" ||
      typeof target.parentNode.dataset.value !== "undefined"
    ) {
      if (
        typeof target.dataset.value === "undefined" &&
        typeof target.parentNode.dataset.value !== "undefined"
      ) {
        target = target.parentNode;
      }
      let loadStats = false;
      if (typeof target.dataset.value !== "undefined") {
        if (typeof implantPresets[target.dataset.value] !== "undefined") {
          loadStats = {};
          for (let stat in implantPresets[target.dataset.value]) {
            loadStats["DFSTATS_" + stat] =
              implantPresets[target.dataset.value][stat];
          }
        }
        if (loadStats === false) {
          return;
        }
      }
      if (loadStats === false) {
        loadStats = {};
        for (let i = 1; i <= userVars[`DFSTATS_df_implantslots`]; i++) {
          loadStats[`DFSTATS_df_implant${i}_type`] =
            userVars[`DFSTATS_df_implant${i}_type`];
        }
      }
      implantBonusBox.innerHTML = "<div class='itemName'>Implant Bonuses</div>";
      let implantBonuses = {};
      for (let i of implantBonusesTypes) {
        implantBonuses[i] = 0;
      }
      let bonusStats = {};
      for (let i = 1; i <= userVars["DFSTATS_df_implantslots"]; i++) {
        if (
          typeof loadStats[`DFSTATS_df_implant${i}_type`] === "string" &&
          loadStats[`DFSTATS_df_implant${i}_type`].length > 0
        ) {
          let implantItem = new InventoryItem(
            loadStats[`DFSTATS_df_implant${i}_type`]
          );
          if (
            typeof implantItem.ex !== "undefined" &&
            implantItem.ex <
              parseInt(userVars["pagetime"]) + timeSincePageLoaded
          ) {
            continue;
          }
          let positiveMult = 1;
          let negativeMult = 1;
          let iIndex = i;
          while (globalData[implantItem.type]["implant_mimicry"] === true) {
            positiveMult *=
              globalData[implantItem.type]["implant_mimicry_positive_mult"] ??
              1;
            negativeMult *=
              globalData[implantItem.type]["implant_mimicry_negative_mult"] ??
              1;
            if (
              iIndex - 1 > 0 &&
              loadStats[`DFSTATS_df_implant${i - 1}_type`] !== ""
            ) {
              implantItem = new InventoryItem(
                loadStats[`DFSTATS_df_implant${iIndex - 1}_type`]
              );
              iIndex--;
              if (
                typeof implantItem.ex !== "undefined" &&
                implantItem.ex <
                  parseInt(userVars["pagetime"]) + timeSincePageLoaded
              ) {
                break;
              }
            } else {
              break;
            }
          }
          if (
            typeof implantItem.ex !== "undefined" &&
            implantItem.ex <
              parseInt(userVars["pagetime"]) + timeSincePageLoaded
          ) {
            continue;
          }
          let implantData = globalData[implantItem.type];

          if (
            typeof implantData["implant_stats"] !== "undefined" &&
            implantData["implant_stats"].length > 0
          ) {
            let allowedStatsToMod = implantData["implant_stats"].split(",");
            let implantMultipliers =
              implantData["implant_stats_multi"].split(",");
            for (let mult in implantMultipliers) {
              implantMultipliers[mult] = parseFloat(implantMultipliers[mult]);
            }
            let maxCombinations = allowedStatsToMod.length;
            let maxNeededBits = BigInt(
              implantData["implant_stats_max_stat"].toString(2).length
            );
            if (typeof implantItem.stats !== "undefined") {
              let tempStats = bAnyToBInt(implantItem.stats, 36);
              for (let i = maxCombinations - 1; i >= 0; i--) {
                if (typeof bonusStats[allowedStatsToMod[i]] === "undefined") {
                  bonusStats[allowedStatsToMod[i]] = 0;
                }
                let impBonus = Number(tempStats & (2n ** maxNeededBits - 1n));
                if (
                  impBonus > parseInt(implantData["implant_stats_max_stat"])
                ) {
                  impBonus = parseInt(implantData["implant_stats_max_stat"]);
                }
                bonusStats[allowedStatsToMod[i]] +=
                  impBonus * implantMultipliers[i] * positiveMult;
                tempStats = tempStats >> maxNeededBits;
              }
            }
          }
          if (
            typeof implantData["implant_mods"] !== "undefined" &&
            implantData["implant_mods"].length > 0
          ) {
            let allowedStatsToMod = implantData["implant_mods"].split(",");
            let maxCombinations = allowedStatsToMod.length;
            let maxStatsReplacement =
              implantData["implant_mods_negative"] +
              implantData["implant_mods_positive"];
            let maxNeededBits = BigInt(maxStatsReplacement.toString(2).length);
            if (typeof implantItem.stats !== "undefined") {
              let tempStats = bAnyToBInt(implantItem.stats, 36);
              for (let i = maxCombinations - 1; i >= 0; i--) {
                if (typeof bonusStats[allowedStatsToMod[i]] === "undefined") {
                  bonusStats[allowedStatsToMod[i]] = 0;
                }
                let impBonus = Number(tempStats & (2n ** maxNeededBits - 1n));
                if (impBonus > maxStatsReplacement) {
                  impBonus = maxStatsReplacement;
                }
                impBonus -= implantData["implant_mods_negative"];
                if (impBonus > 0) {
                  bonusStats[allowedStatsToMod[i]] += impBonus * positiveMult;
                } else {
                  bonusStats[allowedStatsToMod[i]] += impBonus * negativeMult;
                }
                tempStats = tempStats >> maxNeededBits;
              }
            }
          }
          for (let j in implantBonuses) {
            if (typeof implantData[j] !== "undefined") {
              let bonusVal = parseFloat(implantData[j]);
              // hardcoded reversals then regular handler
              if (implantNegativeBonuses.includes(j)) {
                if (bonusVal > 0) {
                  bonusVal *= negativeMult;
                } else {
                  bonusVal *= positiveMult;
                }
              } else {
                if (bonusVal < 0) {
                  bonusVal *= negativeMult;
                } else {
                  bonusVal *= positiveMult;
                }
              }

              implantBonuses[j] += bonusVal;
            }
          }
        }
      }
      implantBonusBox.innerHTML += ic_ImplantData(implantBonuses, bonusStats);
    }

    var invHoldOffsets = inventoryHolder.getBoundingClientRect();
    if (mousePos[1] - 30 - implantBonusBox.offsetHeight < invHoldOffsets.top) {
      implantBonusBox.style.top = mousePos[1] + 30 - invHoldOffsets.top + "px";
    } else {
      implantBonusBox.style.top =
        mousePos[1] -
        30 -
        implantBonusBox.offsetHeight -
        invHoldOffsets.top +
        "px";
    }

    if (mousePos[0] + 20 + implantBonusBox.offsetWidth > invHoldOffsets.right) {
      implantBonusBox.style.left =
        inventoryHolder.offsetWidth - implantBonusBox.offsetWidth + "px";
    } else {
      implantBonusBox.style.left =
        mousePos[0] + 20 - invHoldOffsets.left + "px";
    }
    implantBonusBox.style.visibility = "visible";
  };

  var implantBonusDisplayerHide = function () {
    implantBonusBox.style.visibility = "hidden";
  };

  let implantMenu = document.createElement("div");
  implantMenu.id = "implantMenu";
  implantMenu.style = "position: absolute; left: 13px; top: 50px; z-index: 1;";

  if (
    window.location.pathname.indexOf("/DF3D/") === -1 &&
    window.location.search.indexOf("page=31") === -1
  ) {
    implantMenu.style.top = "0px";
    let implantPresetMenu = document.createElement("div");
    implantPresetMenu.style.height = "50px";
    let iPMTitle = document.createElement("div");
    iPMTitle.textContent = "Presets ";
    let iPMTHelper = document.createElement("button");
    iPMTHelper.textContent = "?";
    iPMTHelper.addEventListener("click", function () {
      promptLoading(
        "Presets fetch implants from your Implant Storage and your equipped implants only. Drag implants onto the bucket to add to your Implant Storage."
      );
      let okayButton = document.createElement("button");
      okayButton.addEventListener("click", promptEnd);
      okayButton.style =
        "position: absolute; bottom: 0; left: 120px; right: 120px;";
      okayButton.textContent = "Ok!";
      df_prompt.appendChild(okayButton);
    });
    iPMTitle.appendChild(iPMTHelper);
    implantPresetMenu.appendChild(iPMTitle);

    let iPMSelect = document.createElement("div");
    iPMSelect.id = "implantPresetMenuSelect";
    iPMSelect.style =
      "height: 17px; background-color: #222; border: 1px solid #990000; position: relative; width: 80%; display: inline-block;";
    iPMSelect.dataset.value = "";
    let iPMSSelection = document.createElement("span");

    iPMSelect.appendChild(iPMSSelection);

    let iPMSCaret = document.createElement("span");
    iPMSCaret.textContent = "∨";
    iPMSCaret.style = "float: right;";
    iPMSelect.appendChild(iPMSCaret);

    let buyPresetSlot = document.createElement("button");
    buyPresetSlot.textContent = "+";
    buyPresetSlot.style = "margin-left: 5px; vertical-align: top;";
    buyPresetSlot.dataset.pmoverride = "buy";
    buyPresetSlot.addEventListener("click", function () {
      let yesButton = document.createElement("button");
      yesButton.textContent = "Yes";
      yesButton.style = "position: absolute; left: 86px; bottom: 5px;";
      yesButton.addEventListener("click", function () {
        promptLoading();
        let dataArr = {
          pagetime: userVars["pagetime"],
          templateID: userVars["template_id"],
          sc: userVars["sc"],
          userID: userVars["userID"],
          password: userVars["password"],
          gv: 21,
          action: "upgradepresets",
        };

        webCall(
          "hotrods/inventory_actions",
          dataArr,
          function (webData) {
            updateIntoArr(flshToArr(webData), userVars);
            if (!iPMSMenu.contains(newOption)) {
              iPMSMenu.prepend(newOption);
            }
            if (userVars["DFSTATS_df_credits"] < 25) {
              buyPresetSlot.disabled = true;
            }
            populateInventory();
            updateAllFields();
          },
          true
        );
      });

      let noButton = document.createElement("button");
      noButton.textContent = "No";
      noButton.style = "position: absolute; right: 86px; bottom: 5px;";
      noButton.addEventListener("click", promptEnd);

      df_prompt.innerHTML =
        'Are you sure you want to purchase another preset slot for <span style="color: #999999;">25 Credits</span>?';
      df_prompt.appendChild(yesButton);
      df_prompt.appendChild(noButton);

      df_prompt.onkeydown = function (e) {
        if (e.keyCode === 13) {
          df_prompt.onkeydown = null;
          yesButton.click();
        }
      };
      df_prompt.parentNode.style.display = "block";
      df_prompt.focus();
    });
    buyPresetSlot.addEventListener("mousemove", function () {
      if (userVars["DFSTATS_df_credits"] >= 25) {
        displayPlacementMessage(
          "Increase total presets",
          loadButton.getBoundingClientRect().left,
          loadButton.getBoundingClientRect().bottom + 12,
          "ACTION"
        );
      } else {
        buyPresetSlot.disabled = true;
        displayPlacementMessage(
          "You need 25 Credits to increase total presets",
          loadButton.getBoundingClientRect().left,
          loadButton.getBoundingClientRect().bottom + 12,
          "ERROR"
        );
      }
    });
    if (userVars["DFSTATS_df_credits"] < 25) {
      buyPresetSlot.disabled = true;
    }

    let loadButton = document.createElement("button");
    loadButton.dataset.pmoverride = "";
    loadButton.id = "implantPresetLoadButton";

    let iPMSMenu = document.createElement("div");
    iPMSMenu.style =
      "display: none; background-color: #222; position: relative; top: 2px; left: -1px; right: -1px; z-index: 16;";

    let implantNameField = document.createElement("input");
    let saveButton = document.createElement("button");

    let presetClick = function (evt) {
      let target = evt.currentTarget;
      iPMSelect.dataset.value = target.dataset.value;
      iPMSSelection.textContent = target.textContent;

      if (iPMSelect.dataset.value.length <= 0) {
        loadButton.disabled = true;
        loadButton.dataset.pmoverride = "nothing";
        implantNameField.value = "";
        saveButton.disabled = true;
      } else {
        let totalImplants = 0;
        for (let i in implantStorage) {
          totalImplants += parseInt(implantStorage[i]["quantity"]);
        }
        for (let i = 1; i <= userVars["DFSTATS_df_implantslots"]; i++) {
          if (
            typeof userVars["DFSTATS_df_implant" + i + "_type"] !==
              "undefined" &&
            userVars["DFSTATS_df_implant" + i + "_type"].length > 0
          ) {
            totalImplants++;
          }
        }
        let disableLoadButton = true;
        if (typeof implantPresets[target.dataset.value] !== "undefined") {
          for (let i = 1; i <= userVars["DFSTATS_df_implantslots"]; i++) {
            if (
              implantPresets[target.dataset.value][
                "df_implant" + i + "_type"
              ] !== userVars["DFSTATS_df_implant" + i + "_type"] &&
              disableLoadButton
            ) {
              disableLoadButton = false;
              loadButton.dataset.pmoverride = "equipped";
              break;
            }
          }
          if (!canDoImplantSwap(target.dataset.value)) {
            disableLoadButton = true;
            loadButton.dataset.pmoverride = "full";
          }
          loadButton.disabled = disableLoadButton;
        }
        saveButton.disabled = false;
      }
    };

    let newOption = document.createElement("div");
    newOption.classList.add("menuHoverEffect");
    newOption.dataset.value = "";
    newOption.textContent = "New Preset";
    newOption.style = "border: 1px solid #990000;";
    newOption.addEventListener("click", presetClick);
    if (
      parseInt(userVars["PLAYERVARS_implant_preset_slots"]) >
      implantPresets.length
    ) {
      iPMSMenu.appendChild(newOption);
    }

    let loadedPresetIndex = false;

    for (let pIndex in implantPresets) {
      let presetOption = document.createElement("div");
      presetOption.classList.add("menuHoverEffect");
      presetOption.dataset.value = pIndex;
      presetOption.textContent = implantPresets[pIndex]["preset_name"];
      presetOption.style = "border: 1px solid #990000;";
      presetOption.addEventListener("click", presetClick);
      iPMSMenu.appendChild(presetOption);

      if (loadedPresetIndex !== false) {
        continue;
      }
      for (let i = 1; i <= userVars["DFSTATS_df_implantslots"]; i++) {
        if (
          implantPresets[pIndex]["df_implant" + i + "_type"] !==
          userVars["DFSTATS_df_implant" + i + "_type"]
        ) {
          break;
        }
        if (i == userVars["DFSTATS_df_implantslots"]) {
          iPMSSelection.textContent = implantPresets[pIndex]["preset_name"];
          iPMSelect.dataset.value = pIndex;
          loadButton.disabled = true;
          loadedPresetIndex = pIndex;
          loadButton.dataset.pmoverride = "equipped";
        }
      }
    }
    if (loadedPresetIndex === false) {
      if (
        parseInt(userVars["PLAYERVARS_implant_preset_slots"]) >
        implantPresets.length
      ) {
        iPMSSelection.textContent = "New Preset";
        saveButton.disabled = true;
      } else {
        iPMSSelection.textContent = iPMSMenu.firstChild.textContent;
        iPMSelect.dataset.value = iPMSMenu.firstChild.dataset.value;
        loadedPresetIndex = iPMSMenu.firstChild.dataset.value;
      }
    } else {
      saveButton.disabled = false;
    }

    iPMSelect.appendChild(iPMSMenu);

    iPMSelect.addEventListener("mousemove", implantBonusDisplayerDisplay);
    iPMSelect.addEventListener("mouseleave", implantBonusDisplayerHide);
    iPMSelect.addEventListener("click", function () {
      if (iPMSMenu.style.display === "none") {
        iPMSMenu.style.display = "";
      } else {
        iPMSMenu.style.display = "none";
      }
    });

    loadButton.textContent = "load";
    loadButton.addEventListener("click", function (evt) {
      playSound("swap");
      promptLoading();
      let target = evt.currentTarget;
      target.disabled = true;

      var dataArr = {};
      dataArr["pagetime"] = userVars["pagetime"];
      dataArr["templateID"] = userVars["template_id"];
      dataArr["sc"] = userVars["sc"];
      dataArr["userID"] = userVars["userID"];
      dataArr["password"] = userVars["password"];
      dataArr["gv"] = 21;
      dataArr["action"] = "loadpreset";
      dataArr["presetname"] =
        implantPresets[iPMSelect.dataset.value]["preset_name"];

      webCall(
        "hotrods/inventory_actions",
        dataArr,
        function (webData) {
          let flshArr = flshToArr(webData);
          implantStorage = JSON.parse(flshArr["impdata"]);
          delete flshArr["impdata"];
          updateIntoArr(flshArr, userVars);

          populateInventory();
          populateImplants();
          promptEnd();
        },
        true
      );
    });
    loadButton.addEventListener("mouseover", function () {
      if (loadButton.disabled) {
        switch (loadButton.dataset.pmoverride) {
          case "full":
            displayPlacementMessage(
              "Implant storage is too full for this move",
              loadButton.getBoundingClientRect().left,
              loadButton.getBoundingClientRect().bottom + 12,
              "ERROR"
            );
            break;
          case "equipped":
            displayPlacementMessage(
              "This loadout is already equipped",
              loadButton.getBoundingClientRect().left,
              loadButton.getBoundingClientRect().bottom + 12,
              "ERROR"
            );
            break;
          case "nothing":
            displayPlacementMessage(
              "No loadout selected",
              loadButton.getBoundingClientRect().left,
              loadButton.getBoundingClientRect().bottom + 12,
              "ERROR"
            );
            break;
        }
      }
    });

    saveButton.textContent = "save";
    saveButton.dataset.pmoverride = "";
    saveButton.addEventListener("click", function (evt) {
      promptLoading();
      let target = evt.currentTarget;
      target.disabled = true;

      let isQuestion = false;

      var dataArr = {};
      dataArr["pagetime"] = userVars["pagetime"];
      dataArr["templateID"] = userVars["template_id"];
      dataArr["sc"] = userVars["sc"];
      dataArr["userID"] = userVars["userID"];
      dataArr["password"] = userVars["password"];
      dataArr["gv"] = 21;
      if (iPMSelect.dataset.value.length > 0) {
        isQuestion = true;
        dataArr["action"] = "updatepreset";
        dataArr["presetname"] =
          implantPresets[iPMSelect.dataset.value]["preset_name"];
        if (
          implantNameField.value.length > 0 &&
          implantNameField.value !==
            implantPresets[iPMSelect.dataset.value]["preset_name"]
        ) {
          dataArr["updatename"] = implantNameField.value;
        }
      } else {
        dataArr["action"] = "savepreset";
        dataArr["presetname"] = implantNameField.value;
      }
      let doTheOperation = function () {
        webCall(
          "hotrods/inventory_actions",
          dataArr,
          function (webData) {
            let flshArr = flshToArr(webData);
            let presetFound = false;
            for (let pIndex in implantPresets) {
              if (
                implantPresets[pIndex]["preset_name"] === flshArr["preset_name"]
              ) {
                presetFound = pIndex;
                if (typeof flshArr["update_name"] !== "undefined") {
                  flshArr["preset_name"] = flshArr["update_name"];
                  delete flshArr["update_name"];
                }
                break;
              }
            }
            if (presetFound === false) {
              implantNameField.value = "";
              implantPresets.push(flshArr);

              let presetOption = document.createElement("div");
              presetOption.classList.add("menuHoverEffect");
              presetOption.dataset.value = implantPresets.length - 1;
              presetOption.textContent = flshArr["preset_name"];
              presetOption.style = "border: 1px solid #990000;";
              presetOption.addEventListener("click", presetClick);
              iPMSMenu.appendChild(presetOption);
              iPMSSelection.textContent = presetOption.textContent;
              iPMSelect.dataset.value = presetOption.dataset.value;
              if (iPMSelect.dataset.value.length > 0) {
                let totalImplants = 0;
                for (let i in implantStorage) {
                  totalImplants++;
                }
                for (let i = 1; i <= userVars["DFSTATS_df_implantslots"]; i++) {
                  if (
                    typeof userVars["DFSTATS_df_implant" + i + "_type"] !==
                      "undefined" &&
                    userVars["DFSTATS_df_implant" + i + "_type"].length > 0
                  ) {
                    totalImplants++;
                  }
                }
                if (totalImplants > 0) {
                  loadButton.disabled = false;
                }
              } else {
                loadButton.disabled = true;
                loadButton.dataset.pmoverride = "nothing";
              }
              if (
                parseInt(userVars["PLAYERVARS_implant_preset_slots"]) <=
                implantPresets.length
              ) {
                iPMSMenu.removeChild(iPMSMenu.firstChild);
                iPMSSelection.textContent = presetOption.textContent;
              }
            } else {
              implantPresets[presetFound] = flshArr;
              iPMSSelection.textContent = flshArr["preset_name"];
              iPMSelect.querySelector(
                ".menuHoverEffect[data-value='" + presetFound + "']"
              ).textContent = implantPresets[presetFound]["preset_name"];
            }

            populateInventory();
            populateImplants();
            promptEnd();
            target.disabled = false;
          },
          true
        );
      };
      if (isQuestion) {
        let yesButton = document.createElement("button");
        yesButton.textContent = "Yes";
        yesButton.style = "position: absolute; left: 86px; bottom: 5px;";
        yesButton.addEventListener("click", function () {
          promptLoading();
          doTheOperation();
        });

        let noButton = document.createElement("button");
        noButton.textContent = "No";
        noButton.style = "position: absolute; right: 86px; bottom: 5px;";
        noButton.addEventListener("click", promptEnd);

        df_prompt.innerHTML = "Are you sure you want to overwrite this preset?";
        df_prompt.appendChild(yesButton);
        df_prompt.appendChild(noButton);

        df_prompt.onkeydown = function (e) {
          if (e.keyCode === 13) {
            df_prompt.onkeydown = null;
            yesButton.click();
          }
        };
        df_prompt.parentNode.style.display = "block";
        df_prompt.focus();
      } else {
        doTheOperation();
      }
    });
    saveButton.addEventListener("mousemove", function () {
      if (saveButton.disabled) {
        switch (saveButton.dataset.pmoverride) {
          case "usedname":
            displayPlacementMessage(
              "This name is already in use",
              loadButton.getBoundingClientRect().left,
              loadButton.getBoundingClientRect().bottom + 12,
              "ERROR"
            );
            break;
          case "noname":
            displayPlacementMessage(
              "No name has been input",
              loadButton.getBoundingClientRect().left,
              loadButton.getBoundingClientRect().bottom + 12,
              "ERROR"
            );
            break;
        }
      }
    });

    implantNameField.placeholder = "Preset name...";
    implantNameField.style.width = "70px";
    implantNameField.addEventListener("input", function () {
      if (implantNameField.value.length <= 0) {
        saveButton.disabled = true;
        saveButton.dataset.pmoverride = "noname";
        return;
      }
      for (let pIndex in implantPresets) {
        if (pIndex != iPMSelect.dataset.value) {
          if (
            implantNameField.value === implantPresets[pIndex]["preset_name"]
          ) {
            saveButton.disabled = true;
            saveButton.dataset.pmoverride = "usedname";
            return;
          }
        }
      }
      saveButton.disabled = false;
    });

    if (iPMSelect.dataset.value.length <= 0) {
      loadButton.disabled = true;
      loadButton.dataset.pmoverride = "nothing";
    }

    implantPresetMenu.appendChild(iPMSelect);
    implantPresetMenu.appendChild(buyPresetSlot);
    implantPresetMenu.appendChild(implantNameField);
    implantPresetMenu.appendChild(loadButton);
    implantPresetMenu.appendChild(document.createTextNode(" "));
    implantPresetMenu.appendChild(saveButton);
    implantMenu.appendChild(implantPresetMenu);
  }

  let backpackMenu = document.createElement("div");
  backpackMenu.id = "backpackMenu";
  backpackMenu.style =
    "position: absolute; right: 28px; top: 50px; max-width: 132px; z-index: 1;";

  var backpackLabel = document.createElement("div");
  backpackLabel.id = "backpackLabel";
  backpackMenu.appendChild(backpackLabel);

  var backpackWindow = document.createElement("table");
  backpackWindow.id = "backpackdisplay";
  backpackMenu.appendChild(backpackWindow);

  var backpackWithdraw = document.createElement("button");
  backpackWithdraw.id = "backpackWithdraw";
  backpackWithdraw.textContent = "Move All to Inventory";
  backpackWithdraw.dataset.pmoverride = "";
  backpackWithdraw.style.display = "none";
  backpackWithdraw.disabled = true;
  backpackWithdraw.addEventListener("click", function (evt) {
    let iBackpack = new InventoryItem(userVars["DFSTATS_df_backpack"]);
    let totalCurrentBackpackSlots = parseInt(
      globalData[iBackpack.type]["slots"]
    );
    if (typeof iBackpack.stats !== "undefined") {
      totalCurrentBackpackSlots += parseInt(iBackpack.stats);
    }
    let totalItemsInPack = 0;
    for (var i = 1; i <= totalCurrentBackpackSlots; i++) {
      if (
        typeof userVars["DFSTATS_df_backpack" + i + "_type"] !== "undefined" &&
        userVars["DFSTATS_df_backpack" + i + "_type"].length > 0
      ) {
        totalItemsInPack++;
      }
    }

    let freeInventorySlots = 0;
    for (let i = 1; i <= userVars["DFSTATS_df_invslots"]; i++) {
      if (
        typeof userVars["DFSTATS_df_inv" + i + "_type"] === "undefined" ||
        userVars["DFSTATS_df_inv" + i + "_type"].length === 0
      ) {
        freeInventorySlots++;
      }
    }

    if (freeInventorySlots >= totalItemsInPack) {
      var dataArr = {};
      dataArr["pagetime"] = userVars["pagetime"];
      dataArr["templateID"] = userVars["template_ID"];
      dataArr["sc"] = userVars["sc"];
      dataArr["creditsnum"] = userVars["DFSTATS_df_credits"];
      dataArr["buynum"] = "0";
      dataArr["renameto"] = "undefined`undefined";
      dataArr["expected_itemprice"] = "-1";
      dataArr["expected_itemtype2"] = "";
      dataArr["expected_itemtype"] = "";
      dataArr["itemnum2"] = "";
      dataArr["itemnum"] = "";
      dataArr["price"] = getUpgradePrice();
      dataArr["gv"] = 21;
      dataArr["userID"] = userVars["userID"];
      dataArr["password"] = userVars["password"];
      playSound("swap");
      dataArr["action"] = "empty";
      webCall(
        "hotrods/backpack",
        dataArr,
        function (data) {
          updateIntoArr(flshToArr(data, "DFSTATS_"), userVars);
          populateInventory();
          populateBackpack();
          populateImplants();
          updateAllFields();
        },
        true
      );
    }
  });
  backpackWithdraw.addEventListener("mousemove", function (evt) {
    if (backpackWithdraw.disabled) {
      switch (backpackWithdraw.dataset.pmoverride) {
        case "lock":
          displayPlacementMessage(
            "No movable items",
            backpackWithdraw.getBoundingClientRect().left,
            backpackWithdraw.getBoundingClientRect().bottom + 12,
            "ERROR"
          );
          break;
        default:
          displayPlacementMessage(
            "Too many items",
            backpackWithdraw.getBoundingClientRect().left,
            backpackWithdraw.getBoundingClientRect().bottom + 12,
            "ERROR"
          );
          break;
      }
    }
  });
  if (
    typeof userVars["DFSTATS_df_backpack"] === "undefined" ||
    userVars["DFSTATS_df_backpack"].length === 0
  ) {
    backpackMenu.style.display = "none";
  }
  backpackMenu.appendChild(backpackWithdraw);

  var hideArmours = document.createElement("button");
  hideArmours.classList.add("opElem");
  hideArmours.style.left = "400px";
  hideArmours.style.top = "205px";
  if (userVars["DFSTATS_df_hidearmour"] === "0") {
    hideArmours.textContent = "Show Armour [X]";
    hideArmours.dataset.hide = "1";
  } else {
    hideArmours.textContent = "Show Armour [ ]";
    hideArmours.dataset.hide = "0";
  }

  hideArmours.addEventListener("click", function (e) {
    var target = e.currentTarget;

    var hideData = {
      action: "setarmour",
      userID: userVars["userID"],
      password: userVars["password"],
      hide: target.dataset.hide,
    };
    webCall("DF3D/DF3D_PlayerSettings", hideData, function (data) {
      updateIntoArr(flshToArr(data, "DFSTATS_"), userVars);
      if (userVars["DFSTATS_df_hidearmour"] === "0") {
        target.textContent = "Show Armour [X]";
        target.dataset.hide = "1";
      } else {
        target.textContent = "Show Armour [ ]";
        target.dataset.hide = "0";
      }
      renderAvatarUpdate();
      pageLock = false;
    });
  });

  inventoryHolder.appendChild(hideArmours);
  userVars["DFSTATS_df_implantslots"] = parseInt(
    userVars["DFSTATS_df_implantslots"]
  );
  if (userVars["DFSTATS_df_implantslots"] > 0) {
    var implantLabel = document.createElement("span");
    implantLabel.classList.add("credits");
    implantLabel.textContent = "Implants";
    implantLabel.dataset.cash = "Implants";
    implantMenu.appendChild(implantLabel);

    var implantBonusBox = document.createElement("div");
    implantBonusBox.classList.add("infoBox");
    implantBonusBox.classList.add("opElem");
    implantBonusBox.style.visibility = "hidden";
    implantBonusBox.style.width = "max-content";

    var implantBonusDisplayerTrigger = document.createElement("span");
    implantBonusDisplayerTrigger.textContent = " B";
    implantBonusDisplayerTrigger.style.color = "gold";
    implantBonusDisplayerTrigger.onmousemove = implantBonusDisplayerDisplay;
    implantBonusDisplayerTrigger.onmouseleave = implantBonusDisplayerHide;

    inventoryHolder.appendChild(implantBonusBox);
    implantMenu.appendChild(implantBonusDisplayerTrigger);

    let implantElem = document.createElement("table");
    implantElem.style.display = "table";
    implantElem.id = "implants";
    implantElem.innerHTML = "";
    implantElem.textContent = "";
    var invSection = document.createElement("tr");
    for (var y = 0; y < userVars["DFSTATS_df_implantslots"]; y++) {
      var slot = document.createElement("td");
      slot.dataset.slot = y + 1;
      slot.dataset.slottype = "implant";
      slot.classList.add("validSlot");
      invSection.appendChild(slot);

      if (invSection.childNodes.length >= 4) {
        implantElem.appendChild(invSection);
        invSection = document.createElement("tr");
      }
    }
    if (invSection.childNodes.length > 0 && invSection.childNodes.length < 4) {
      implantElem.appendChild(invSection);
    }
    implantMenu.appendChild(implantElem);

    implantBonusDisplayerTrigger.style.left =
      implantLabel.offsetLeft +
      implantLabel.offsetWidth +
      implantBonusDisplayerTrigger.offsetWidth +
      5 +
      "px";
  } else {
    implantMenu.style.display = "none";
  }

  document.getElementById("invController").appendChild(implantMenu);
  document.getElementById("invController").appendChild(backpackMenu);

  if (
    window.location.pathname.indexOf("/DF3D/") === -1 &&
    window.location.search.indexOf("page=31") === -1
  ) {
    let implantBucket = document.createElement("div");
    implantBucket.id = "implantbucket";
    implantBucket.dataset.action = "toimpstore";
    implantBucket.classList.add("fakeSlot");
    implantBucket.addEventListener("click", function () {
      implantBucket.classList.add("open");

      pageLock = true;
      let implantBucketMenu = document.createElement("div");
      implantBucketMenu.classList.add("genericHolder");
      implantBucketMenu.style.position = "absolute";
      implantBucketMenu.style.inset = "20px";
      implantBucketMenu.classList.add("genericActionBox");
      implantBucketMenu.style.zIndex = "40";

      let implantBucketMenuTitle = document.createElement("div");
      implantBucketMenuTitle.classList.add("downcomeTitle");
      implantBucketMenuTitle.textContent = "Stored Implants";
      implantBucketMenu.appendChild(implantBucketMenuTitle);

      let implantBucketMenuCapacity = document.createElement("div");
      let totalStoredImplants = 0;
      for (let implantType in implantStorage) {
        totalStoredImplants += parseInt(
          implantStorage[implantType]["quantity"]
        );
      }
      implantBucketMenuCapacity.textContent =
        totalStoredImplants +
        " / " +
        userVars["PLAYERVARS_implant_storage_slots"];
      implantBucketMenuCapacity.style =
        "position: absolute; right: 5px; top: 30px;";
      implantBucketMenu.appendChild(implantBucketMenuCapacity);

      let implantBucketMenuUpgradeButton = document.createElement("button");
      implantBucketMenuUpgradeButton.textContent =
        "Increase Bucket by +5 Slots for 25 Credits";
      if (parseInt(userVars["DFSTATS_df_credits"]) < 25) {
        implantBucketMenuUpgradeButton.disabled = true;
      }
      implantBucketMenuUpgradeButton.addEventListener("click", function () {
        implantBucketMenuUpgradeButton.disabled = true;
        promptLoading(
          "Are you sure you want to purchase 5 storage slots for <span class='cashhack credits' data-cash='25 Credits' style='position: relative; display: inline-block;'>25 Credits</span>?"
        );
        df_prompt.classList.add("warning");
        let yesButton = document.createElement("button");
        yesButton.textContent = "Yes";
        yesButton.style = "position: absolute; left: 86px; bottom: 5px;";
        yesButton.addEventListener("click", function () {
          var dataArr = {};
          dataArr["pagetime"] = userVars["pagetime"];
          dataArr["templateID"] = userVars["template_id"];
          dataArr["sc"] = userVars["sc"];
          dataArr["userID"] = userVars["userID"];
          dataArr["password"] = userVars["password"];
          dataArr["gv"] = 21;
          dataArr["action"] = "upgradeimpstore";

          webCall(
            "hotrods/inventory_actions",
            dataArr,
            function (webData) {
              updateIntoArr(flshToArr(webData), userVars);
              if (parseInt(userVars["DFSTATS_df_credits"]) < 25) {
                implantBucketMenuUpgradeButton.disabled = true;
              } else {
                implantBucketMenuUpgradeButton.disabled = false;
              }
              implantBucketMenuCapacity.textContent =
                totalStoredImplants +
                " / " +
                userVars["PLAYERVARS_implant_storage_slots"];

              populateInventory();
              populateImplantList();
              updateAllFields();
            },
            true
          );
        });
        df_prompt.appendChild(yesButton);

        let noButton = document.createElement("button");
        noButton.textContent = "no";
        noButton.style = "position: absolute; right: 86px; bottom: 5px;";
        noButton.addEventListener("click", function () {
          if (parseInt(userVars["DFSTATS_df_credits"]) < 25) {
            implantBucketMenuUpgradeButton.disabled = true;
          } else {
            implantBucketMenuUpgradeButton.disabled = false;
          }
          promptEnd();
        });
        df_prompt.appendChild(noButton);
        df_prompt.onkeydown = function (e) {
          if (e.keyCode === 13) {
            df_prompt.onkeydown = null;
            yesButton.click();
          }
        };
        df_prompt.focus();
      });
      implantBucketMenu.appendChild(implantBucketMenuUpgradeButton);

      let closeMenu = document.createElement("button");
      closeMenu.classList.add("opElem");
      closeMenu.style.top = "3px";
      closeMenu.style.right = "3px";
      closeMenu.innerHTML = "&#10006;";
      closeMenu.addEventListener("click", function (evt) {
        pageLock = false;
        implantBucket.classList.remove("open");
        inventoryHolder.removeChild(implantBucketMenu);
      });
      implantBucketMenu.appendChild(closeMenu);

      let implantList = document.createElement("div");
      implantList.classList.add("itemList");
      implantList.classList.add("opElem");
      implantList.style.bottom = "0";
      implantList.addEventListener("mouseenter", () => {
        allowInfoCardOverride = true;
      });
      implantList.addEventListener("mouseleave", () => {
        allowInfoCardOverride = false;
      });

      let withdrawAction = function (evt) {
        promptLoading();
        let target = evt.currentTarget;
        target.disabled = true;

        var dataArr = {};
        dataArr["pagetime"] = userVars["pagetime"];
        dataArr["templateID"] = userVars["template_id"];
        dataArr["sc"] = userVars["sc"];
        dataArr["userID"] = userVars["userID"];
        dataArr["password"] = userVars["password"];
        dataArr["gv"] = 21;
        dataArr["action"] = "fromimpstore";
        dataArr["itemtype"] = target.dataset.type;

        webCall(
          "hotrods/inventory_actions",
          dataArr,
          function (webData) {
            let flshArr = flshToArr(webData);
            implantStorage = JSON.parse(flshArr["impdata"]);
            delete flshArr["impdata"];
            updateIntoArr(flshArr, userVars);
            totalStoredImplants = 0;
            for (let implantType in implantStorage) {
              totalStoredImplants += parseInt(
                implantStorage[implantType]["quantity"]
              );
            }
            implantBucketMenuCapacity.textContent =
              totalStoredImplants +
              " / " +
              userVars["PLAYERVARS_implant_storage_slots"];

            populateInventory();
            populateImplantList();
            populateImplants();
            promptEnd();
          },
          true
        );
      };

      let equipAction = function (evt) {
        promptLoading();
        let target = evt.currentTarget;
        target.disabled = true;

        var dataArr = {};
        dataArr["pagetime"] = userVars["pagetime"];
        dataArr["templateID"] = userVars["template_id"];
        dataArr["sc"] = userVars["sc"];
        dataArr["userID"] = userVars["userID"];
        dataArr["password"] = userVars["password"];
        dataArr["gv"] = 21;
        dataArr["action"] = "equipimpfromstore";
        dataArr["itemtype"] = target.dataset.type;

        webCall(
          "hotrods/inventory_actions",
          dataArr,
          function (webData) {
            let flshArr = flshToArr(webData);
            implantStorage = JSON.parse(flshArr["impdata"]);
            delete flshArr["impdata"];
            updateIntoArr(flshArr, userVars);
            totalStoredImplants = 0;
            for (let implantType in implantStorage) {
              totalStoredImplants += parseInt(
                implantStorage[implantType]["quantity"]
              );
            }
            implantBucketMenuCapacity.textContent =
              totalStoredImplants +
              " / " +
              userVars["PLAYERVARS_implant_storage_slots"];

            populateInventory();
            populateImplantList();
            populateImplants();
            promptEnd();
          },
          true
        );
      };

      let canEquipImplant = function (implantType) {
        let firstImpSlot = findFirstEmptyGenericSlot("implant");
        if (firstImpSlot !== false) {
          return isImplantAvailable(implantType, firstImpSlot, false, false);
        }
        return false;
      };

      let populateImplantList = function () {
        while (implantList.firstChild) {
          implantList.removeChild(implantList.firstChild);
        }
        for (let implant in implantStorage) {
          let implantIItem = new InventoryItem(implant);

          let implantElem = document.createElement("div");
          implantElem.style = "height: 36px; overflow: hidden;";
          implantElem.style.backgroundImage =
            "url(https://files.deadfrontier.com/deadfrontier/inventoryimages/large/" +
            pickItemImageSubStr(implant) +
            ".png)";
          implantElem.style.backgroundPosition = "60% 0";
          implantElem.style.backgroundRepeat = "no-repeat";
          implantElem.classList.add("fakeItem");
          implantElem.dataset.type = implant;
          implantElem.dataset.quantity = 1;
          implantElem.style.position = "relative";

          let implantName = document.createElement("div");
          implantName.classList.add("opElem");
          implantName.style.left = "10px";
          implantName.style.top = "2px";
          implantName.textContent =
            typeof implantIItem.name !== "undefined"
              ? implantIItem.name
              : globalData[implantIItem.type]["name"];
          implantElem.appendChild(implantName);

          let implantQuantity = document.createElement("div");
          implantQuantity.classList.add("opElem");
          implantQuantity.style.left = "10px";
          implantQuantity.style.bottom = "2px";
          implantQuantity.textContent =
            "Quantity Stored: " + implantStorage[implant]["quantity"];
          implantElem.appendChild(implantQuantity);

          let implantEquip = document.createElement("button");
          implantEquip.classList.add("opElem");
          implantEquip.style.right = "110px";
          implantEquip.style.bottom = "2px";
          implantEquip.textContent = "Equip 1";
          implantEquip.dataset.type = implant;
          implantEquip.addEventListener("mousemove", function (evt) {
            let firstImpSlot = findFirstEmptyGenericSlot("implant");
            if (firstImpSlot !== false) {
              isImplantAvailable(
                evt.currentTarget.dataset.type,
                firstImpSlot,
                true,
                false
              );
              return;
            }
            displayPlacementMessage(
              "Implant slots full",
              mousePos[0] + 10,
              mousePos[1] + 10,
              "ERROR"
            );
          });
          implantEquip.addEventListener("mouseleave", cleanPlacementMessage);
          if (canEquipImplant(implant)) {
            implantEquip.addEventListener("click", equipAction);
          } else {
            implantEquip.disabled = true;
          }
          implantElem.appendChild(implantEquip);

          let implantWithdraw = document.createElement("button");
          implantWithdraw.classList.add("opElem");
          implantWithdraw.style.right = "10px";
          implantWithdraw.style.bottom = "2px";
          implantWithdraw.textContent = "Withdraw 1";
          implantWithdraw.dataset.type = implant;
          implantWithdraw.addEventListener("click", withdrawAction);
          if (findFirstEmptyGenericSlot("inv") === false) {
            implantWithdraw.disabled = true;
          }
          implantElem.appendChild(implantWithdraw);

          implantList.appendChild(implantElem);
        }
      };
      populateImplantList();
      implantBucketMenu.appendChild(implantList);

      inventoryHolder.appendChild(implantBucketMenu);
    });

    let bucketCanvas = document.createElement("canvas");
    bucketCanvas.width = 100;
    bucketCanvas.height = 120;
    bucketCanvas.dataset.override = 1;
    implantBucket.appendChild(bucketCanvas);

    implantMenu.appendChild(implantBucket);
  }

  populateImplants();
  populateBackpack();
}

function initiateInventoryEquipment() {
  createCommonInventoryItems();

  let implantMenu = document.getElementById("implantMenu");
  implantMenu.dataset.opened = "open";

  if (
    userVars["DFSTATS_df_profession"] === "Doctor" ||
    userVars["DFSTATS_df_profession"] === "Chef" ||
    userVars["DFSTATS_df_profession"] === "Engineer" ||
    userVars["DFSTATS_df_profession"].indexOf("Ironman") !== -1
  ) {
    if (
      !(
        parseInt(userVars["DFSTATS_df_positionx"]) > 0 &&
        userVars["DFSTATS_df_minioutpost"] !== "1"
      )
    ) {
      var profSlot = document.createElement("div");
      profSlot.classList.add("opElem");
      profSlot.style.left = "78px";
      profSlot.style.top = "329px";
      profSlot.style.width = "52px";
      profSlot.style.height = "58px";
      profSlot.style.background = "no-repeat center center / cover";

      profSlot.classList.add("fakeSlot");
      profSlot.classList.add("hoverEffect");

      var profText = document.createElement("div");
      profText.classList.add("opElem");
      switch (userVars["DFSTATS_df_profession"]) {
        case "Doctor":
          profSlot.dataset.action = "newadminister";
          profText.textContent = "Administer";
          profSlot.style.backgroundImage =
            "url(https://files.deadfrontier.com/hotrods/hotrods_v" +
            hrV +
            "/HTML5/images/market_heal.png)";
          break;
        case "Chef":
          profSlot.dataset.action = "newcook";
          profText.textContent = "Cook";
          profSlot.style.backgroundImage =
            "url(https://files.deadfrontier.com/hotrods/hotrods_v" +
            hrV +
            "/HTML5/images/market_cook.png)";
          break;
        case "Engineer":
          profSlot.dataset.action = "newrepair";
          profText.textContent = "Repair Armour";
          profSlot.style.backgroundImage =
            "url(https://files.deadfrontier.com/hotrods/hotrods_v" +
            hrV +
            "/HTML5/images/market_repair.png)";
          profSlot.style.backgroundImage =
            "url(./hotrods/hotrods_v" +
            hrV +
            "/HTML5/images/market_repair.png)"; // remove line
          break;
      }
      if (userVars["DFSTATS_df_profession"].indexOf("Ironman") !== -1) {
        profSlot.dataset.action = "ironmanaction";
        profText.textContent = "Iron Service";
        profSlot.style.backgroundImage =
          "url(https://files.deadfrontier.com/hotrods/hotrods_v" +
          hrV +
          "/HTML5/images/market_ironman.png)";
        profSlot.style.backgroundImage =
          "url(./hotrods/hotrods_v" + hrV + "/HTML5/images/market_ironman.png)"; // remove line
      }
      profText.style.width = "120px";

      inventoryHolder.appendChild(profSlot);

      profText.style.left =
        profSlot.offsetLeft + profSlot.offsetWidth / 2 - 60 + "px";
      profText.style.top = "380px";

      inventoryHolder.appendChild(profText);
    }
  }

  initiateCharacterInventory();
}

function initiateCharacterInventory() {
  renderAvatarUpdate(inventoryHolder.querySelector(".characterRender"));
  var charElem = document.getElementById("character").querySelector("div");
  charElem.innerHTML = "";
  charElem.textContent = "";
  // bpm - -20
  var slotTypes = [
    ["hat", 242, 67, 40],
    ["mask", 378, 67, 39],
    ["coat", 222, 146, 38],
    ["armour", 418, 146, 34],
    ["shirt", 242, 231, 36],
    ["trousers", 398, 231, 37],
    ["backpack", 436, 67, 35],
  ];

  for (var i in slotTypes) {
    var holder = document.createElement("div");
    holder.classList.add("opElem");
    holder.dataset.slottype = slotTypes[i][0];
    holder.dataset.slot = slotTypes[i][3];
    holder.style.left = slotTypes[i][1] + "px";
    holder.style.top = slotTypes[i][2] + "px";
    holder.classList.add("validSlot");
    charElem.appendChild(holder);
    var slotText = document.createElement("div");
    var actualText =
      slotTypes[i][0].charAt(0).toUpperCase() + slotTypes[i][0].slice(1);
    slotText.classList.add("opElem");
    slotText.classList.add("cashhack");
    slotText.classList.add("credits");
    slotText.dataset.cash = actualText;
    slotText.style.left = slotTypes[i][1] - 10 + "px";
    slotText.style.top = slotTypes[i][2] + 46 + "px";
    slotText.style.width = "64px";
    slotText.textContent = actualText;
    charElem.appendChild(slotText);
  }

  slotTypes = [
    [222, 320, 31],
    [320, 340, 32],
    [418, 320, 33],
  ];
  // 31,32,33
  var actualText = "Weapon";
  for (var i = 0; i < 3; i++) {
    var holder = document.createElement("div");
    holder.classList.add("opElem");
    holder.dataset.slottype = "weapon";
    holder.dataset.slot = slotTypes[i][2];
    holder.style.left = slotTypes[i][0] + "px";
    holder.style.top = slotTypes[i][1] + "px";
    holder.classList.add("validSlot");
    charElem.appendChild(holder);
    var slotText = document.createElement("div");
    slotText.classList.add("opElem");
    slotText.classList.add("cashhack");
    slotText.classList.add("credits");
    slotText.dataset.cash = actualText;
    slotText.style.left = slotTypes[i][0] - 10 + "px";
    slotText.style.top = slotTypes[i][1] + 46 + "px";
    slotText.style.width = "64px";
    slotText.textContent = actualText;
    charElem.appendChild(slotText);
  }

  var charMouth = document.createElement("div");
  charMouth.dataset.action = "giveToChar";
  charMouth.classList.add("fakeSlot");

  charElem.appendChild(charMouth);

  populateCharacterInventory();
}

function getItemType(itemData) {
  if (itemData["implant"]) {
    return "implant";
  } else if (itemData["clothingtype"]) {
    if (itemData["clothingtype"] === "mask2") {
      return "mask";
    }
    return itemData["clothingtype"];
  } else {
    return itemData["itemtype"];
  }
}

function populateImplants() {
  for (let impSlot of document.querySelectorAll("#implants .validSlot")) {
    while (impSlot.lastChild) {
      impSlot.removeChild(impSlot.lastChild);
    }
  }
  for (var i = 1; i <= parseInt(userVars["DFSTATS_df_implantslots"]); i++) {
    if (userVars["DFSTATS_df_implant" + i + "_type"] !== "") {
      setSlotData(
        userVars["DFSTATS_df_implant" + i + "_type"],
        "1",
        document
          .getElementById("implants")
          .querySelector("td[data-slot='" + i + "'].validSlot")
      );
    }
  }

  if (
    window.location.pathname.indexOf("/DF3D/") === -1 &&
    window.location.search.indexOf("page=31") === -1
  ) {
    if (document.getElementById("implantPresetLoadButton")) {
      let disablePresetLoadButton = false;
      for (let pIndex in implantPresets) {
        if (
          pIndex ===
          document.getElementById("implantPresetMenuSelect").dataset.value
        ) {
          for (let i = 1; i <= userVars["DFSTATS_df_implantslots"]; i++) {
            if (
              implantPresets[pIndex]["df_implant" + i + "_type"] !==
              userVars["DFSTATS_df_implant" + i + "_type"]
            ) {
              break;
            }
            if (i == userVars["DFSTATS_df_implantslots"]) {
              disablePresetLoadButton = true;
            }
          }
          if (!canDoImplantSwap(pIndex)) {
            disablePresetLoadButton = true;
            document.getElementById(
              "implantPresetLoadButton"
            ).dataset.pmoverride = "full";
          }
          break;
        }
      }
      document.getElementById("implantPresetLoadButton").disabled =
        disablePresetLoadButton;
    }
    let implantBucket = document.getElementById("implantbucket");
    let totalStoredImplants = 0;
    for (let implantType in implantStorage) {
      totalStoredImplants += parseInt(implantStorage[implantType]["quantity"]);
    }
    if (totalStoredImplants >= userVars["PLAYERVARS_implant_storage_slots"]) {
      implantBucket.disabled = true;
    } else {
      implantBucket.disabled = false;
    }

    let bucketCanvas = implantBucket.querySelector("canvas");
    let bucketCtx = bucketCanvas.getContext("2d");
    bucketCtx.clearRect(0, 0, bucketCanvas.width, bucketCanvas.height);
    /**
     *
     * @param {CanvasRenderingContext2D} ctx
     * @param {string} imgPath
     */
    let compileImage = function (ctx, pathArr, i = 0) {
      if (i < pathArr.length) {
        var img = new Image();
        img.onload = function () {
          if (pathArr[i][3] ?? false) {
            ctx.scale(-1, 1);
            ctx.drawImage(
              img,
              pathArr[i][1] ?? 0,
              pathArr[i][2] ?? 0,
              img.width * -1,
              img.height
            );
            ctx.scale(-1, 1);
          } else {
            ctx.drawImage(
              img,
              pathArr[i][1] ?? 0,
              pathArr[i][2] ?? 0,
              img.width,
              img.height
            );
          }
          i++;
          compileImage(ctx, pathArr, i);
        };
        img.onerror = function () {
          i++;
          compileImage(ctx, pathArr, i);
        };
        img.src = pathArr[i][0];
      }
    };

    let bucketImages = [];
    if (Object.keys(implantStorage).length > 0) {
      bucketImages.push([
        "hotrods/hotrods_v" + hrV + "/HTML5/images/inv/bucketback.png",
        0,
        20,
      ]);
      let i = 0;
      let implantDrawCodes = [
        [15, 10, false],
        [0, 10, true],
        [40, 10, false],
      ];
      for (let implantdata in implantStorage) {
        bucketImages.push([
          "https://files.deadfrontier.com/deadfrontier/inventoryimages/large/" +
            pickItemImageSubStr(implantdata) +
            ".png",
          ...implantDrawCodes[i],
        ]);
        i++;
        if (i > 2) {
          break;
        }
      }
      bucketImages.push([
        "hotrods/hotrods_v" + hrV + "/HTML5/images/inv/bucketfront.png",
        0,
        20,
      ]);
    } else {
      bucketImages.push([
        "hotrods/hotrods_v" + hrV + "/HTML5/images/inv/bucketback.png",
      ]);
      bucketImages.push([
        "hotrods/hotrods_v" + hrV + "/HTML5/images/inv/bucketfront.png",
      ]);
    }

    compileImage(bucketCtx, bucketImages);
  }
}

function populateBackpack() {
  let totalCurrentBackpackSlots = 0;

  if (
    typeof userVars["DFSTATS_df_backpack"] !== "undefined" &&
    userVars["DFSTATS_df_backpack"].length > 0
  ) {
    let iBackpack = new InventoryItem(userVars["DFSTATS_df_backpack"]);
    totalCurrentBackpackSlots = parseInt(globalData[iBackpack.type]["slots"]);
    if (typeof iBackpack.stats !== "undefined") {
      totalCurrentBackpackSlots += parseInt(iBackpack.stats);
    }
  }

  var backpackWindow = document.getElementById("backpackdisplay");
  if (backpackWindow === null) {
    return;
  }

  while (backpackWindow.firstChild) {
    backpackWindow.removeChild(backpackWindow.firstChild);
  }

  var invSection = document.createElement("tr");
  for (var y = 0; y < totalCurrentBackpackSlots; y++) {
    var slot = document.createElement("td");
    slot.dataset.slot = y + 1;
    slot.classList.add("validSlot");
    if (
      typeof userVars["DFSTATS_df_backpack" + (y + 1) + "_type"] !==
        "undefined" &&
      userVars["DFSTATS_df_backpack" + (y + 1) + "_type"] !== ""
    ) {
      setSlotData(
        userVars["DFSTATS_df_backpack" + (y + 1) + "_type"],
        userVars["DFSTATS_df_backpack" + (y + 1) + "_quantity"],
        slot
      );
    }
    invSection.appendChild(slot);

    if (invSection.childNodes.length >= 3) {
      backpackWindow.appendChild(invSection);
      invSection = document.createElement("tr");
    }
  }
  if (invSection.childNodes.length > 0 && invSection.childNodes.length < 3) {
    backpackWindow.appendChild(invSection);
  }

  var backpackLabel = document.getElementById("backpackLabel");
  var backpackWithdraw = document.getElementById("backpackWithdraw");
  if (totalCurrentBackpackSlots > 0) {
    backpackLabel.textContent = itemNamer(userVars["DFSTATS_df_backpack"], 1);
    backpackLabel.style.display = "block";
    backpackLabel.style.width = "";
    backpackLabel.style.left = backpackWindow.offsetLeft + "px";
    if (backpackWindow.offsetWidth > backpackLabel.offsetWidth) {
      backpackLabel.style.width = backpackWindow.offsetWidth + "px";
    }
    backpackLabel.style.bottom =
      document.getElementById("invController").offsetHeight -
      backpackWindow.offsetTop +
      "px";

    let totalItemsInPack = 0;
    for (var i = 1; i <= totalCurrentBackpackSlots; i++) {
      if (
        typeof userVars["DFSTATS_df_backpack" + i + "_type"] !== "undefined" &&
        userVars["DFSTATS_df_backpack" + i + "_type"].length > 0
      ) {
        if (!lockedSlots.includes(1050 + i + "")) {
          totalItemsInPack++;
        }
      }
    }
    let freeInventorySlots = 0;
    for (let i = 1; i <= userVars["DFSTATS_df_invslots"]; i++) {
      if (
        typeof userVars["DFSTATS_df_inv" + i + "_type"] === "undefined" ||
        userVars["DFSTATS_df_inv" + i + "_type"].length === 0
      ) {
        freeInventorySlots++;
      }
    }

    if (totalItemsInPack > 0 && freeInventorySlots >= totalItemsInPack) {
      backpackWithdraw.disabled = false;
    } else {
      if (totalItemsInPack > 0) {
        backpackWithdraw.dataset.pmoverride = "";
      } else {
        backpackWithdraw.dataset.pmoverride = "lock";
      }
      backpackWithdraw.disabled = true;
    }
    backpackWithdraw.style.display = "block";
    backpackWithdraw.style.width = "";
    backpackWithdraw.style.left = backpackWindow.offsetLeft + "px";
    if (backpackWindow.offsetWidth > backpackWithdraw.offsetWidth) {
      backpackWithdraw.style.width = backpackWindow.offsetWidth + "px";
    }
    backpackWithdraw.style.top =
      backpackWindow.offsetTop + backpackWindow.offsetHeight + "px";
    doLockedElems();
  } else {
    backpackLabel.style.display = "none";
    backpackWithdraw.style.display = "none";
    backpackWithdraw.disabled = true;
  }
}

function findFirstEmptyBackpackSlot() {
  var totalBackpackSlots = 0;
  if (
    typeof userVars["DFSTATS_df_backpack"] === "undefined" ||
    userVars["DFSTATS_df_backpack"].length === 0
  ) {
    return false;
  }

  let iBackpack = new InventoryItem(userVars["DFSTATS_df_backpack"]);
  var backpackType = userVars["DFSTATS_df_backpack"].split("_")[0];
  totalBackpackSlots = parseInt(globalData[backpackType]["slots"]);
  totalBackpackSlots +=
    typeof iBackpack.stats !== "undefined" ? parseInt(iBackpack.stats) : 0;

  for (let i = 1; i <= totalBackpackSlots; i++) {
    if (typeof userVars["DFSTATS_df_backpack" + i + "_type"] === "undefined") {
      return i;
    }
  }
  return false;
}

function populateCharacterInventory() {
  for (let charSlot of document.querySelectorAll("#character .validSlot")) {
    while (charSlot.lastChild) {
      charSlot.removeChild(charSlot.lastChild);
    }
  }
  var itemData = { type: "", image: "" };
  var temp;
  let targetSlot = document.querySelector(
    "#character :is(.validSlot,.blockedSlot)[data-slottype='hat']"
  );
  if (
    targetSlot !== null &&
    typeof userVars["DFSTATS_df_avatar_hat"] !== "undefined"
  ) {
    if (userVars["DFSTATS_df_avatar_hat"].length) {
      if (userVars["DFSTATS_df_avatar_hat"] !== "blocked_slot") {
        setSlotData(userVars["DFSTATS_df_avatar_hat"], "1", targetSlot);
      } else {
        targetSlot.classList.remove("validSlot");
        targetSlot.classList.add("blockedSlot");
      }
    } else {
      targetSlot.classList.remove("blockedSlot");
      targetSlot.classList.add("validSlot");
    }
  }

  targetSlot = document.querySelector(
    "#character :is(.validSlot,.blockedSlot)[data-slottype='mask']"
  );
  if (
    targetSlot !== null &&
    typeof userVars["DFSTATS_df_avatar_mask"] !== "undefined"
  ) {
    if (userVars["DFSTATS_df_avatar_mask"].length) {
      if (userVars["DFSTATS_df_avatar_mask"] !== "blocked_slot") {
        setSlotData(userVars["DFSTATS_df_avatar_mask"], "1", targetSlot);
      } else {
        targetSlot.classList.remove("validSlot");
        targetSlot.classList.add("blockedSlot");
      }
    } else {
      targetSlot.classList.remove("blockedSlot");
      targetSlot.classList.add("validSlot");
    }
  }

  targetSlot = document.querySelector(
    "#character :is(.validSlot,.blockedSlot)[data-slottype='coat']"
  );
  if (
    targetSlot !== null &&
    typeof userVars["DFSTATS_df_avatar_coat"] !== "undefined"
  ) {
    if (userVars["DFSTATS_df_avatar_coat"].length) {
      if (userVars["DFSTATS_df_avatar_coat"] !== "blocked_slot") {
        setSlotData(userVars["DFSTATS_df_avatar_coat"], "1", targetSlot);
      } else {
        targetSlot.classList.remove("validSlot");
        targetSlot.classList.add("blockedSlot");
      }
    } else {
      targetSlot.classList.remove("blockedSlot");
      targetSlot.classList.add("validSlot");
    }
  }

  targetSlot = document.querySelector(
    "#character :is(.validSlot,.blockedSlot)[data-slottype='shirt']"
  );
  if (
    targetSlot !== null &&
    typeof userVars["DFSTATS_df_avatar_shirt"] !== "undefined"
  ) {
    if (userVars["DFSTATS_df_avatar_shirt"].length) {
      if (userVars["DFSTATS_df_avatar_shirt"] !== "blocked_slot") {
        setSlotData(userVars["DFSTATS_df_avatar_shirt"], "1", targetSlot);
      } else {
        targetSlot.classList.remove("validSlot");
        targetSlot.classList.add("blockedSlot");
      }
    } else {
      targetSlot.classList.remove("blockedSlot");
      targetSlot.classList.add("validSlot");
    }
  }

  targetSlot = document.querySelector(
    "#character :is(.validSlot,.blockedSlot)[data-slottype='trousers']"
  );
  if (
    targetSlot !== null &&
    typeof userVars["DFSTATS_df_avatar_trousers"] !== "undefined"
  ) {
    if (userVars["DFSTATS_df_avatar_trousers"].length) {
      if (userVars["DFSTATS_df_avatar_trousers"] !== "blocked_slot") {
        setSlotData(userVars["DFSTATS_df_avatar_trousers"], "1", targetSlot);
      } else {
        targetSlot.classList.remove("validSlot");
        targetSlot.classList.add("blockedSlot");
      }
    } else {
      targetSlot.classList.remove("blockedSlot");
      targetSlot.classList.add("validSlot");
    }
  }

  targetSlot = document.querySelector(
    "#character .validSlot[data-slottype='backpack']"
  );
  if (targetSlot !== null) {
    if (
      typeof userVars["DFSTATS_df_backpack"] !== "undefined" &&
      userVars["DFSTATS_df_backpack"].length > 0
    ) {
      setSlotData(userVars["DFSTATS_df_backpack"], "1", targetSlot);
    }
  }

  for (let i = 1; i <= 3; i++) {
    targetSlot = document.querySelector(
      `#character .validSlot[data-slot='3${i}']`
    );
    if (targetSlot !== null) {
      if (
        typeof userVars[`DFSTATS_df_weapon${i}type`] !== "undefined" &&
        userVars[`DFSTATS_df_weapon${i}type`].length > 0
      ) {
        setSlotData(userVars[`DFSTATS_df_weapon${i}type`], "1", targetSlot);
      }
    }
  }

  targetSlot = document.querySelector(
    "#character .validSlot[data-slottype='armour']"
  );
  if (targetSlot !== null) {
    if (
      typeof userVars["DFSTATS_df_armourtype"] !== "undefined" &&
      userVars["DFSTATS_df_armourtype"].length > 0
    ) {
      setSlotData(
        userVars["DFSTATS_df_armourtype"],
        userVars["DFSTATS_df_armourhp"],
        targetSlot
      );
    }
  }

  populateBackpack();
}

function allowedInfoCard(elem) {
  if (
    elem &&
    typeof elem.classList !== "undefined" &&
    (typeof elem.dataset.type !== "undefined" ||
      typeof elem.parentNode.dataset.type !== "undefined") &&
    (elem.classList.contains("item") ||
      elem.classList.contains("fakeItem") ||
      elem.parentNode.classList.contains("fakeItem"))
  ) {
    return true;
  } else {
    return false;
  }
}

function ic_ImplantData(itemData, bonusStats = {}, expired = false) {
  let icOut = "";
  for (let bonusType of implantBonusesTypes) {
    let bonusStat = 0;
    if (
      typeof itemData[bonusType] !== "undefined" &&
      itemData[bonusType] != 0
    ) {
      bonusStat += parseFloat(itemData[bonusType]);
    }
    if (
      typeof bonusStats[bonusType] !== "undefined" &&
      bonusStats[bonusType] != 0
    ) {
      if (implantNegativeBonuses.includes(bonusType)) {
        if (implantIntBonuses.includes(bonusType)) {
          bonusStat -= parseInt(bonusStats[bonusType]);
        } else {
          bonusStat -= parseInt(bonusStats[bonusType]) / 100;
        }
      } else {
        if (implantIntBonuses.includes(bonusType)) {
          bonusStat += parseInt(bonusStats[bonusType]);
        } else {
          bonusStat += parseInt(bonusStats[bonusType]) / 100;
        }
      }
    }
    if (bonusStat != 0) {
      icOut += "<div class='itemData' style='";
      if (expired) {
        icOut +=
          "text-decoration: line-through; text-decoration-thickness: 3px;";
      }
      icOut += "";
      if (implantNegativeBonuses.includes(bonusType)) {
        if (bonusStat < 0) {
          icOut += "text-decoration-color: #D20303; color: #12FF00;'>+";
        } else {
          icOut += "text-decoration-color: #12FF00; color: #D20303;'>";
        }
        if (implantIntBonuses.includes(bonusType)) {
          icOut += bonusStat * -1;
        } else {
          icOut += (Math.round(bonusStat * 100000) / 1000) * -1;
        }
        icOut += implantBonusText[bonusType] + "</div>";
      } else {
        if (bonusStat > 0) {
          icOut += "text-decoration-color: #D20303; color: #12FF00;'>+";
        } else {
          icOut += "text-decoration-color: #12FF00; color: #D20303;'>";
        }
        if (implantIntBonuses.includes(bonusType)) {
          icOut += bonusStat;
        } else {
          icOut += Math.round(bonusStat * 100000) / 1000;
        }
        icOut += implantBonusText[bonusType] + "</div>";
      }
    }
  }
  return icOut;
}

function infoCard(e, override) {
  var colorMod = infoBox.style.borderColor;
  if (
    !active &&
    (!pageLock || allowInfoCardOverride) &&
    allowedInfoCard(e.target)
  ) {
    var target;
    if (e.target.parentNode.classList.contains("fakeItem")) {
      target = e.target.parentNode;
    } else {
      target = e.target;
    }
    if (
      infoBox.style.visibility === "hidden" ||
      curInfoItem !== target ||
      override === true
    ) {
      curInfoItem = target;
      var slotData = target.dataset.type.trim().split("_");
      let invItem = new InventoryItem(target.dataset.type);
      var itemData = globalData[invItem.type];
      colorMod = "";
      var cooked = false;
      if (target.dataset.type.indexOf("_cooked") >= 0) {
        cooked = true;
      }
      var itemName = itemNamer(target.dataset.type, target.dataset.quantity);
      let qualityStr = "";
      if (typeof qualityTranslation[target.dataset.quality] !== "undefined") {
        qualityStr = `<span style='color: #${
          qualityColors[target.dataset.quality]
        };'>${qualityTranslation[target.dataset.quality]}</span> `;
      }
      var icOut = "<div class='itemName'>" + qualityStr + itemName + "</div>";
      if (slotData.length > 1) {
        if (typeof invItem.ex !== "undefined") {
          if (parseInt(userVars["pagetime"]) > parseInt(invItem.ex)) {
            if (
              typeof itemData["expire_use"] !== "undefined" &&
              itemData["expire_use"]
            ) {
              icOut +=
                "<div class='itemName' style='color: #00ff00;'>Usable</div>";
            } else {
              icOut +=
                "<div class='itemName' style='color: #990000;'>Expired</div>";
            }
          } else {
            if (
              typeof itemData["expire_use"] !== "undefined" &&
              itemData["expire_use"]
            ) {
              icOut +=
                "<div class='itemName' style='color: #808080;'>Usable " +
                new Date(invItem.ex * 1000).toLocaleString(undefined, {
                  hour12: false,
                }) +
                "</div>";
            } else {
              icOut +=
                "<div class='itemName' style='color: #808080;'>Expires " +
                new Date(invItem.ex * 1000).toLocaleString(undefined, {
                  hour12: false,
                }) +
                "</div>";
            }
            icOut +=
              "<div class='itemName' style='color: #808080;'><span data-endtime='" +
              invItem.ex +
              "' data-precision='4' class='timeKeeper'>" +
              createMovingTimeString(
                parseInt(invItem.ex) - parseInt(userVars["pagetime"]),
                4
              ) +
              "</span> Remain</div>";
          }
        }
        if (typeof invItem.re !== "undefined") {
          icOut +=
            "<div class='itemName'>Reinforced " + invItem.re + "0%</div>";
        }
      }

      // cb indicator
      if (itemData["cb_exclude"] !== true) {
        icOut += "<div class='cbind'>CB</div>";
      }
      let appendInfo = "";

      let uniqueParameters = false;
      if (typeof itemData["unique_parameters"] !== "undefined") {
        let uniqueParametersTemp = itemData["unique_parameters"]
          .trim()
          .split(",");
        uniqueParameters = {};
        for (let key in uniqueParametersTemp) {
          uniqueParametersTemp[key] = uniqueParametersTemp[key].split("=");
          uniqueParameters[uniqueParametersTemp[key][0].trim()] =
            uniqueParametersTemp[key][1].trim();
        }
      }

      if (itemData["itemtype"] === "weapon") {
        if (itemData["melee"] == 0) {
          if (itemData["ammo_type"] !== "") {
            icOut +=
              "<div class='itemData'>" +
              globalData[itemData["ammo_type"]]["name"] +
              "</div>";
          }
          icOut +=
            "<div class='itemData'>" +
            itemData["bullet_capacity"] +
            " " +
            (itemData["stack_str"] ?? "Round Capacity") +
            "</div>";

          let reloadTime = itemData["reload_time"];
          if (reloadTime > 0 && reloadTime <= 90) {
            reloadTime = "Very Fast Reload Speed";
          } else if (reloadTime <= 120) {
            reloadTime = "Fast Reload Speed";
          } else if (reloadTime <= 180) {
            reloadTime = "Slow Reload Speed";
          } else if (reloadTime > 180) {
            reloadTime = "Very Slow Reload Speed";
          }
          icOut += "<div class='itemData'>" + reloadTime + "</div>";
        }

        let shotTime = itemData["shot_time"];
        if (shotTime < 60 && shotTime >= 40) {
          shotTime = "Slow";
        } else if (shotTime < 40 && shotTime >= 30) {
          shotTime = "Average";
        } else if (shotTime < 30 && shotTime >= 20) {
          shotTime = "Fast";
        } else if (shotTime < 20 && shotTime >= 7) {
          shotTime = "Very Fast";
        } else if (shotTime < 7 && shotTime >= 5) {
          shotTime = "Super Fast";
        } else if (shotTime < 5 && shotTime > 2.5) {
          shotTime = "F***ing Fast!";
        } else if (shotTime <= 2.5) {
          shotTime = "Ultra F***ing Fast!";
        } else if (shotTime > 60) {
          shotTime = "Super Slow";
        } else {
          shotTime = "Very Slow";
        }
        if (
          typeof itemData["melee"] !== "undefined" &&
          itemData["melee"] == 1
        ) {
          shotTime += " Attack Speed";
        } else {
          shotTime += " Firing Speed";
        }
        icOut += "<div class='itemData'>" + shotTime + "</div>";

        if (itemData["melee"] == 0) {
          let accuracyMod = itemData["accuracy_mod"];
          if (accuracyMod >= -8 && accuracyMod < 0) {
            accuracyMod = "High Accuracy";
          } else if (accuracyMod === 0) {
            accuracyMod = "Average Accuracy";
          } else if (accuracyMod > 0 && accuracyMod <= 10) {
            accuracyMod = "Low Accuracy";
          } else if (accuracyMod > 10 && accuracyMod <= 20) {
            accuracyMod = "Very Low Accuracy";
          } else if (accuracyMod > 20) {
            accuracyMod = "Ultra Low Accuracy";
          } else {
            accuracyMod = "Very High Accuracy";
          }
          icOut += "<div class='itemData'>" + accuracyMod + "</div>";
        }

        let criticalStr = itemData["critical"];
        if (criticalStr > 0 && criticalStr <= 0.1) {
          criticalStr = "Very Low Critical Chance";
        } else if (criticalStr > 0.1 && criticalStr <= 0.5) {
          criticalStr = "Low Critical Chance";
        } else if (criticalStr > 0.5 && criticalStr <= 1) {
          criticalStr = "Average Critical Chance";
        } else if (criticalStr > 1 && criticalStr <= 2) {
          criticalStr = "High Critical Chance";
        } else if (criticalStr > 2) {
          criticalStr = "Very High Critical Chance";
        } else {
          criticalStr = "Zero Critical Chance";
        }
        if (uniqueParameters !== false) {
          if (
            typeof uniqueParameters["CanAlwaysCrit"] !== "undefined" &&
            uniqueParameters["CanAlwaysCrit"] == 1
          ) {
            criticalStr = "Always Critical Hits";
          }
        }
        icOut += "<div class='itemData'>" + criticalStr + "</div>";

        if (
          typeof itemData["level_req"] !== "undefined" &&
          itemData["level_req"] > 0
        ) {
          icOut += "<div class='itemData'";
          if (userVars["DFSTATS_df_level"] < itemData["level_req"]) {
            icOut += " style='color: #B20108;'";
          }
          icOut +=
            ">Must be level " + itemData["level_req"] + " to Equip</div>";
        }
        if (
          typeof itemData["str_req"] !== "undefined" &&
          itemData["str_req"] > 0
        ) {
          icOut += "<div class='itemData'";
          if (userVars["DFSTATS_df_strength"] < itemData["str_req"]) {
            icOut += " style='color: #B20108;'";
          }
          icOut += ">" + itemData["str_req"] + " Strength Required</div>";
        }
        if (
          typeof itemData["pro_req"] !== "undefined" &&
          parseInt(itemData["pro_req"]) > 0
        ) {
          icOut += "<div class='itemData'";
          if (
            userVars["DFSTATS_df_pro" + itemData["pro_type"]] <
            itemData["pro_req"]
          ) {
            icOut += " style='color: #B20108;'";
          }
          icOut +=
            ">" +
            itemData["pro_req"] +
            " " +
            itemData["weptype"] +
            " Skill to Master</div>";
        }
        if (typeof itemData["weptype"] !== "undefined") {
          if (
            typeof itemData["chainsaw"] !== "undefined" &&
            itemData["chainsaw"] === 1
          ) {
            appendInfo +=
              "<span class='itemData' style='color: #897129;'>Chainsaw Mastery</span><br />";
          } else if (
            typeof itemData["type"] !== "undefined" &&
            itemData["type"] === "submachinegun"
          ) {
            appendInfo +=
              "<span class='itemData' style='color: #897129;'>SMG Mastery</span><br />";
          } else {
            appendInfo +=
              "<span class='itemData' style='color: #897129;'>" +
              itemData["weptype"] +
              " Mastery</span><br />";
          }
        }

        if (typeof itemData["selective_fire_type"] !== "undefined") {
          switch (itemData["selective_fire_type"]) {
            case "burst":
              fireString =
                parseFloat(itemData["selective_fire_amount"]) +
                " Round Burst Fire";
              break;
          }
          icOut +=
            "<div class='itemData' style='color: #cc7100;'>" +
            fireString +
            "</div>";
        }

        if (
          typeof itemData["flamethrower"] !== "undefined" &&
          itemData["flamethrower"] === true
        ) {
          if (
            typeof itemData["elemental"] !== "undefined" &&
            itemData["elemental"] === "cryo"
          ) {
            icOut +=
              "<div class='itemData' style='color: #00AAFF'>Frost-throwing Weapon</div>";
          } else {
            icOut +=
              "<div class='itemData' style='color: #e25822'>Flame-throwing Weapon</div>";
          }
        }

        if (typeof itemData["elemental"] !== "undefined") {
          switch (itemData["elemental"]) {
            case "cryo":
              icOut +=
                "<div class='itemData' style='color: #00AAFF'>Slows infected by " +
                itemData["elementalAmount"] * 100 +
                "% for " +
                itemData["elementalDuration"] +
                " seconds per hit</div>";
              break;
            case "armourpenetration":
              icOut +=
                "<div class='itemData' style='color: #9947ad'>" +
                itemData["elementalAmount"] +
                "% PvE Armour Penetration</div>";
              break;
            case "armourignore":
              icOut +=
                "<div class='itemData' style='color: #9947ad'>100% PvE Armour Penetration</div>";
              break;
          }
        }

        if (uniqueParameters !== false) {
          if (typeof uniqueParameters["MeleeHitCountAmount"] !== "undefined") {
            icOut +=
              "<div class='itemData' style='color: #FFA500;'>" +
              parseInt(uniqueParameters["MeleeHitCountAmount"]) +
              " Enemies Hit</div>";
          }

          if (
            typeof uniqueParameters["MeleeAttackRangeX"] !== "undefined" ||
            typeof uniqueParameters["MeleeAttackRangeY"] !== "undefined" ||
            typeof uniqueParameters["MeleeAttackRangeZ"] !== "undefined"
          ) {
            icOut +=
              "<div class='itemData' style='color: #FFA500;'>Enhanced Melee Damage Radius</div>";
          }

          if (typeof uniqueParameters["KnockbackMultiplier"] !== "undefined") {
            icOut +=
              "<div class='itemData' style='color: #FFA500;'>Enhanced Knockback</div>";
          }

          if (typeof uniqueParameters["ReducedNoiseRadius"] !== "undefined") {
            icOut +=
              "<div class='itemData' style='color: #FFA500;'>Reduces Noise by " +
              parseFloat(uniqueParameters["ReducedNoiseRadius"]) * 100 +
              "%</div>";
          }

          if (typeof uniqueParameters["SlowingEffect"] !== "undefined") {
            if (typeof uniqueParameters["SlowingAmount"] !== "undefined") {
              var multiplyByThisManyShots = 1;
              if (typeof itemData["shots_fired"] !== "undefined") {
                multiplyByThisManyShots = parseInt(itemData["shots_fired"]);
                if (multiplyByThisManyShots < 1) {
                  multiplyByThisManyShots = 1;
                }
              }
              icOut +=
                "<div class='itemData' style='color: #00AAFF;'>Slows infected by " +
                parseFloat(uniqueParameters["SlowingAmount"]) *
                  100 *
                  multiplyByThisManyShots +
                "%</div>";
            }
            if (typeof uniqueParameters["SlowingDuration"] !== "undefined") {
              icOut +=
                "<div class='itemData' style='color: #00AAFF;'>Slows for " +
                uniqueParameters["SlowingDuration"] +
                " Seconds</div>";
            }
          }

          if (typeof uniqueParameters["StunChance"] !== "undefined") {
            icOut +=
              "<div class='itemData' style='color: #FFA500;'>" +
              parseFloat(uniqueParameters["StunChance"]) +
              "% chance to stun enemy for " +
              uniqueParameters["StunTime"] +
              " Seconds</div>";
          }

          if (typeof uniqueParameters["elementalPenetration"] !== "undefined") {
            icOut +=
              "<div class='itemData' style='color: #f03eba;'>" +
              parseFloat(uniqueParameters["elementalPenetration"]) * 100 +
              "% Elemental Penetration</div>";
          }

          if (typeof uniqueParameters["specialFiringType"] !== "undefined") {
            switch (uniqueParameters["specialFiringType"]) {
              case "wallpenetration":
                icOut +=
                  "<div class='itemData' style='color:rgb(80, 194, 150);'>Shots penetrate walls and objects</div>";
                break;
            }
          }

          if (typeof uniqueParameters["maximumHitCount"] !== "undefined") {
            if (
              typeof uniqueParameters["splitSequence"] !== "undefined" &&
              uniqueParameters["splitSequence"] == 1.0
            ) {
              icOut +=
                "<div class='itemData' style='color: #FFA500;'>Damage is split evenly up to " +
                parseInt(uniqueParameters["maximumHitCount"]) +
                " targets<br />(Non-infected entities count as targets)</div>";
            }
          }
        }

        if (
          typeof itemData["damage_falloff_amount"] !== "undefined" &&
          typeof itemData["damage_falloff_startrange"] !== "undefined"
        ) {
          icOut +=
            "<div class='itemData'>-1% to -" +
            (100 - parseFloat(itemData["damage_falloff_amount"]) * 100) +
            "% damage after " +
            itemData["damage_falloff_startrange"] +
            " Meters</div>";
        }

        if (itemData["no_ammo"] > 0)
          icOut += "<div class='itemData'>Unlimited Ammo</div>";
        if (itemData["no_df2"] > 0)
          icOut +=
            "<div class='itemData' style='color: #AA0000;'>DF2 Reward Claimed</div>";
        if (target.dataset.type.indexOf("_stats") >= 0) {
          var n = target.dataset.type.indexOf("_stats") + 6;
          n = target.dataset.type.substring(n, n + 3);
          if (mcData[n] && mcData[n][0] !== "") {
            colorMod = mcData[n][0];
          }
          n = n.split("");
          for (var i in n) n[i] = parseInt(n[i]);
          if (n[0] > 0)
            icOut +=
              "<div class='itemData' style='color: #aba000;'>+" +
              n[0] +
              " Accuracy</div>";
          if (n[1] > 0)
            icOut +=
              "<div class='itemData' style='color: #aba000;'>+" +
              n[1] +
              " Reloading</div>";
          if (n[2] > 0)
            icOut +=
              "<div class='itemData' style='color: #aba000;'>+" +
              n[2] +
              " Critical Hit</div>";
          icOut += calcMCTag(target.dataset.type, true, "div", "itemData");
        }
      } else if (itemData["itemtype"] === "armour") {
        icOut +=
          "<div class='itemData' style='color: #" +
          damageColor(target.dataset.quantity, itemData["hp"]) +
          "'>" +
          target.dataset.quantity +
          " / " +
          itemData["hp"] +
          "</div>";
        icOut +=
          "<div class='itemData'>" +
          itemData["str"] +
          "% Damage Absorption</div>";
        if (
          typeof itemData["str_req"] !== "undefined" &&
          itemData["str_req"] > 0
        ) {
          icOut += "<div class='itemData'";
          if (
            parseInt(userVars["DFSTATS_df_strength"]) <
            parseInt(itemData["str_req"])
          ) {
            icOut += " style='color: #B20108;'";
          }
          icOut += ">" + itemData["str_req"] + " Strength Required</div>";
        }

        icOut += "<div class='itemData'";
        if (
          (userVars["DFSTATS_df_profession"].toLowerCase() === "engineer" &&
            userVars["df_level"] < itemData["shop_level"] - 5) ||
          (userVars["DFSTATS_df_profession"].indexOf("Ironman") !== -1 &&
            userVars["df_level"] < itemData["shop_level"] - 10)
        ) {
          icOut += " style='color: #B20108;'";
        }
        if (userVars["DFSTATS_df_profession"].indexOf("Ironman") !== -1) {
          icOut +=
            ">Repair by Engineer Level " +
            (itemData["shop_level"] - 10) +
            "</div>";
        } else {
          icOut +=
            ">Repair by Engineer Level " +
            (itemData["shop_level"] - 5) +
            "</div>";
        }

        if (target.dataset.type.indexOf("_stats") >= 0) {
          var n = target.dataset.type.indexOf("_stats") + 6;
          n = target.dataset.type.substring(n, n + 4);
          if (mcData[n] && mcData[n][0] !== "") {
            colorMod = mcData[n][0];
          }
          n = n.match(/[\s\S]{1,2}/g) || [];
          for (var i in n) n[i] = parseInt(n[i]);
          if (n[0] > 0)
            icOut +=
              "<div class='itemData' style='color: #aba000;'>+" +
              n[0] +
              " Agility</div>";
          if (n[1] > 0)
            icOut +=
              "<div class='itemData' style='color: #aba000;'>+" +
              n[1] +
              " Endurance</div>";
          icOut += calcMCTag(target.dataset.type, true, "div", "itemData");
        }

        if (typeof itemData["unique_parameters"] !== "undefined") {
          if (typeof uniqueParameters["damageGrace"] !== "undefined") {
            icOut +=
              "<div class='itemData' style='color: #F58216;'>" +
              parseFloat(uniqueParameters["damageGrace"]) * 100 +
              "% Chance to Reduce Incoming Damage to 1</div>";
          }

          if (typeof uniqueParameters["deathGrace"] !== "undefined") {
            icOut +=
              "<div class='itemData' style='color: #FFA500;'>" +
              parseFloat(uniqueParameters["deathGrace"]) * 100 +
              "% Chance to Survive Fatal Blow</div>";
          }

          if (typeof uniqueParameters["breakReduction"] !== "undefined") {
            icOut +=
              "<div class='itemData' style='color: #FFA500;'>" +
              parseFloat(uniqueParameters["breakReduction"]) * 100 +
              "% Extra Damage Reduction On Breaking Hit</div>";
          }

          if (typeof uniqueParameters["dRadMod"] !== "undefined") {
            icOut +=
              "<div class='itemData' style='color: #FFA500;'>Reduces Enemy Detection Radius by " +
              parseFloat(uniqueParameters["dRadMod"]) * 100 +
              "%</div>";
          }

          if (typeof uniqueParameters["lRadMod"] !== "undefined") {
            icOut +=
              "<div class='itemData' style='color: #FFA500;'>Reduces Enemy Sight by " +
              parseFloat(uniqueParameters["lRadMod"]) * 100 +
              "%</div>";
          }
        }
      } else if (itemData["itemtype"] === "backpack") {
        icOut +=
          "<div class='itemData' style='color: #12FF00;'>" +
          itemData["slots"] +
          " Backpack Slot" +
          (itemData["slots"] > 1 ? "s" : "") +
          "</div>";
        if (target.dataset.type.indexOf("_stats") >= 0) {
          var n = target.dataset.type.indexOf("_stats") + 6;
          n = target.dataset.type.substring(n, n + 1);
          if (mcData[n] && mcData[n][0] !== "") {
            colorMod = mcData[n][0];
          }
          n = parseInt(n);
          if (n > 0) {
            icOut +=
              "<div class='itemData' style='color: #aba000;'>+" +
              n +
              " Slots</div>";
          }
          icOut += calcMCTag(target.dataset.type, true, "div", "itemData");
        }
      } else if (itemData["itemtype"] === "item") {
        if (itemData["implant"] === true) {
          let bonusStats = {};
          if (
            typeof itemData["implant_stats"] !== "undefined" &&
            itemData["implant_stats"].length > 0
          ) {
            appendInfo += `<span style='color: #FFA500;'>${itemData["implant_stats_free_points"]} Total Points to Allocate</span><br />`;
            if (
              itemData["implant_stats_max_stat"] !=
              itemData["implant_stats_free_points"]
            ) {
              appendInfo += `<span style='color: #aba000;'>${itemData["implant_stats_max_stat"]} Points per Stat</span><br />`;
            }
            appendInfo += "Possible stats per allocation point";
            let allowedStatsToMod = itemData["implant_stats"].split(",");
            let implantMultipliers = itemData["implant_stats_multi"].split(",");
            for (let mult in implantMultipliers) {
              implantMultipliers[mult] = parseFloat(implantMultipliers[mult]);
            }
            let maxCombinations = allowedStatsToMod.length;
            let maxNeededBits = BigInt(
              itemData["implant_stats_max_stat"].toString(2).length
            );
            if (typeof invItem.stats !== "undefined") {
              let tempStats = bAnyToBInt(invItem.stats, 36);
              for (let i = maxCombinations - 1; i >= 0; i--) {
                if (typeof bonusStats[allowedStatsToMod[i]] === "undefined") {
                  bonusStats[allowedStatsToMod[i]] = 0;
                }
                let impBonus = Number(tempStats & (2n ** maxNeededBits - 1n));
                if (impBonus > parseInt(itemData["implant_stats_max_stat"])) {
                  impBonus = parseInt(itemData["implant_stats_max_stat"]);
                }
                bonusStats[allowedStatsToMod[i]] +=
                  impBonus * implantMultipliers[i];
                tempStats = tempStats >> maxNeededBits;
              }
            }
            for (let i = 0; i < maxCombinations; i++) {
              appendInfo += `<br /><span style='color: grey; font-style: italic;'>&emsp;${
                implantMultipliers[i]
              }${implantBonusText[allowedStatsToMod[i]]}</span>`;
            }
          }

          let specialStatStr = "";
          if (
            typeof itemData["implant_mods"] !== "undefined" &&
            itemData["implant_mods"].length > 0
          ) {
            appendInfo += `<span style='color: #FFA500;'>Roll upto ${itemData["implant_mods_total"]} stats</span><br />`;
            appendInfo += "Possible stats per roll";
            let allowedStatsToMod = itemData["implant_mods"].split(",");
            let maxCombinations = allowedStatsToMod.length;
            let maxStatsReplacement =
              itemData["implant_mods_negative"] +
              itemData["implant_mods_positive"];
            let maxNeededBits = BigInt(maxStatsReplacement.toString(2).length);
            let totalStatsRolledPerfect = 0;
            if (typeof invItem.stats !== "undefined") {
              let tempStats = bAnyToBInt(invItem.stats, 36);
              for (let i = maxCombinations - 1; i >= 0; i--) {
                if (typeof bonusStats[allowedStatsToMod[i]] === "undefined") {
                  bonusStats[allowedStatsToMod[i]] = 0;
                }
                let impBonus = Number(tempStats & (2n ** maxNeededBits - 1n));
                if (impBonus > maxStatsReplacement) {
                  impBonus = maxStatsReplacement;
                }
                impBonus -= itemData["implant_mods_negative"];
                bonusStats[allowedStatsToMod[i]] += impBonus;
                if (impBonus >= itemData["implant_mods_positive"]) {
                  totalStatsRolledPerfect++;
                }
                tempStats = tempStats >> maxNeededBits;
              }
              if (totalStatsRolledPerfect >= itemData["implant_mods_total"]) {
                specialStatStr +=
                  "<div class='itemData' style='color: #e6cc4d;'>Golden Roll</div>";
              }
            }
            for (let i = 0; i < maxCombinations; i++) {
              appendInfo += `<br /><span style='color: grey; font-style: italic;'>&emsp;-${
                itemData["implant_mods_negative"]
              }% to ${itemData["implant_mods_positive"]}${
                implantBonusText[allowedStatsToMod[i]]
              }</span>`;
            }
          }

          icOut += ic_ImplantData(
            itemData,
            bonusStats,
            typeof invItem.ex !== "undefined" &&
              invItem.ex < parseInt(userVars["pagetime"]) + timeSincePageLoaded
          );
          if (specialStatStr.length > 0) {
            icOut += specialStatStr;
          }
          if (
            typeof itemData["implant_mimicry"] !== "undefined" &&
            itemData["implant_mimicry"] === true
          ) {
            let positiveMult = itemData["implant_mimicry_positive_mult"] ?? 1;
            let negativeMult = itemData["implant_mimicry_negative_mult"] ?? 1;
            if (
              curInfoItem.parentNode.parentNode.parentNode.id === "implants"
            ) {
              let targetSlotNum = parseInt(curInfoItem.parentNode.dataset.slot);

              if (
                targetSlotNum - 1 > 0 &&
                userVars[`DFSTATS_df_implant${targetSlotNum - 1}_type`] !== ""
              ) {
                let targetItem = new InventoryItem(
                  userVars[`DFSTATS_df_implant${targetSlotNum - 1}_type`]
                );
                let iIndex = targetSlotNum - 1;
                while (
                  globalData[targetItem.type]["implant_mimicry"] === true
                ) {
                  positiveMult *=
                    globalData[targetItem.type][
                      "implant_mimicry_positive_mult"
                    ] ?? 1;
                  negativeMult *=
                    globalData[targetItem.type][
                      "implant_mimicry_negative_mult"
                    ] ?? 1;
                  iIndex--;
                  if (
                    iIndex > 0 &&
                    userVars[`DFSTATS_df_implant${iIndex}_type`] !== ""
                  ) {
                    targetItem = new InventoryItem(
                      userVars[`DFSTATS_df_implant${iIndex}_type`]
                    );
                  } else {
                    break;
                  }
                }
                let targetItemDisplayName =
                  targetItem.name ?? globalData[targetItem.type]["name"];
                if (
                  typeof targetItem.ex !== "undefined" &&
                  targetItem.ex <
                    parseInt(userVars["pagetime"]) + timeSincePageLoaded
                ) {
                  targetItemDisplayName = "Expired " + targetItemDisplayName;
                }
                icOut += `<div class='itemData'>Mimicking <span style='color: #aa0000;'>${targetItemDisplayName}</span></div>`;

                let implantBonuses = {};
                for (let i of implantBonusesTypes) {
                  implantBonuses[i] = 0;
                }
                let targetData = globalData[targetItem.type];

                if (
                  typeof targetData["implant_stats"] !== "undefined" &&
                  targetData["implant_stats"].length > 0
                ) {
                  let allowedStatsToMod =
                    targetData["implant_stats"].split(",");
                  let implantMultipliers =
                    targetData["implant_stats_multi"].split(",");
                  for (let mult in implantMultipliers) {
                    implantMultipliers[mult] = parseFloat(
                      implantMultipliers[mult]
                    );
                  }
                  let maxCombinations = allowedStatsToMod.length;
                  let maxNeededBits = BigInt(
                    targetData["implant_stats_max_stat"].toString(2).length
                  );
                  if (typeof targetItem.stats !== "undefined") {
                    let tempStats = bAnyToBInt(targetItem.stats, 36);
                    for (let i = maxCombinations - 1; i >= 0; i--) {
                      if (
                        typeof bonusStats[allowedStatsToMod[i]] === "undefined"
                      ) {
                        bonusStats[allowedStatsToMod[i]] = 0;
                      }
                      let impBonus = Number(
                        tempStats & (2n ** maxNeededBits - 1n)
                      );
                      if (
                        impBonus >
                        parseInt(targetData["implant_stats_max_stat"])
                      ) {
                        impBonus = parseInt(
                          targetData["implant_stats_max_stat"]
                        );
                      }
                      if (impBonus < 0) {
                        impBonus *= negativeMult;
                      } else {
                        impBonus *= positiveMult;
                      }
                      bonusStats[allowedStatsToMod[i]] +=
                        impBonus * implantMultipliers[i];
                      tempStats = tempStats >> maxNeededBits;
                    }
                  }
                }
                if (
                  typeof targetData["implant_mods"] !== "undefined" &&
                  targetData["implant_mods"].length > 0
                ) {
                  let allowedStatsToMod = targetData["implant_mods"].split(",");
                  let maxCombinations = allowedStatsToMod.length;
                  let maxStatsReplacement =
                    targetData["implant_mods_negative"] +
                    targetData["implant_mods_positive"];
                  let maxNeededBits = BigInt(
                    maxStatsReplacement.toString(2).length
                  );
                  if (typeof targetItem.stats !== "undefined") {
                    let tempStats = bAnyToBInt(targetItem.stats, 36);
                    for (let i = maxCombinations - 1; i >= 0; i--) {
                      if (
                        typeof bonusStats[allowedStatsToMod[i]] === "undefined"
                      ) {
                        bonusStats[allowedStatsToMod[i]] = 0;
                      }
                      let impBonus = Number(
                        tempStats & (2n ** maxNeededBits - 1n)
                      );
                      if (impBonus > maxStatsReplacement) {
                        impBonus = maxStatsReplacement;
                      }
                      impBonus -= targetData["implant_mods_negative"];
                      if (
                        implantNegativeBonuses.includes(allowedStatsToMod[i])
                      ) {
                        if (impBonus > 0) {
                          impBonus *= negativeMult;
                        } else {
                          impBonus *= positiveMult;
                        }
                      } else {
                        if (impBonus < 0) {
                          impBonus *= negativeMult;
                        } else {
                          impBonus *= positiveMult;
                        }
                      }
                      bonusStats[allowedStatsToMod[i]] += impBonus;
                      tempStats = tempStats >> maxNeededBits;
                    }
                  }
                }
                for (let j in implantBonuses) {
                  if (typeof targetData[j] !== "undefined") {
                    let bonusVal = parseFloat(targetData[j]);
                    // hardcoded reversals then regular handler
                    if (implantNegativeBonuses.includes(j)) {
                      if (bonusVal > 0) {
                        bonusVal *= negativeMult;
                      } else {
                        bonusVal *= positiveMult;
                      }
                    } else {
                      if (bonusVal < 0) {
                        bonusVal *= negativeMult;
                      } else {
                        bonusVal *= positiveMult;
                      }
                    }

                    implantBonuses[j] += bonusVal;
                  }
                }
                icOut += ic_ImplantData(
                  implantBonuses,
                  bonusStats,
                  typeof targetItem.ex !== "undefined" &&
                    targetItem.ex <
                      parseInt(userVars["pagetime"]) + timeSincePageLoaded
                );
              }
            }
            if (positiveMult > 1) {
              icOut += `<div class='itemData'>Positive Effects Multiplied by <span style='color: #00aa00;'>${positiveMult}</span></div>`;
            } else if (positiveMult < 1) {
              icOut += `<div class='itemData'>Positive Effects Multiplied by <span style='color: #aa0000;'>${positiveMult}</span></div>`;
            }
            if (negativeMult > 1) {
              icOut += `<div class='itemData'>Negative Effects Multiplied by <span style='color: #aa0000;'>${negativeMult}</span></div>`;
            } else if (negativeMult < 1) {
              icOut += `<div class='itemData'>Negative Effects Multiplied by <span style='color: #00aa00;'>${negativeMult}</span></div>`;
            }
          }

          if (itemData["implant_unique"] === true) {
            icOut +=
              "<div class='itemData'>Unique Implant (may only equip one)</div>";
          }
          if (itemData["implant_block"] && itemData["implant_block"] !== "") {
            icOut +=
              "<div class='itemData' style='color: #D20303;'>Cannot be used with:</div>";
            icOut +=
              "<div class='itemData' style='color: #D20303;'>" +
              itemData["implant_block_names"] +
              "</div>";
          }
        } else {
          if (
            itemData["foodrestore"] &&
            parseInt(itemData["foodrestore"]) > 0
          ) {
            var foodRestore = parseInt(itemData["foodrestore"]);
            var foodCooked = foodRestore * 2;
            if (!cooked && itemData["needcook"] === true) {
              icOut +=
                "<div class='itemData'>Restores " +
                foodRestore +
                " hunger</div>";
              icOut +=
                "<div class='itemData'>Restores " +
                foodCooked +
                " hunger if cooked</div>";
            } else {
              if (cooked) {
                icOut +=
                  "<div class='itemData'>Restores " +
                  foodCooked +
                  " hunger</div>";
              } else {
                icOut +=
                  "<div class='itemData'>Restores " +
                  foodRestore +
                  " hunger</div>";
              }
            }

            icOut +=
              "<div class='itemData'>Nutrition Level " +
              itemData["level"] +
              "</div>";
            /*var nutritionFactorColor = "";
						if (parseInt(userVars["DFSTATS_df_level"]) > parseInt(itemData["level"]) && parseInt(itemData["level"]) < 50 || parseInt(userVars["DFSTATS_df_level"]) > 70 && parseInt(itemData["level"]) === 50)
						{
							nutritionFactorColor = " style='color: #DD9203;'";
						}
						if (parseInt(userVars["DFSTATS_df_level"]) > parseInt(itemData["level"]) + 10 && parseInt(itemData["level"]) < 40 || parseInt(userVars["DFSTATS_df_level"]) > 70 && parseInt(itemData["level"]) === 40)
						{
							nutritionFactorColor = " style='color: #B20108;'";
						}
						icOut += nutritionFactorColor + ">Nutrition Level " + itemData["level"] + "</div>";*/
            if (!cooked && itemData["needcook"] === true) {
              if (
                (userVars["DFSTATS_df_profession"] === "Chef" &&
                  parseInt(userVars["DFSTATS_df_level"]) <
                    parseInt(itemData["level"]) - 5) ||
                (userVars["DFSTATS_df_profession"].indexOf("Ironman") !== -1 &&
                  parseInt(userVars["DFSTATS_df_level"]) <
                    itemData["level"] - 10)
              ) {
                if (
                  userVars["DFSTATS_df_profession"].indexOf("Ironman") !== -1
                ) {
                  icOut +=
                    "<div class='itemData' style='color: #B20108;'>Can be cooked by Chef Level " +
                    (itemData["level"] - 10) +
                    "+</div>";
                } else {
                  icOut +=
                    "<div class='itemData' style='color: #B20108;'>Can be cooked by Chef Level " +
                    (itemData["level"] - 5) +
                    "+</div>";
                }
              } else {
                if (
                  userVars["DFSTATS_df_profession"].indexOf("Ironman") !== -1
                ) {
                  icOut +=
                    "<div class='itemData'>Can be cooked by Chef Level " +
                    (itemData["level"] - 10) +
                    "+</div>";
                } else {
                  icOut +=
                    "<div class='itemData'>Can be cooked by Chef Level " +
                    (itemData["level"] - 5) +
                    "+</div>";
                }
              }
            }
          }
          if (itemData["healthrestore"] && itemData["healthrestore"] > 0) {
            var healthRestore = parseInt(itemData["healthrestore"]);
            var healthDoctor = healthRestore * 2;
            icOut +=
              "<div class='itemData'>Restores " +
              healthRestore +
              " health</div>";
            if (itemData["needdoctor"] === true) {
              icOut +=
                "<div class='itemData'>Restores " +
                healthDoctor +
                " health if administered</div>";
            }

            //icOut += "<div class='itemData'>Administer Level " + itemData["level"] + "</div>";

            if (itemData["needdoctor"] === true) {
              if (
                (userVars["DFSTATS_df_profession"] === "Doctor" &&
                  userVars["DFSTATS_df_level"] < itemData["level"] - 5) ||
                (userVars["DFSTATS_df_profession"].indexOf("Ironman") !== -1 &&
                  userVars["DFSTATS_df_level"] < itemData["level"] - 10)
              ) {
                if (
                  userVars["DFSTATS_df_profession"].indexOf("Ironman") !== -1
                ) {
                  icOut +=
                    "<div class='itemData' style='color: #B20108;'>Can be administered by Doctor Level " +
                    (itemData["level"] - 10) +
                    "+</div>";
                } else {
                  icOut +=
                    "<div class='itemData' style='color: #B20108;'>Can be administered by Doctor Level " +
                    (itemData["level"] - 5) +
                    "+</div>";
                }
              } else {
                if (
                  userVars["DFSTATS_df_profession"].indexOf("Ironman") !== -1
                ) {
                  icOut +=
                    "<div class='itemData'>Can be administered by Doctor Level " +
                    (itemData["level"] - 10) +
                    "+</div>";
                } else {
                  icOut +=
                    "<div class='itemData'>Can be administered by Doctor Level " +
                    (itemData["level"] - 5) +
                    "+</div>";
                }
              }
            }
          }
          if (
            typeof itemData["armourrestore"] !== "undefined" &&
            itemData["armourrestore"] > 0
          ) {
            let armourRestore = parseInt(itemData["armourrestore"]);
            let minDamageCanRestore = parseInt(itemData["min_repair"] ?? 0);
            let maxLevelToRestore = parseInt(itemData["max_repair"] ?? 100);

            icOut +=
              "<div class='itemData'>Repairs <span class='good'>" +
              armourRestore +
              "%</span> of armour durability</div>";
            if (minDamageCanRestore > 0) {
              icOut +=
                "<div class='itemData'>Cannot repair below <span class='bad'>" +
                minDamageCanRestore +
                "%</span> of max durability</div>";
            }
            if (maxLevelToRestore < 100) {
              icOut +=
                "<div class='itemData'>Cannot repair beyond <span class='bad'>" +
                maxLevelToRestore +
                "%</span> of max durability</div>";
            }
            icOut +=
              "<div class='itemData'>Hold <span style='color: #FFFF00; font-weight: bolder;'>[ALT]</span> to repair armour not equipped</div>";
          }
          if (itemData["canread"] === true) {
            icOut += "<div class='itemData'>Can be read</div>";
          }
          if (itemData["opencontents"] && itemData["opencontents"] > 0) {
            icOut += "<div class='itemData'>Can be opened</div>";
          }
          if (itemData["moneygift"] === true) {
            icOut += "<div class='itemData'>Can be opened for cash</div>";
          }
          if (itemData["barricade"] === true) {
            icOut += "<div class='itemData'>Can be used for Barricading</div>";
          }
          if (itemData["packsize"] && parseInt(itemData["packsize"]) > 0) {
            icOut +=
              "<div class='itemData'>Can hold " +
              parseInt(itemData["packsize"]) +
              " items</div>";
          }
          if (itemData["clothingtype"]) {
            icOut += "<div class='itemData'>Can be worn</div>";
            icOut +=
              "<div class='opElem' style='bottom: 5px; right: 5px; color: #333'>" +
              itemData["clothingtype"].charAt(0).toUpperCase() +
              itemData["clothingtype"].slice(1) +
              "</div>";
          }
          if (itemData["cashprotect"]) {
            icOut +=
              "<div class='itemData'>Will preserve up to $" +
              nf.format(itemData["cashprotect"]) +
              " on death</div>";
            icOut += "<div class='itemData'>Only your best box will work</div>";
          }

          if (
            itemData["boostexphours"] > 0 ||
            itemData["boostexphours_ex"] > 0
          ) {
            if (typeof itemData["boostexphours_ex"] === "undefined") {
              itemData["boostexphours_ex"] = 0;
            }
            var hours = Math.floor(
              parseFloat(itemData["boostexphours"]) +
                parseFloat(itemData["boostexphours_ex"])
            );
            var minutes =
              (
                (parseFloat(itemData["boostexphours"]) +
                  parseFloat(itemData["boostexphours_ex"])) %
                1
              ).toFixed(2) * 60;
            var baseExp = 50;
            if (
              itemData["boostexphours"] > 0 &&
              itemData["boostexphours_ex"] > 0
            ) {
              baseExp += 50;
            }
            icOut +=
              "<div class='itemData'>Boosts exp gain by +" + baseExp + "% for ";
            if (hours > 0) {
              icOut += hours + " hour";
              if (hours > 1) {
                icOut += "s";
              }
              if (minutes > 0) {
                icOut += " and " + minutes + " minute";
                if (minutes > 1) {
                  icOut += "s";
                }
              }
            } else {
              icOut += minutes + " minute";
              if (minutes > 1) {
                icOut += "s";
              }
            }
            icOut += "</div>";
          }
          if (
            itemData["boostdamagehours"] > 0 ||
            itemData["boostdamagehours_ex"] > 0
          ) {
            if (typeof itemData["boostdamagehours_ex"] === "undefined") {
              itemData["boostdamagehours_ex"] = 0;
            }
            var hours = Math.floor(
              parseFloat(itemData["boostdamagehours"]) +
                parseFloat(itemData["boostdamagehours_ex"])
            );
            var minutes =
              (
                (parseFloat(itemData["boostdamagehours"]) +
                  parseFloat(itemData["boostdamagehours_ex"])) %
                1
              ).toFixed(2) * 60;
            var baseDamage = 35;
            if (
              itemData["boostdamagehours"] > 0 &&
              itemData["boostdamagehours_ex"] > 0
            ) {
              baseDamage += 35;
            }
            icOut +=
              "<div class='itemData'>Boosts damage by +" +
              baseDamage +
              "% for ";
            if (hours > 0) {
              icOut += hours + " hour";
              if (hours > 1) {
                icOut += "s";
              }
              if (minutes > 0) {
                icOut += " and " + minutes + " minute";
                if (minutes > 1) {
                  icOut += "s";
                }
              }
            } else {
              icOut += minutes + " minute";
              if (minutes > 1) {
                icOut += "s";
              }
            }
            icOut += "</div>";
          }
          if (
            itemData["boostspeedhours"] > 0 ||
            itemData["boostspeedhours_ex"] > 0
          ) {
            if (typeof itemData["boostspeedhours_ex"] === "undefined") {
              itemData["boostspeedhours_ex"] = 0;
            }
            var hours = Math.floor(
              parseFloat(itemData["boostspeedhours"]) +
                parseFloat(itemData["boostspeedhours_ex"])
            );
            var minutes =
              (
                (parseFloat(itemData["boostspeedhours"]) +
                  parseFloat(itemData["boostspeedhours_ex"])) %
                1
              ).toFixed(2) * 60;

            var baseSpeed = 35;
            if (
              itemData["boostspeedhours"] > 0 &&
              itemData["boostspeedhours_ex"] > 0
            ) {
              baseSpeed += 35;
            }
            icOut +=
              "<div class='itemData'>Boosts run/walk speed by +" +
              baseSpeed +
              "% for ";
            if (hours > 0) {
              icOut += hours + " hour";
              if (hours > 1) {
                icOut += "s";
              }
              if (minutes > 0) {
                icOut += " and " + minutes + " minute";
                if (minutes > 1) {
                  icOut += "s";
                }
              }
            } else {
              icOut += minutes + " minute";
              if (minutes > 1) {
                icOut += "s";
              }
            }
            icOut += "</div>";
          }

          if (itemData["opencontents"] && itemData["opencontents"].length > 0) {
            icOut += "<div class='itemData'>Contents:</div>";
            var itemContents = itemData["opencontents"].split(",");
            var chances = {};
            var totalItems = 0;

            for (var j in itemContents) {
              if (!chances[itemContents[j]]) {
                chances[itemContents[j]] = 0;
              }
              chances[itemContents[j]]++;
              totalItems++;
            }
            for (var j in chances) {
              var chanceCalc =
                Math.floor((chances[j] / totalItems) * 1000) / 10 + "";
              if (chanceCalc.indexOf(".") === -1) {
                chanceCalc += ".0";
              }
              icOut +=
                "<div class='itemData' style='color: #12FF00;'> - " +
                chanceCalc +
                "% " +
                globalData[j]["name"] +
                "</div>";
            }

            /*for(var j in itemContents)
						 {
						 if($.inArray(itemContents[j], addedItems) >= 0)
						 {
						 continue;
						 } else {
						 icOut += "<div class='itemData' style='color: #12FF00;'> - " + globalData[itemContents[j]]["name"] + "</div>";
						 addedItems.push(itemContents[j]);
						 }
						 }*/

            icOut += "<div class='itemData'>Can be opened</div>";
          }

          if (itemData["gm_days"] && itemData["gm_days"] !== "0") {
            //icOut += "<div class='itemData' style='color: #897129; width: 250px;'>Grants " + itemData["gm_days"] + " days of Gold Membership on use (excluding monthly credits)</div>";
            icOut +=
              "<div class='itemData' style='color: #897129;'>Grants " +
              itemData["gm_days"] +
              " days of Gold Membership</div>";
            icOut +=
              "<div class='itemData' style='color: #12FF00;'>+ 2x exp</div>";
            icOut +=
              "<div class='itemData' style='color: #12FF00;'>+ 2x mission rewards</div>";
            icOut +=
              "<div class='itemData' style='color: #12FF00;'>+ Improved loot</div>";
            icOut +=
              "<div class='itemData' style='color: #12FF00;'>+ Credit shop discount</div>";
            //icOut += "<div class='itemData' style='color: #12FF00;'>+ F***ing Fast revive</div>";
            icOut +=
              "<div class='itemData' style='color: #B20108;'>- Excludes monthly credits</div>";
          }

          var whereBloodFrom = "";
          switch (slotData[0]) {
            case "crowsample":
              whereBloodFrom = "an infected crow";
              break;
            case "childsample":
              whereBloodFrom = "an infected child</div>";
              break;
            case "malesample":
              whereBloodFrom = "an infected male";
              break;
            case "femalesimple":
              whereBloodFrom = "an infected female";
              break;
            case "charredmalesample":
              whereBloodFrom = "an infected charred male";
              break;
            case "charredfemalesample":
              whereBloodFrom = "an infected charred female";
              break;
            case "mutantsample":
              whereBloodFrom = "a mutant";
              break;
            case "advancedmutantsample":
              whereBloodFrom = "an advanced mutant";
              break;
            case "behemothsample":
              whereBloodFrom = "a behemoth";
              break;
            case "radiatedsample":
              whereBloodFrom = "a radiated infected";
              break;
            case "radiatedcharredsample":
              whereBloodFrom = "a radiated charred infected";
              break;
            case "radiatedmutantsample":
              whereBloodFrom = "a radiated mutant";
              break;
            case "radiatedadvancedmutantsample":
              whereBloodFrom = "a radiated advanced mutant";
              break;
          }
          if (whereBloodFrom !== "") {
            icOut +=
              "<div class='itemData'>Blood sample from " +
              whereBloodFrom +
              "</div>";
          }
        }
      } else {
        if (itemData["itemtype"] === "ammo") {
          if (slotData[0] === "fuelammo") {
            icOut +=
              "<div class='itemData'>" + target.dataset.quantity + " mL</div>";
          } else {
            icOut +=
              "<div class='itemData'>" +
              target.dataset.quantity +
              " Rounds</div>";
          }
        } else if (itemData["itemtype"] === "broken") {
          icOut +=
            "<div class='itemData'>This item is broken. Please report this to support.</div>";
          icOut +=
            "<div class='itemData'>Item Code: " +
            target.dataset.broken +
            "</div>";
          icOut +=
            "<div class='itemData'>Container: " +
            target.parentNode.parentNode.parentNode.id +
            "</div>";
          icOut +=
            "<div class='itemData'>Item Slot: " +
            target.parentNode.dataset.slot +
            "</div>";
        }
      }

      let boostMod = 0;
      if (
        typeof itemData["movespeedmod"] !== "undefined" &&
        parseFloat(itemData["movespeedmod"]) != 0
      ) {
        icOut += "<div class='itemData' ";
        boostMod = parseFloat(itemData["movespeedmod"]);
        if (boostMod > 0) {
          icOut += "style='color: #12FF00;'>+";
        } else {
          icOut += "style='color: #D20303;'>";
        }
        icOut +=
          Math.round(boostMod * 100000) / 1000 + "% Movement Speed</div>";
      } //
      if (
        typeof itemData["damagemod"] !== "undefined" &&
        parseFloat(itemData["damagemod"]) != 0
      ) {
        icOut += "<div class='itemData' ";
        boostMod = parseFloat(itemData["damagemod"]);
        if (boostMod > 0) {
          icOut += "style='color: #12FF00;'>+";
        } else {
          icOut += "style='color: #D20303;'>";
        }
        icOut +=
          Math.round(boostMod * 100000) / 1000 + "% Damage Inflicted</div>";
      }
      if (
        typeof itemData["experiencemod"] !== "undefined" &&
        parseFloat(itemData["experiencemod"]) != 0
      ) {
        icOut += "<div class='itemData' ";
        boostMod = parseFloat(itemData["experiencemod"]);
        if (boostMod > 0) {
          icOut += "style='color: #12FF00;'>+";
        } else {
          icOut += "style='color: #D20303;'>";
        }
        icOut +=
          Math.round(boostMod * 100000) / 1000 + "% Experience Gain</div>";
      }

      if (/_colour(\d*)\^(\d*)\^(\d*)/.test(target.dataset.type)) {
        var rgbVal = target.dataset.type.match(/(\d*)\^(\d*)\^(\d*)/);
        rgbVal[0] = rgbVal[0].replace(/\^/g, ",");

        var rgbSep = rgbVal[1].split("^");
        for (var i = 0; i < rgbSep.length; i++) {
          rgbSep[i] = rgbSep[i] / 255;
        }
        var cMin = Math.min(rgbSep[0], rgbSep[1], rgbSep[2]);
        var cMax = Math.max(rgbSep[0], rgbSep[1], rgbSep[2]);
        var light = (cMax + cMin) / 2;
        light = +(light * 100).toFixed(1);

        if (light >= 50) {
          icOut +=
            "<div class='itemData' style='background-color: rgb(" +
            rgbVal[0] +
            "); width: 50%; color: black;'>Colour: " +
            rgbVal[0] +
            "</div>";
        } else {
          icOut +=
            "<div class='itemData' style='background-color: rgb(" +
            rgbVal[0] +
            "); width: 50%; color: white;'>Colour: " +
            rgbVal[0] +
            "</div>";
        }
      }

      if (target.dataset.type !== "credits") {
        icOut +=
          "<div class='itemData' style='color: #FFCC00;'>Scrap Price: $" +
          nf.format(scrapValue(target.dataset.type, target.dataset.quantity)) +
          "</div>";
      }

      if (itemData["no_transfer"] === true) {
        icOut += "<div class='itemData'>Non-Transferable</div>";
      } else if (target.dataset.type.indexOf("_nt") >= 0) {
        icOut +=
          "<div class='itemData' style='color: #aa0000;'>Non-Transferable</div>";
      }

      if (itemData["dismantle"] && itemData["dismantle"].length > 0) {
        icOut +=
          "<br /><div class='itemData' style='color: grey; font-style: italic;'>Can be dismantled into <br />" +
          generateTextItemList(itemData["dismantle"].split(","), "&emsp;") +
          "</div>";
      }

      if (itemData["notes"] && itemData["notes"] !== "") {
        icOut += "<br /><div class='itemData' style='color: ";
        if (itemData["notes_colour"] && itemData["notes_colour"] !== "") {
          icOut += "#" + itemData["notes_colour"];
        } else {
          icOut += "grey";
        }
        icOut += ";'>" + itemData["notes"] + "</div>";
      }

      if (itemData["description"] && itemData["description"] !== "") {
        icOut += "<br /><div class='itemData' style='color: ";
        if (
          itemData["description_colour"] &&
          itemData["description_colour"] !== ""
        ) {
          icOut += "#" + itemData["description_colour"];
        } else {
          icOut += "#897129";
        }
        icOut += ";'>" + itemData["description"] + "</div>";
      }

      if (
        typeof itemData["max_quantity"] !== "undefined" &&
        itemData["max_quantity"] > 1
      ) {
        if (
          target.parentNode.parentNode.parentNode.id === "inventory" ||
          target.parentNode.parentNode.parentNode.id === "backpackdisplay"
        ) {
          icOut +=
            "<div class='itemData' style='color: blue; position: absolute; bottom: 2px;'>Click while holding 'S' to split stack";
          if (parseInt(target.dataset.quantity) > itemData["max_quantity"]) {
            icOut +=
              "<br /><span style='color: #990000;'>You cannot split overstacks</span>";
          }
          icOut += "</div>";
        }
      }

      if (appendInfo.length > 0) {
        icOut += `<div class='itemData'>${appendInfo}</div>`;
      }

      if (target.classList.contains("fakeItem")) {
        if (target.parentNode.id === "recipes") {
          icOut += "<div class='itemData'>Required For Crafting:</div>";
          icOut +=
            "<div class='itemData' style='max-width: 240px; color: #ff6600;'>" +
            target.dataset.desc +
            "</div>"; //requiredItemsDesc
        }
        if (target.parentNode.parentNode.id === "venditron") {
          icOut +=
            "<div style='position: absolute; top: -18px; left: 0px; width: auto;'>";
          icOut += "<span class='itemData' style='position: relative; color: #";
          if (userVars["DFSTATS_df_cash"] < parseInt(target.dataset.vendcost)) {
            icOut += "ff0000";
          } else {
            icOut += "E6CC4D";
          }
          icOut +=
            "; border: 1px solid #990000; background-color: rgba(0,0,0,0.8); padding-left: 4px; padding-right: 4px;'>Costs: $" +
            nf.format(target.dataset.vendcost) +
            "</span>";
          if (userVars["DFSTATS_df_profession"].indexOf("Ironman") !== -1) {
            icOut +=
              "<span class='itemData' style='position: relative; color: #";
            if (parseInt(target.dataset.limit) <= 0) {
              icOut += "ff0000";
            } else {
              icOut += "E6CC4D";
            }
            icOut +=
              "; border: 1px solid #990000; background-color: rgba(0,0,0,0.8); padding-left: 4px; padding-right: 4px; margin-left: 6px'>Remaining: " +
              nf.format(target.dataset.limit) +
              "</span>";
          }
          icOut += "</div>";

          if (typeof target.dataset.limit !== "undefined") {
            let timeBackup = parseInt(target.dataset.cooldown);
            icOut += `Limited to ${target.dataset.limit} per ${createTimeString(
              timeBackup
            )} when purchasing.`;
          }
        }
      }

      icOut +=
        "<div class='opElem' style='color: #111; right: 0; bottom: 4px; line-height: 2px; user-select: none;'>" +
        x4jC(parseInt(userVars["userID"]).toString(36)) +
        "</div>";

      infoBox.innerHTML = icOut;
      infoBox.querySelector(".itemName").style.color = colorMod;
      infoBox.style.borderColor = colorMod;

      if (typeof itemData["image_override"] !== "undefined") {
        infoBox.style.backgroundImage =
          "url('https://files.deadfrontier.com/deadfrontier/inventoryimages/large/" +
          itemData["image_override"] +
          ".png')";
      } else if (
        itemData["itemtype"] === "armour" ||
        (itemData["itemtype"] === "item" &&
          typeof itemData["clothingtype"] !== "undefined" &&
          itemData["clothingtype"].length > 0)
      ) {
        infoBox.style.backgroundImage =
          "url('https://files.deadfrontier.com/deadfrontier/inventoryimages/large/" +
          slotData[0] +
          ".png')";
      } else {
        infoBox.style.backgroundImage =
          "url('https://files.deadfrontier.com/deadfrontier/inventoryimages/large/" +
          pickItemImageSubStr(target.dataset.type.trim()) +
          ".png')";
      }

      infoBox.dispatchEvent(hoverEvent);
      infoBox.style.visibility = "visible";
    }

    var invHoldOffsets = inventoryHolder.getBoundingClientRect();
    var specialPlacement = false;
    if (mousePos[1] - 30 - infoBox.offsetHeight < invHoldOffsets.top) {
      //infoBox.style.top = (mousePos[1] + 30 - invHoldOffsets.top) + "px";
      if (target.parentNode.parentNode.id === "venditron") {
        infoBox.style.top = "17px";
      } else {
        infoBox.style.top = "0px";
      }
      specialPlacement = true;
    } else {
      infoBox.style.top =
        mousePos[1] - 30 - infoBox.offsetHeight - invHoldOffsets.top + "px";
    }

    if (mousePos[0] + 20 + infoBox.offsetWidth > invHoldOffsets.right) {
      if (specialPlacement) {
        infoBox.style.left =
          mousePos[0] - 20 - infoBox.offsetWidth - invHoldOffsets.left + "px";
      } else {
        // original
        infoBox.style.left =
          inventoryHolder.offsetWidth - infoBox.offsetWidth + "px";
      }
    } else {
      infoBox.style.left = mousePos[0] + 20 - invHoldOffsets.left + "px";
    }
  } else {
    if (infoBox.style.visibility !== "hidden") {
      if (document.elementFromPoint(mousePos[0], mousePos[1]) !== infoBox) {
        infoBox.style.borderColor = colorMod;
        infoBox.style.visibility = "hidden";
      }
    }
  }
}

function clearCard(e) {
  if (expireTimer !== false) {
    clearInterval(expireTimer);
  }
  infoBox.style.visibility = "hidden";
  var underElem = document.elementFromPoint(mousePos[0], mousePos[1]);
  infoBox.style.visibility = "visible";
  if (!allowedInfoCard(underElem) || active) {
    if (infoBox.style.visibility !== "hidden") {
      infoBox.style.visibility = "hidden";
      infoBox.style.borderColor = "";
    }
  }
}

var colourArray = {};
colourArray["Black"] = ["000000", "ffffff"];
colourArray["White"] = ["ffffff", "000000"];
colourArray["Yellow"] = ["ffff00", "000000"];
colourArray["Grey"] = ["808080", "ffffff"];
colourArray["Brown"] = ["8b4513", "ffffff"];
colourArray["Red"] = ["ff0000", "000000"];
colourArray["Green"] = ["00ff00", "000000"];
colourArray["Blue"] = ["0000ff", "ffffff"];
colourArray["Forest Camo"] = ["78866b", "000000"];
colourArray["Desert Camo"] = ["f2cb8a", "000000"];
colourArray["Pink"] = ["FFC0CB", "000000"];
colourArray["Purple"] = ["800080", "FFFFFF"];
colourArray["Cyan"] = ["00FFFF", "000000"];
colourArray["Orange"] = ["FFA500", "000000"];

var moddedVars = userVars;

function dragDropAction(e) {
  var itemType = currentItem.dataset.type.split("_");
  fakeGrabbedItem.style.visibility = "hidden";
  var nSlot = document.elementFromPoint(mousePos[0], mousePos[1]);
  fakeGrabbedItem.style.visibility = "visible";
  while (nSlot.dataset.override) {
    nSlot = nSlot.parentNode;
  }

  if (
    (nSlot.classList.contains("fakeItem") ||
      nSlot.classList.contains("profitList")) &&
    nSlot.parentNode.classList.contains("fakeSlot")
  ) {
    nSlot = nSlot.parentNode;
  } else if (
    (nSlot.parentNode.classList.contains("fakeItem") ||
      nSlot.parentNode.classList.contains("profitList")) &&
    nSlot.parentNode.parentNode.classList.contains("fakeSlot")
  ) {
    nSlot = nSlot.parentNode.parentNode;
  }

  var question = false;
  var action, itemData;
  var extraData = {};
  switch (nSlot.dataset.action) {
    default:
      console.log(nSlot);
      return;
      break;
    case "discard":
      itemData = [
        parseInt(currentItem.parentNode.dataset.slot),
        currentItem.dataset.type,
        currentItem.parentNode.parentNode.parentNode.id,
      ];
      //var servData = [itemData, [0, "", "discard"]];
      extraData = [itemData, [0, "", "discard"]];
      df_prompt.innerHTML = "Are you sure you want to discard the ";
      if (itemData[1].indexOf("_name") >= 0) {
        var endOName = itemData[1].indexOf(
          "_",
          itemData[1].indexOf("_name") + 5
        );
        if (endOName > 0) {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemData[1].substring(itemData[1].indexOf("_name") + 5, endOName) +
            "</span>";
        } else {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemData[1].substring(itemData[1].indexOf("_name") + 5) +
            "</span>";
        }
      } else {
        df_prompt.innerHTML +=
          "<span style='color: red;'>" +
          globalData[itemType[0]]["name"] +
          "</span>";
      }
      df_prompt.innerHTML += "?";
      df_prompt.classList.add("warning");
      df_prompt.classList.add("redhighlight");
      action = updateInventory;
      question = true;
      break;
    case "simpleStore":
      shiftItem(currentItem);
      break;
    case "sendItemPrivate":
      extraData["sendto"] = userVars["member_to"];
    case "sellitem":
      itemData = [
        parseInt(currentItem.parentNode.dataset.slot),
        currentItem.dataset.type,
        currentItem.parentNode.parentNode.parentNode.id,
        currentItem.dataset.quantity,
      ];
      extraData["itemData"] = itemData;
      question = true;
      var priceHolder = document.createElement("div");
      priceHolder.style.position = "absolute";
      priceHolder.style.width = "100%";
      priceHolder.style.textAlign = "center";
      priceHolder.style.bottom = "30px";

      if (itemData[1] === "credits") {
        df_prompt.innerHTML =
          "How many <span style='color: red;'>Credits</span> would you like to sell and for how much?";
        var creditInput = document.createElement("input");
        creditInput.dataset.type = "credit";
        creditInput.style.color = "#cccccc";
        creditInput.style.backgroundColor = "#555555";
        creditInput.type = "number";
        creditInput.min = 0;
        creditInput.value = 100;
        if (parseInt(userVars["DFSTATS_df_credits"]) > 4000) {
          creditInput.max = 4000;
        } else {
          creditInput.max = userVars["DFSTATS_df_credits"];
        }
        var creditLabel = document.createElement("label");
        creditLabel.textContent = "C";
        creditLabel.style.color = "#cccccc";
        priceHolder.appendChild(creditLabel);
        priceHolder.appendChild(creditInput);
        priceHolder.appendChild(document.createElement("br"));
      } else {
        df_prompt.innerHTML = "How much would you like to sell the ";
        if (itemData[1].indexOf("_name") >= 0) {
          var endOName = itemData[1].indexOf(
            "_",
            itemData[1].indexOf("_name") + 5
          );
          if (endOName > 0) {
            df_prompt.innerHTML +=
              "<span style='color: red;'>" +
              itemData[1].substring(
                itemData[1].indexOf("_name") + 5,
                endOName
              ) +
              "</span>";
          } else {
            df_prompt.innerHTML +=
              "<span style='color: red;'>" +
              itemData[1].substring(itemData[1].indexOf("_name") + 5) +
              "</span>";
          }
        } else {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            globalData[itemType[0]]["name"] +
            "</span>";
        }
        df_prompt.innerHTML += " for?";
      }

      var priceLabel = document.createElement("label");
      priceLabel.textContent = "$";
      priceLabel.style.color = "#ffff00";
      var priceInput = document.createElement("input");
      priceInput.dataset.type = "price";
      priceInput.classList.add("moneyField");
      priceInput.type = "number";
      priceInput.max = 9999999999;
      priceInput.min = 0;

      if (marketLastMoney !== false && lastItemSold === itemType[0]) {
        priceInput.value = marketLastMoney;
      } else {
        priceInput.value = "";
        marketLastMoney = false;
        lastItemSold = itemType[0];
      }

      priceHolder.appendChild(priceLabel);
      priceHolder.appendChild(priceInput);

      df_prompt.appendChild(priceHolder);
      action = sellpriceConfirm;
      break;
    case "tradeitem":
      if (tradeTimer !== undefined) {
        stopQueryUpdate();
      }
      itemData = [
        parseInt(currentItem.parentNode.dataset.slot),
        currentItem.dataset.type,
        currentItem.parentNode.parentNode.parentNode.id,
        currentItem.dataset.quantity,
      ];
      extraData["itemData"] = itemData;
      // nSlot
      extraData["itemnum"] = currentItem.parentNode.dataset.slot;
      extraData["target"] = nSlot.dataset.target;
      extraData["credits"] = nSlot.dataset.credits;
      extraData["update"] = nSlot.dataset.update;
      extraData["trade"] = nSlot.dataset.trade;
      extraData["type"] = currentItem.dataset.type;
      extraData["cash"] = currentItem.dataset.quantity;
      extraData["action"] = "additem";
      question = true;

      df_prompt.innerHTML = "Are you sure you want to trade ";
      if (itemData[1].indexOf("_name") >= 0) {
        var endOName = itemData[1].indexOf(
          "_",
          itemData[1].indexOf("_name") + 5
        );
        if (endOName > 0) {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemData[1].substring(itemData[1].indexOf("_name") + 5, endOName) +
            "</span>";
        } else {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemData[1].substring(itemData[1].indexOf("_name") + 5) +
            "</span>";
        }
      } else {
        df_prompt.innerHTML +=
          "<span style='color: red;'>" +
          globalData[itemType[0]]["name"] +
          "</span>";
      }
      df_prompt.innerHTML += "?";

      action = doTradeAction;
      break;
    case "scrap":
      itemData = [
        parseInt(currentItem.parentNode.dataset.slot),
        currentItem.dataset.type,
        currentItem.parentNode.parentNode.parentNode.id,
      ];
      if (
        currentItem.parentNode.parentNode.parentNode.id === "backpackdisplay"
      ) {
        itemData[0] += 1050;
      }
      if (currentItem.parentNode.dataset.slottype === "implant") {
        itemData[0] += 1000;
      }
      //var servData = [itemData, [0, "", "discard"]];
      var scrapPrice = scrapValue(
        currentItem.dataset.type,
        currentItem.dataset.quantity
      );
      extraData = [itemData, scrapPrice];
      extraData["action"] = "scrap";
      df_prompt.innerHTML = "Are you sure you want to scrap the ";
      if (itemData[1].indexOf("_name") >= 0) {
        var endOName = itemData[1].indexOf(
          "_",
          itemData[1].indexOf("_name") + 5
        );
        if (endOName > 0) {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemData[1].substring(itemData[1].indexOf("_name") + 5, endOName) +
            "</span>";
        } else {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemData[1].substring(itemData[1].indexOf("_name") + 5) +
            "</span>";
        }
      } else {
        df_prompt.innerHTML +=
          "<span style='color: red;'>" +
          globalData[itemType[0]]["name"] +
          "</span>";
      }
      df_prompt.innerHTML +=
        " for <span style='color: #FFCC00;'>$" +
        nf.format(scrapPrice) +
        "</span>?";
      df_prompt.classList.add("warning");
      question = true;
      action = scrapItem;
      break;
    case "dismantle":
      itemData = [
        parseInt(currentItem.parentNode.dataset.slot),
        currentItem.dataset.type,
        currentItem.parentNode.parentNode.parentNode.id,
      ];
      //var servData = [itemData, [0, "", "discard"]];
      var scrapPrice = scrapValue(
        currentItem.dataset.type,
        currentItem.dataset.quantity
      );
      extraData = [itemData, scrapPrice];
      extraData["action"] = "dismantle";
      df_prompt.innerHTML = "Are you sure you want to dismantle the ";
      if (itemData[1].indexOf("_name") >= 0) {
        var endOName = itemData[1].indexOf(
          "_",
          itemData[1].indexOf("_name") + 5
        );
        if (endOName > 0) {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemData[1].substring(itemData[1].indexOf("_name") + 5, endOName) +
            "</span>";
        } else {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemData[1].substring(itemData[1].indexOf("_name") + 5) +
            "</span>";
        }
      } else {
        df_prompt.innerHTML +=
          "<span style='color: red;'>" +
          globalData[itemType[0]]["name"] +
          "</span>";
      }
      df_prompt.innerHTML +=
        " for <span style='color: #12FF00;'>" +
        generateTextItemList(globalData[itemType[0]]["dismantle"].split(",")) +
        "</span>?";
      df_prompt.classList.add("warning");
      question = true;
      action = scrapItem;
      break;
    case "enhance":
      itemData = [
        parseInt(currentItem.parentNode.dataset.slot),
        currentItem.dataset.type,
        currentItem.parentNode.parentNode.parentNode.id,
      ];
      if (
        currentItem.parentNode.parentNode.parentNode.id === "backpackdisplay"
      ) {
        itemData[0] += 1050;
      }
      if (currentItem.parentNode.dataset.slottype === "implant") {
        itemData[0] += 1000;
      }
      //var servData = [itemData, [0, "", "discard"]];
      var enhancePrice = enhanceValue(itemType[0]);
      extraData = [itemData, enhancePrice];
      df_prompt.innerHTML = "";
      if (itemData[1].indexOf("_stats") >= 0) {
        df_prompt.innerHTML +=
          "There is a chance this item will receive worse stats. ";
      }
      df_prompt.innerHTML += "Are you sure you want to enhance the ";
      if (itemData[1].indexOf("_name") >= 0) {
        var endOName = itemData[1].indexOf(
          "_",
          itemData[1].indexOf("_name") + 5
        );
        if (endOName > 0) {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemData[1].substring(itemData[1].indexOf("_name") + 5, endOName) +
            "</span>";
        } else {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemData[1].substring(itemData[1].indexOf("_name") + 5) +
            "</span>";
        }
      } else {
        df_prompt.innerHTML +=
          "<span style='color: red;'>" +
          globalData[itemType[0]]["name"] +
          "</span>";
      }
      df_prompt.innerHTML +=
        " for <span style='color: #FFCC00;'>$" +
        nf.format(enhancePrice) +
        "</span>?";
      action = function (data) {
        var dataArr = {};
        dataArr["pagetime"] = userVars["pagetime"];
        dataArr["templateID"] = userVars["template_ID"];
        dataArr["sc"] = userVars["sc"];
        dataArr["creditsnum"] = 0;
        dataArr["buynum"] = 0;
        dataArr["renameto"] = "";
        dataArr["expected_itemprice"] = "-1";
        dataArr["expected_itemtype2"] = "";
        dataArr["expected_itemtype"] = data[0][1];
        dataArr["itemnum2"] = "0";
        dataArr["itemnum"] = data[0][0];
        dataArr["price"] = data[1];
        dataArr["action"] = "enhance";
        dataArr["gv"] = 21;
        dataArr["userID"] = userVars["userID"];
        dataArr["password"] = userVars["password"];

        df_prompt.innerHTML =
          "<div style='text-align: center'>Loading, please wait...</div>";
        webCall(
          "inventory_new",
          dataArr,
          function (webData) {
            updateIntoArr(flshToArr(webData, "DFSTATS_"), userVars);
            populateInventory();
            populateCharacterInventory();
            updateAllFields();
            renderAvatarUpdate();
          },
          true
        );
      };
      question = true;
      break;
    case "reinforce":
      itemData = [
        parseInt(currentItem.parentNode.dataset.slot),
        currentItem.dataset.type,
        currentItem.parentNode.parentNode.parentNode.id,
      ];
      //var servData = [itemData, [0, "", "discard"]];
      var enhancePrice = enhanceValue(itemType[0]);
      extraData = [itemData, enhancePrice];
      df_prompt.innerHTML = "";
      df_prompt.innerHTML += "Are you sure you want to reinforce the ";
      if (itemData[1].indexOf("_name") >= 0) {
        var endOName = itemData[1].indexOf(
          "_",
          itemData[1].indexOf("_name") + 5
        );
        if (endOName > 0) {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemData[1].substring(itemData[1].indexOf("_name") + 5, endOName) +
            "</span>";
        } else {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemData[1].substring(itemData[1].indexOf("_name") + 5) +
            "</span>";
        }
      } else {
        df_prompt.innerHTML +=
          "<span style='color: red;'>" +
          globalData[itemType[0]]["name"] +
          "</span>";
      }
      df_prompt.innerHTML +=
        " for <span style='color: #12FF00;'>1 Rare Metal Scrap</span>? This cannot be reversed.";
      action = function (data) {
        var dataArr = {};
        dataArr["pagetime"] = userVars["pagetime"];
        dataArr["templateID"] = userVars["template_ID"];
        dataArr["sc"] = userVars["sc"];
        dataArr["creditsnum"] = 0;
        dataArr["buynum"] = 0;
        dataArr["renameto"] = "";
        dataArr["expected_itemprice"] = "-1";
        dataArr["expected_itemtype2"] = "";
        dataArr["expected_itemtype"] = data[0][1];
        dataArr["itemnum2"] = "0";
        dataArr["itemnum"] = data[0][0];
        dataArr["price"] = data[1];
        dataArr["action"] = "reinforce";
        dataArr["gv"] = 21;
        dataArr["userID"] = userVars["userID"];
        dataArr["password"] = userVars["password"];

        df_prompt.innerHTML =
          "<div style='text-align: center'>Loading, please wait...</div>";
        webCall(
          "inventory_new",
          dataArr,
          function (webData) {
            updateIntoArr(flshToArr(webData, "DFSTATS_"), userVars);
            populateInventory();
            populateCharacterInventory();
            updateAllFields();
          },
          true
        );
      };
      question = true;
      break;
    case "adye":
      itemData = [
        parseInt(currentItem.parentNode.dataset.slot),
        currentItem.dataset.type,
        currentItem.parentNode.parentNode.parentNode.id,
      ];
      //var servData = [itemData, [0, "", "discard"]];
      var dyePrice = dyeValue(itemType[0]);
      extraData = [itemData, dyePrice];
      df_prompt.innerHTML =
        "This will give the item a random colour. Are you sure you want to re-colour the ";
      if (itemData[1].indexOf("_name") >= 0) {
        var endOName = itemData[1].indexOf(
          "_",
          itemData[1].indexOf("_name") + 5
        );
        if (endOName > 0) {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemData[1].substring(itemData[1].indexOf("_name") + 5, endOName) +
            "</span>";
        } else {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemData[1].substring(itemData[1].indexOf("_name") + 5) +
            "</span>";
        }
      } else {
        df_prompt.innerHTML +=
          "<span style='color: red;'>" +
          globalData[itemType[0]]["name"] +
          "</span>";
      }
      df_prompt.innerHTML +=
        " for <span style='color: #FFCC00;'>$" +
        nf.format(dyePrice) +
        "</span>?";
      action = function (data) {
        var dataArr = {};
        dataArr["pagetime"] = userVars["pagetime"];
        dataArr["templateID"] = userVars["template_ID"];
        dataArr["sc"] = userVars["sc"];
        dataArr["creditsnum"] = 0;
        dataArr["buynum"] = 0;
        dataArr["renameto"] = "";
        dataArr["expected_itemprice"] = "-1";
        dataArr["expected_itemtype2"] = "";
        dataArr["expected_itemtype"] = data[0][1];
        dataArr["itemnum2"] = "0";
        dataArr["itemnum"] = data[0][0];
        dataArr["price"] = data[1];
        dataArr["action"] = "apprendye";
        dataArr["gv"] = 21;
        dataArr["userID"] = userVars["userID"];
        dataArr["password"] = userVars["password"];

        if (data[0][2] === "backpackdisplay") {
          dataArr["itemnum"] = parseInt(dataArr["itemnum"]) + 1050;
        }

        df_prompt.innerHTML =
          "<div style='text-align: center'>Loading, please wait...</div>";
        webCall(
          "inventory_new",
          dataArr,
          function (webData) {
            updateIntoArr(flshToArr(webData, "DFSTATS_"), userVars);
            populateInventory();
            populateCharacterInventory();
            updateAllFields();
            renderAvatarUpdate();
          },
          true
        );
      };
      question = true;
      break;
    case "cbadd":
      itemData = [
        parseInt(currentItem.parentNode.dataset.slot),
        currentItem.dataset.type,
        currentItem.parentNode.parentNode.parentNode.id,
      ];
      extraData = [itemData];
      df_prompt.innerHTML = "This will add ";
      if (itemData[1].indexOf("_name") >= 0) {
        var endOName = itemData[1].indexOf(
          "_",
          itemData[1].indexOf("_name") + 5
        );
        if (endOName > 0) {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemData[1].substring(itemData[1].indexOf("_name") + 5, endOName) +
            "</span>";
        } else {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemData[1].substring(itemData[1].indexOf("_name") + 5) +
            "</span>";
        }
      } else {
        df_prompt.innerHTML +=
          "<span style='color: red'>" +
          globalData[itemType[0].trim()]["name"] +
          "</span>";
      }
      df_prompt.innerHTML +=
        " to your collection book. There will be a charge of ";
      let invItem = new InventoryItem(currentItem.dataset.type);
      if (globalData[invItem.type]["no_transfer"] === true || invItem.nt) {
        df_prompt.innerHTML += "<span style='color: #FFCC00;'>$0</span>";
      } else {
        df_prompt.innerHTML +=
          "<span style='color: #FFCC00;'>$" +
          nf.format(
            scrapValue(currentItem.dataset.type, currentItem.dataset.quantity) *
              2
          ) +
          "</span>";
      }
      df_prompt.innerHTML += " to take out. Confirm?";
      question = true;
      action = function (data) {
        var dataArr = {};
        dataArr["pagetime"] = userVars["pagetime"];
        dataArr["templateID"] = userVars["template_id"];
        dataArr["sc"] = userVars["sc"];
        dataArr["userID"] = userVars["userID"];
        dataArr["password"] = userVars["password"];
        dataArr["gv"] = 21;
        dataArr["action"] = "add";
        dataArr["invslot"] = data[0][0];

        webCall(
          "hotrods/collectionbook",
          dataArr,
          function (webData) {
            cbContent = flshToArr(webData);
            if (cbContent["message"]) {
              df_prompt.innerHTML =
                "<div style='text-align: center'>" +
                cbContent["message"] +
                "</div>";
              df_prompt.innerHTML +=
                "<div style='text-align: center'>Reloading data...</div>";
              //df_prompt.parentNode.style.display = "block";
              setTimeout(function () {
                var dataArr = {};
                dataArr["userID"] = userVars["userID"];
                dataArr["password"] = userVars["password"];
                dataArr["sc"] = userVars["sc"];
                dataArr["pagetime"] = userVars["pagetime"];
                dataArr["action"] = "get";

                webCall(
                  "hotrods/collectionbook",
                  dataArr,
                  function (data) {
                    cbContent = flshToArr(data);
                    reloadInventoryData();
                    reloadCollectionBook();
                  },
                  true
                );
              }, 800);
            } else {
              reloadInventoryData();
              reloadCollectionBook();
            }
          },
          true
        );
      };
      break;
    case "mdye":
      itemData = [
        parseInt(currentItem.parentNode.dataset.slot),
        currentItem.dataset.type,
        currentItem.parentNode.parentNode.parentNode.id,
      ];

      var clothingImg = document.createElement("div");
      clothingImg.classList.add("opElem");
      clothingImg.style.width = "160px";
      clothingImg.style.height = "350px";
      clothingImg.style.left = "-150px";
      clothingImg.style.top = "-150px";
      clothingImg.classList.add("characterRender");
      clothingImg.style.opacity = "1";

      //var servData = [itemData, [0, "", "discard"]];
      df_prompt.innerHTML = "Please choose a colour below for the ";
      if (itemData[1].indexOf("_name") >= 0) {
        var endOName = itemData[1].indexOf(
          "_",
          itemData[1].indexOf("_name") + 5
        );
        if (endOName > 0) {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemData[1].substring(itemData[1].indexOf("_name") + 5, endOName) +
            "</span>";
        } else {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemData[1].substring(itemData[1].indexOf("_name") + 5) +
            "</span>";
        }
      } else {
        df_prompt.innerHTML +=
          "<span style='color: red;'>" +
          globalData[itemType[0]]["name"] +
          "</span>";
      }
      df_prompt.innerHTML += ". This will cost ";
      if (userVars["yard_use_credits"] === "1") {
        switch (globalData[itemType[0]]["itemtype"]) {
          case "weapon":
          case "armour":
            df_prompt.innerHTML += 250;
            extraData = [itemData, 250];
            break;
          case "item":
            df_prompt.innerHTML += 100;
            extraData = [itemData, 100];
            break;
        }
        df_prompt.innerHTML += " Credits";
      } else {
        df_prompt.innerHTML += "$";
        switch (globalData[itemType[0]]["itemtype"]) {
          case "weapon":
          case "armour":
            df_prompt.innerHTML += nf.format(1e6);
            //df_prompt.innerHTML += 250;
            extraData = [itemData, 250];
            break;
          case "item":
            df_prompt.innerHTML += nf.format(1e6);
            //df_prompt.innerHTML += 100;
            extraData = [itemData, 100];
            break;
        }
      }
      df_prompt.innerHTML += ".";

      var colourInput = document.createElement("select");
      colourInput.style.position = "absolute";
      colourInput.style.left = "78px";
      colourInput.style.top = "50px";
      colourInput.style.width = "100px";
      var colourOptions = globalData[itemType[0]]["othercolours"].split(",");
      colourInput.value = colourOptions[0];

      if (typeof colourArray[colourOptions[0]] === "undefined") {
        colourArray[colourOptions[0]] = ["ffffff", "000000"];
      }

      colourInput.style.backgroundColor =
        "#" + colourArray[colourOptions[0]][0];
      colourInput.style.color = "#" + colourArray[colourOptions[0]][1];
      for (var i in colourOptions) {
        if (typeof colourArray[colourOptions[i]] === "undefined") {
          colourArray[colourOptions[i]] = ["ffffff", "000000"];
        }
        var colourSelection = document.createElement("option");
        colourSelection.style.backgroundColor =
          "#" + colourArray[colourOptions[i]][0];
        colourSelection.style.color = "#" + colourArray[colourOptions[i]][1];
        colourSelection.value = colourOptions[i];
        colourSelection.textContent = colourOptions[i];

        colourInput.appendChild(colourSelection);
      }

      moddedVars = JSON.parse(JSON.stringify(userVars));

      var typeToChange = "";
      if (globalData[itemType[0]]["itemtype"] === "armour") {
        typeToChange = "DFSTATS_df_armourtype"; //
      } else if (
        globalData[itemType[0]]["itemtype"] === "item" &&
        globalData[itemType[0]]["clothingtype"]
      ) {
        typeToChange =
          "DFSTATS_df_avatar_" + globalData[itemType[0]]["clothingtype"];
      }

      moddedVars[typeToChange] = itemType[0] + "_colour" + colourOptions[0];
      if (!unblockedSlot(itemType[0])) {
        moddedVars["DFSTATS_df_avatar_" + blockingItem] = "";
      }

      colourInput.addEventListener("change", function () {
        colourInput.style.backgroundColor =
          "#" + colourArray[colourInput.value][0];
        colourInput.style.color = "#" + colourArray[colourInput.value][1];

        moddedVars[typeToChange] = itemType[0] + "_colour" + colourInput.value;

        renderAvatarUpdate(clothingImg, moddedVars);
      });
      df_prompt.appendChild(colourInput);
      df_prompt.appendChild(clothingImg);
      renderAvatarUpdate(clothingImg, moddedVars);
      action = function (data) {
        var dataArr = {};
        dataArr["pagetime"] = userVars["pagetime"];
        dataArr["templateID"] = userVars["template_ID"];
        dataArr["sc"] = userVars["sc"];
        dataArr["creditsnum"] = 0;
        dataArr["buynum"] = 0;
        dataArr["renameto"] = df_prompt.querySelector("select").value;
        dataArr["expected_itemprice"] = "-1";
        dataArr["expected_itemtype2"] = "";
        dataArr["expected_itemtype"] = data[0][1];
        dataArr["itemnum2"] = "0";
        dataArr["itemnum"] = data[0][0];
        dataArr["price"] = data[1];
        dataArr["action"] = "masterdye";
        dataArr["gv"] = 21;
        dataArr["userID"] = userVars["userID"];
        dataArr["password"] = userVars["password"];

        if (data[0][2] === "backpackdisplay") {
          dataArr["itemnum"] = parseInt(dataArr["itemnum"]) + 1050;
        }

        df_prompt.innerHTML =
          "<div style='text-align: center'>Loading, please wait...</div>";
        webCall(
          "inventory_new",
          dataArr,
          function (webData) {
            updateIntoArr(flshToArr(webData, "DFSTATS_"), userVars);
            populateInventory();
            populateCharacterInventory();
            updateAllFields();
            renderAvatarUpdate();
          },
          true
        );
      };
      question = true;
      break;
    case "godcraft":
      itemData = [
        parseInt(currentItem.parentNode.dataset.slot),
        currentItem.dataset.type,
        currentItem.parentNode.parentNode.parentNode.id,
      ];
      //var servData = [itemData, [0, "", "discard"]];
      var enhancePrice = enhanceValue(itemType[0]);
      df_prompt.innerHTML = "Are you sure you want to godcraft the ";
      if (itemData[1].indexOf("_name") >= 0) {
        var endOName = itemData[1].indexOf(
          "_",
          itemData[1].indexOf("_name") + 5
        );
        if (endOName > 0) {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemData[1].substring(itemData[1].indexOf("_name") + 5, endOName) +
            "</span>";
        } else {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemData[1].substring(itemData[1].indexOf("_name") + 5) +
            "</span>";
        }
      } else {
        df_prompt.innerHTML +=
          "<span style='color: red;'>" +
          globalData[itemType[0]]["name"] +
          "</span>";
      }

      if (userVars["yard_use_credits"] === "1") {
        df_prompt.innerHTML += " for ";
        switch (globalData[itemType[0]]["itemtype"]) {
          case "backpack":
            df_prompt.innerHTML += 50;
            extraData = [itemData, 50];
            break;
          case "weapon":
            df_prompt.innerHTML += 100;
            extraData = [itemData, 100];
            break;
          case "armour":
            df_prompt.innerHTML += 200;
            extraData = [itemData, 200];
            break;
        }
        df_prompt.innerHTML += " Credits";
      } else {
        df_prompt.innerHTML += " for $";
        switch (globalData[itemType[0]]["itemtype"]) {
          case "backpack":
            df_prompt.innerHTML += nf.format(5e6);
            //df_prompt.innerHTML += 50;
            extraData = [itemData, 50];
            break;
          case "weapon":
            df_prompt.innerHTML += nf.format(10e6);
            //df_prompt.innerHTML += 100;
            extraData = [itemData, 100];
            break;
          case "armour":
            df_prompt.innerHTML += nf.format(20e6);
            //df_prompt.innerHTML += 200;
            extraData = [itemData, 200];
            break;
        }
      }
      df_prompt.innerHTML += "?";
      action = function (data) {
        var dataArr = {};
        dataArr["pagetime"] = userVars["pagetime"];
        dataArr["templateID"] = userVars["template_ID"];
        dataArr["sc"] = userVars["sc"];
        dataArr["creditsnum"] = 0;
        dataArr["buynum"] = 0;
        dataArr["renameto"] = "";
        dataArr["expected_itemprice"] = "-1";
        dataArr["expected_itemtype2"] = "";
        dataArr["expected_itemtype"] = data[0][1];
        dataArr["itemnum2"] = "0";
        dataArr["itemnum"] = data[0][0];
        dataArr["price"] = data[1];
        dataArr["action"] = "godcraft";
        dataArr["gv"] = 21;
        dataArr["userID"] = userVars["userID"];
        dataArr["password"] = userVars["password"];

        if (data[0][2] === "backpackdisplay") {
          dataArr["itemnum"] = parseInt(dataArr["itemnum"]) + 1050;
        }

        df_prompt.innerHTML =
          "<div style='text-align: center'>Loading, please wait...</div>";
        webCall(
          "inventory_new",
          dataArr,
          function (webData) {
            updateIntoArr(flshToArr(webData, "DFSTATS_"), userVars);
            populateInventory();
            populateCharacterInventory();
            updateAllFields();
          },
          true
        );
      };
      question = true;
      break;
    case "rename":
      itemData = [
        parseInt(currentItem.parentNode.dataset.slot),
        currentItem.dataset.type,
        currentItem.parentNode.parentNode.parentNode.id,
      ];
      extraData = [itemData, 500];

      //df_prompt.textContent = "We ask that you only use names that fit with the setting of Dead Frontier. Staff reserve the right to remove names which they consider inconsisten with the style of the game. In this event, your credits will be refunded.";
      df_prompt.innerHTML = "Pick an appropriate name for your ";
      if (itemData[1].indexOf("_name") >= 0) {
        var endOName = itemData[1].indexOf(
          "_",
          itemData[1].indexOf("_name") + 5
        );
        if (endOName > 0) {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemData[1].substring(itemData[1].indexOf("_name") + 5, endOName) +
            "</span>";
        } else {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemData[1].substring(itemData[1].indexOf("_name") + 5) +
            "</span>";
        }
      } else {
        df_prompt.innerHTML +=
          "<span style='color: red;'>" +
          globalData[itemType[0]]["name"] +
          "</span>";
      }
      df_prompt.innerHTML += ".";

      var priceHolder = document.createElement("div");
      priceHolder.style.position = "relative";
      priceHolder.style.height = "43px";
      var priceLabel = document.createElement("label");
      priceLabel.textContent = "Custom Name: ";
      priceLabel.style.color = "#ffff00";
      var priceInput = document.createElement("input");
      priceInput.dataset.type = "newname";
      priceInput.style.backgroundColor = "rgba(160,160,160,0.3)";
      priceInput.style.width = "162px";
      priceInput.style.color = "red";
      priceInput.type = "text";
      priceInput.value = "";

      priceLabel.appendChild(priceInput);
      priceHolder.appendChild(priceLabel);
      df_prompt.appendChild(priceHolder);
      action = function (data) {
        var nickname = df_prompt.querySelector("input").value;

        df_prompt.innerHTML = "Are you sure you want to rename your ";
        if (data[0][1].indexOf("_name") >= 0) {
          var endOName = data[0][1].indexOf(
            "_",
            data[0][1].indexOf("_name") + 5
          );
          if (endOName > 0) {
            df_prompt.innerHTML +=
              "<span style='color: red;'>" +
              data[0][1].substring(data[0][1].indexOf("_name") + 5, endOName) +
              "</span>";
          } else {
            df_prompt.innerHTML +=
              "<span style='color: red;'>" +
              data[0][1].substring(data[0][1].indexOf("_name") + 5) +
              "</span>";
          }
        } else {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            globalData[itemType[0]]["name"] +
            "</span>";
        }
        df_prompt.innerHTML += " to <i>" + nickname + "</i> for ";

        if (userVars["yard_use_credits"] === "1") {
          df_prompt.innerHTML += "500 Credits";
        } else {
          df_prompt.innerHTML += "$" + nf.format(20e6);
        }
        df_prompt.innerHTML += "?";

        var noButton = document.createElement("button");

        noButton.style.position = "absolute";
        noButton.style.top = "72px";
        noButton.addEventListener("click", function () {
          df_prompt.parentNode.style.display = "none";
          df_prompt.innerHTML = "";
          pageLock = false;
        });
        noButton.textContent = "No";
        noButton.style.right = "86px";
        var yesButton = document.createElement("button");
        yesButton.textContent = "Yes";
        yesButton.style.position = "absolute";
        yesButton.style.left = "86px";
        yesButton.style.top = "72px";
        yesButton.addEventListener("click", function () {
          data[2] = nickname;
          var dataArr = {};
          dataArr["pagetime"] = userVars["pagetime"];
          dataArr["templateID"] = userVars["template_ID"];
          dataArr["sc"] = userVars["sc"];
          dataArr["creditsnum"] = 0;
          dataArr["buynum"] = 0;
          dataArr["renameto"] = data[2];
          dataArr["expected_itemprice"] = "-1";
          dataArr["expected_itemtype2"] = "";
          dataArr["expected_itemtype"] = data[0][1];
          dataArr["itemnum2"] = "0";
          dataArr["itemnum"] = data[0][0];
          dataArr["price"] = data[1];
          dataArr["action"] = "rename";
          dataArr["gv"] = 21;
          dataArr["userID"] = userVars["userID"];
          dataArr["password"] = userVars["password"];

          if (data[0][2] === "backpackdisplay") {
            dataArr["itemnum"] = parseInt(dataArr["itemnum"]) + 1050;
          }

          df_prompt.innerHTML =
            "<div style='text-align: center'>Loading, please wait...</div>";
          webCall(
            "inventory_new",
            dataArr,
            function (webData) {
              updateIntoArr(flshToArr(webData, "DFSTATS_"), userVars);
              populateInventory();
              populateCharacterInventory();
              updateAllFields();
            },
            true
          );
        });
        df_prompt.appendChild(yesButton);
        df_prompt.appendChild(noButton);
        df_prompt.onkeydown = function (e) {
          if (e.keyCode === 13) {
            df_prompt.onkeydown = null;
            yesButton.click();
          }
        };
        df_prompt.focus();
      };
      question = true;
      break;
    case "removerename":
      itemData = [
        parseInt(currentItem.parentNode.dataset.slot),
        currentItem.dataset.type,
        currentItem.parentNode.parentNode.parentNode.id,
      ];
      extraData = [itemData, 50];

      df_prompt.innerHTML =
        "Are you sure you want to remove the rename from your ";
      var endOName = itemData[1].indexOf("_", itemData[1].indexOf("_name") + 5);
      if (endOName > 0) {
        df_prompt.innerHTML +=
          "<span style='color: red;'>" +
          itemData[1].substring(itemData[1].indexOf("_name") + 5, endOName) +
          "</span>";
      } else {
        df_prompt.innerHTML +=
          "<span style='color: red;'>" +
          itemData[1].substring(itemData[1].indexOf("_name") + 5) +
          "</span>";
      }
      df_prompt.innerHTML += ". This will cost ";
      if (userVars["yard_use_credits"] === "1") {
        df_prompt.innerHTML += nf.format(50) + " Credits";
      } else {
        df_prompt.innerHTML += "$" + nf.format(2.5e6);
      }
      df_prompt.innerHTML += ".";

      action = function (data) {
        var dataArr = {};
        dataArr["pagetime"] = userVars["pagetime"];
        dataArr["templateID"] = userVars["template_ID"];
        dataArr["sc"] = userVars["sc"];
        dataArr["creditsnum"] = 0;
        dataArr["buynum"] = 0;
        dataArr["renameto"] = "";
        dataArr["expected_itemprice"] = "-1";
        dataArr["expected_itemtype2"] = "";
        dataArr["expected_itemtype"] = data[0][1];
        dataArr["itemnum2"] = "0";
        dataArr["itemnum"] = data[0][0];
        dataArr["price"] = data[1];
        dataArr["action"] = "removerename";
        dataArr["gv"] = 21;
        dataArr["userID"] = userVars["userID"];
        dataArr["password"] = userVars["password"];
        if (data[0][2] === "backpackdisplay") {
          dataArr["itemnum"] = parseInt(dataArr["itemnum"]) + 1050;
        }

        df_prompt.innerHTML =
          "<div style='text-align: center'>Loading, please wait...</div>";
        webCall(
          "inventory_new",
          dataArr,
          function (webData) {
            updateIntoArr(flshToArr(webData, "DFSTATS_"), userVars);
            populateInventory();
            populateCharacterInventory();
            updateAllFields();
          },
          true
        );
      };
      question = true;
      break;
    case "buyservice":
      // buyadminister
      // buyrepair
      // buycook
      df_prompt.innerHTML =
        "Are you sure you want to hire " +
        nSlot.parentNode.querySelector(".seller").textContent +
        " for <span style='color: #FFCC00;'>$" +
        nSlot.dataset.price +
        "</span>?";
      itemData = [
        parseInt(currentItem.parentNode.dataset.slot),
        currentItem.dataset.type,
        currentItem.parentNode.parentNode.parentNode.id,
      ];
      extraData[0] = itemData;
      extraData[1] = scrapAmount(
        currentItem.dataset.type,
        currentItem.dataset.quantity
      );
      extraData["buynum"] = nSlot.dataset.buynum;
      extraData["price"] = nSlot.dataset.price;
      switch (nSlot.dataset.profession) {
        case "Engineer":
          extraData["action"] = "buyrepair";
          break;
        case "Chef":
          extraData["action"] = "buycook";
          break;
        case "Doctor":
          extraData["action"] = "buyadminister";
          break;
      }
      action = function (data) {
        switch (data["action"]) {
          case "buyrepair":
            playSound("repair");
            break;
          case "buyadminister":
            playSound("heal");
            break;
          case "buycook":
            playSound("cook");
            break;
        }

        var dataArr = {};
        dataArr["pagetime"] = userVars["pagetime"];
        dataArr["templateID"] = userVars["template_ID"];
        dataArr["sc"] = userVars["sc"];
        dataArr["creditsnum"] = 0;
        dataArr["buynum"] = data["buynum"];
        dataArr["renameto"] = "undefined`undefined";
        dataArr["expected_itemprice"] = data["price"];
        dataArr["expected_itemtype2"] = "";
        dataArr["expected_itemtype"] = data[0][1];
        dataArr["itemnum2"] = "0";
        dataArr["itemnum"] = data[0][0];
        dataArr["price"] = data[1];
        dataArr["action"] = data["action"];
        dataArr["gv"] = 21;
        dataArr["userID"] = userVars["userID"];
        dataArr["password"] = userVars["password"];

        df_prompt.innerHTML =
          "<div style='text-align: center'>Loading, please wait...</div>";
        webCall(
          "inventory_new",
          dataArr,
          function (webData) {
            updateIntoArr(flshToArr(webData, "DFSTATS_"), userVars);
            search();
          },
          true
        );
      };
      question = true;
      break;
    case "giveToChar":
      let doDefaultBehavior = true;
      promptLoading("Loading, please wait...");
      if (
        globalData[itemType[0]]["canread"] === true &&
        window.location.pathname.indexOf("/DF3D/") === -1
      ) {
        window.location.href = "index.php?page=39&book=" + itemType[0];
        return;
      }
      var dataArr = {};
      dataArr["pagetime"] = userVars["pagetime"];
      dataArr["templateID"] = userVars["template_ID"];
      dataArr["sc"] = userVars["sc"];
      dataArr["creditsnum"] = 0;
      dataArr["buynum"] = 0;
      dataArr["renameto"] = "undefined`undefined";
      dataArr["expected_itemprice"] = "-1";
      dataArr["expected_itemtype2"] = "";
      dataArr["expected_itemtype"] = currentItem.dataset.type;
      dataArr["itemnum2"] = "0";
      dataArr["itemnum"] = currentItem.parentNode.dataset.slot;
      dataArr["price"] = 0;
      dataArr["gv"] = 21;
      dataArr["userID"] = userVars["userID"];
      dataArr["password"] = userVars["password"];
      if (
        currentItem.parentNode.parentNode.parentNode.id === "backpackdisplay"
      ) {
        dataArr["itemnum"] = parseInt(dataArr["itemnum"]) + 1050;
      } else if (
        currentItem.parentNode.parentNode.parentNode.id === "implants"
      ) {
        dataArr["itemnum"] = parseInt(dataArr["itemnum"]) + 1000;
      }
      var doPageRefresh = false;
      if (parseInt(globalData[itemType[0]]["healthrestore"]) > 0) {
        if (globalData[itemType[0]]["consume_sound"]) {
          playSound(globalData[itemType[0]]["consume_sound"]);
        } else {
          playSound("heal");
        }
        dataArr["action"] = "newuse";
      } else if (
        typeof globalData[itemType[0]]["armourrestore"] !== "undefined" &&
        parseInt(globalData[itemType[0]]["armourrestore"]) > 0
      ) {
        if (globalData[itemType[0]]["consume_sound"]) {
          playSound(globalData[itemType[0]]["consume_sound"]);
        } else {
          playSound("repair");
        }
        dataArr["action"] = "userepairkit";
      } else if (parseInt(globalData[itemType[0]]["foodrestore"]) > 0) {
        if (globalData[itemType[0]]["consume_sound"]) {
          playSound(globalData[itemType[0]]["consume_sound"]);
        } else {
          playSound("eat");
        }
        dataArr["action"] = "newconsume";
      } else if (
        globalData[itemType[0]]["boostdamagehours"] > 0 ||
        globalData[itemType[0]]["boostexphours"] > 0 ||
        globalData[itemType[0]]["boostspeedhours"] > 0 ||
        globalData[itemType[0]]["boostdamagehours_ex"] > 0 ||
        globalData[itemType[0]]["boostexphours_ex"] > 0 ||
        globalData[itemType[0]]["boostspeedhours_ex"] > 0
      ) {
        if (globalData[itemType[0]]["consume_sound"]) {
          playSound(globalData[itemType[0]]["consume_sound"]);
        } else {
          playSound("heal");
        }
        dataArr["action"] = "newboost";
      } else if (
        typeof globalData[itemType[0]]["opencontents"] !== "undefined" &&
        globalData[itemType[0]]["opencontents"].length > 0
      ) {
        if (globalData[itemType[0]]["consume_sound"]) {
          playSound(globalData[itemType[0]]["consume_sound"]);
        } else {
          playSound("read");
        }
        dataArr["action"] = "newopen";
      } else if (globalData[itemType[0]]["moneygift"] === true) {
        if (globalData[itemType[0]]["consume_sound"]) {
          playSound(globalData[itemType[0]]["consume_sound"]);
        } else {
          playSound("read");
        }
        dataArr["action"] = "moneygift";
      } else if (
        typeof globalData[itemType[0]]["rewardcontents"] !== "undefined" &&
        globalData[itemType[0]]["rewardcontents"].length > 0
      ) {
        dataArr["action"] = "claimreward";
        let selectionBox = document.createElement("select");
        let itemOptions = globalData[itemType[0]]["rewardcontents"].split(",");
        for (let optionType of itemOptions) {
          let optionElem = document.createElement("option");
          optionElem.textContent = globalData[optionType]["name"];
          optionElem.value = optionType;
          selectionBox.appendChild(optionElem);
        }
        df_prompt.innerHTML = "Please select the item you want to claim<br />";
        let centerDiv = document.createElement("div");
        centerDiv.style.textAlign = "center";
        centerDiv.appendChild(selectionBox);
        df_prompt.appendChild(centerDiv);
        doDefaultBehavior = false;
        question = true;
        action = function (data) {
          dataArr["expected_itemtype2"] = selectionBox.value;
          if (globalData[itemType[0]]["consume_sound"]) {
            playSound(globalData[itemType[0]]["consume_sound"]);
          } else {
            playSound("equip");
          }
          webCall(
            "inventory_new",
            dataArr,
            function (webData) {
              if (doPageRefresh) {
                location.reload(true);
                return;
              }
              updateIntoArr(flshToArr(webData, "DFSTATS_"), userVars);
              populateInventory();
              populateCharacterInventory();
              updateAllFields();
            },
            true
          );
        };
      } else if (
        typeof globalData[itemType[0]]["gm_days"] !== "undefined" &&
        globalData[itemType[0]]["gm_days"] !== "0"
      ) {
        if (globalData[itemType[0]]["consume_sound"]) {
          playSound(globalData[itemType[0]]["consume_sound"]);
        } else {
          playSound("eat");
        }
        dataArr["action"] = "newuse";
        doPageRefresh = true;
      } else if (
        typeof globalData[itemType[0]]["implant_stats"] !== "undefined" &&
        globalData[itemType[0]]["implant_stats"].length > 0
      ) {
        playSound("repair");
        doDefaultBehavior = false;
        let statAllocationDisplay = document.createElement("div");
        statAllocationDisplay.classList.add("genericActionBox");
        statAllocationDisplay.classList.add("opElem");
        statAllocationDisplay.style.inset = "100px";

        let statTitle = document.createElement("div");
        statTitle.style.fontFamily = "Downcome";
        statTitle.style.color = "#990000";
        statTitle.style.fontSize = "14pt";
        statTitle.textContent = "Mutate Stats";
        statAllocationDisplay.appendChild(statTitle);

        let statHolder = document.createElement("div");
        statHolder.style.display = "grid";
        statHolder.style.gridTemplateColumns = "repeat(2, 50%)";

        let implantStats = globalData[itemType[0]]["implant_stats"].split(",");
        let implantMultipliers =
          globalData[itemType[0]]["implant_stats_multi"].split(",");
        for (let mult in implantMultipliers) {
          implantMultipliers[mult] = parseFloat(implantMultipliers[mult]);
        }

        let pointsToAllocate =
          globalData[itemType[0]]["implant_stats_free_points"];
        let totalFreePointsRemaining = parseInt(
          globalData[itemType[0]]["implant_stats_free_points"]
        );

        let maxCombinations = implantStats.length;
        let bonusStats = new Array(maxCombinations);
        let maxNeededBits = BigInt(
          globalData[itemType[0]]["implant_stats_max_stat"].toString(2).length
        );
        let invItem = new InventoryItem(currentItem.dataset.type);
        if (typeof invItem.stats !== "undefined") {
          let tempStats = bAnyToBInt(invItem.stats, 36);
          for (let i = maxCombinations - 1; i >= 0; i--) {
            bonusStats[i] = Number(tempStats & (2n ** maxNeededBits - 1n));
            if (
              bonusStats[i] >
              parseInt(globalData[itemType[0]]["implant_stats_max_stat"])
            ) {
              bonusStats[i] = parseInt(
                globalData[itemType[0]]["implant_stats_max_stat"]
              );
            }
            totalFreePointsRemaining -= bonusStats[i];
            tempStats = tempStats >> maxNeededBits;
          }
        } else {
          for (let i = 0; i < maxCombinations; i++) {
            bonusStats[i] = 0;
          }
        }

        for (let i in implantStats) {
          let statDisplay = document.createElement("div");

          let statRange = document.createElement("input");
          let statOutput = document.createElement("div");
          statOutput.dataset.stat = implantStats[i];
          statOutput.classList.add("statOut");
          statRange.type = "range";
          statRange.min = 0;
          statRange.value = bonusStats[i];
          statRange.max = bonusStats[i] + totalFreePointsRemaining;
          if (bonusStats[i] <= 0 && totalFreePointsRemaining <= 0) {
            statRange.disabled = true;
          }

          statRange.addEventListener("input", function (evt) {
            pointsToAllocate = parseInt(
              globalData[itemType[0]]["implant_stats_free_points"]
            );
            let ranges = statHolder.querySelectorAll("input");
            let outputs = statHolder.querySelectorAll("div.statOut");
            for (let i = 0; i < maxCombinations; i++) {
              pointsToAllocate -= parseInt(ranges[i].value);
            }
            for (let i = 0; i < maxCombinations; i++) {
              let maxPointsICanAllocate =
                parseInt(ranges[i].value) + pointsToAllocate;
              if (
                maxPointsICanAllocate >
                parseInt(globalData[itemType[0]]["implant_stats_max_stat"])
              ) {
                maxPointsICanAllocate = parseInt(
                  globalData[itemType[0]]["implant_stats_max_stat"]
                );
              }
              ranges[i].max = maxPointsICanAllocate;
              if (maxPointsICanAllocate <= 0) {
                ranges[i].disabled = true;
              } else {
                ranges[i].disabled = false;
              }
              outputs[i].textContent =
                ranges[i].value * implantMultipliers[i] +
                implantBonusText[outputs[i].dataset.stat];
            }
          });

          statDisplay.appendChild(statRange);

          statOutput.textContent =
            bonusStats[i] * implantMultipliers[i] +
            implantBonusText[implantStats[i]];
          statDisplay.appendChild(statOutput);
          statHolder.appendChild(statDisplay);
        }
        statAllocationDisplay.appendChild(statHolder);

        let confirmBtn = document.createElement("button");
        confirmBtn.addEventListener("click", function (evt) {
          let ranges = statHolder.querySelectorAll("input");
          let bonusStats = [];
          let totalStats = 0;
          for (let i = 0; i < ranges.length; i++) {
            ranges[i].disabled = true;
            bonusStats[i] = parseInt(ranges[i].value);
            totalStats += parseInt(ranges[i].value);
          }
          if (
            totalStats <=
            parseInt(globalData[itemType[0]]["implant_stats_free_points"])
          ) {
            // tutorial on binary to base36 conversions, https://www.youtube.com/watch?v=Iu81WUG9OeU
            let curBit = BigInt(0);
            for (let i = 0; i < maxCombinations; i++) {
              curBit = curBit << BigInt(maxNeededBits);
              curBit += BigInt(ranges[i].value);
            }

            invItem.stats = curBit.toString(36);
            dataArr["expected_itemtype2"] = "" + invItem;
            dataArr["action"] = "mutate";

            promptLoading("Updating...");
            webCall(
              "inventory_new",
              dataArr,
              function (webData) {
                updateIntoArr(flshToArr(webData, "DFSTATS_"), userVars);
                if (dataArr["itemnum"] > 1050) {
                  populateBackpack();
                } else if (dataArr["itemnum"] > 1000) {
                  populateImplants();
                } else {
                  populateInventory();
                }
                updateAllFields();
                df_prompt.parentNode.removeChild(statAllocationDisplay);
                df_prompt.style.display = "";
              },
              true
            );
          } else {
            for (let i = 0; i < ranges.length; i++) {
              if (parseInt(ranges[i].value) > 0) {
                ranges[i].disabled = false;
              }
            }
          }
        });
        confirmBtn.textContent = "Confirm";
        statAllocationDisplay.appendChild(confirmBtn);

        df_prompt.style.display = "none";

        df_prompt.parentNode.appendChild(statAllocationDisplay);
        return;
      } else if (
        typeof globalData[itemType[0]]["implant_mods"] !== "undefined" &&
        globalData[itemType[0]]["implant_mods"].length > 0
      ) {
        dataArr["action"] = "rerollgambler";
        let rerollCost = globalData[itemType[0]]["implant_reroll_cost"] ?? 1e6;
        df_prompt.innerHTML =
          "Rerolling this implant's stats will cost <span style='color: #FFCC00;'>$" +
          nf.format(rerollCost) +
          "</span>. Do you still want to reroll?";
        doDefaultBehavior = false;
        question = true;
        action = function (data) {
          let finalAction = function () {
            webCall(
              "hotrods/inventory_actions",
              dataArr,
              function (webData) {
                let webArr = flshToArr(webData, "DFSTATS_");
                let targetStatSlot = webArr["DFSTATS_target"];
                delete webArr["DFSTATS_target"];

                updateIntoArr(webArr, userVars);
                populateInventory();
                populateCharacterInventory();
                populateImplants();
                updateAllFieldsBase();

                let implantItem = new InventoryItem(
                  webArr["DFSTATS_" + targetStatSlot]
                );

                df_prompt.innerHTML = "Your rolled stats are,";
                df_prompt.appendChild(document.createElement("br"));

                let bonusStats = {};
                let allowedStatsToMod =
                  globalData[itemType[0]]["implant_mods"].split(",");
                let maxCombinations = allowedStatsToMod.length;
                let maxStatsReplacement =
                  globalData[itemType[0]]["implant_mods_negative"] +
                  globalData[itemType[0]]["implant_mods_positive"];
                let maxNeededBits = BigInt(
                  maxStatsReplacement.toString(2).length
                );
                let totalStatsRolledPerfect = 0;
                if (typeof implantItem.stats !== "undefined") {
                  let tempStats = bAnyToBInt(implantItem.stats, 36);
                  for (let i = maxCombinations - 1; i >= 0; i--) {
                    if (
                      typeof bonusStats[allowedStatsToMod[i]] === "undefined"
                    ) {
                      bonusStats[allowedStatsToMod[i]] = 0;
                    }
                    let impBonus = Number(
                      tempStats & (2n ** maxNeededBits - 1n)
                    );
                    if (impBonus > maxStatsReplacement) {
                      impBonus = maxStatsReplacement;
                    }
                    impBonus -=
                      globalData[itemType[0]]["implant_mods_negative"];
                    if (impBonus > 0) {
                      bonusStats[allowedStatsToMod[i]] += impBonus;
                      if (
                        impBonus >=
                        globalData[itemType[0]]["implant_mods_positive"]
                      ) {
                        totalStatsRolledPerfect++;
                      }
                    } else {
                      bonusStats[allowedStatsToMod[i]] += impBonus;
                    }
                    tempStats = tempStats >> maxNeededBits;
                  }
                }
                df_prompt.innerHTML += ic_ImplantData(
                  allowedStatsToMod,
                  bonusStats
                );
                if (
                  totalStatsRolledPerfect >=
                  globalData[itemType[0]]["implant_mods_total"]
                ) {
                  df_prompt.innerHTML +=
                    "<span style='color: #e6cc4d;'>Golden Roll</span>";
                }

                setTimeout(function () {
                  df_prompt.parentNode.style.display = "none";
                  df_prompt.innerHTML = "";
                }, 2000);
              },
              true
            );
          };
          if (checkLSBool("gamblingden", "instant")) {
            finalAction();
          } else {
            df_prompt.innerHTML = "";
            df_prompt.appendChild(createSlotDisplay());
            setTimeout(finalAction, 2000);
          }
        };
      }
      if (doDefaultBehavior) {
        webCall(
          "inventory_new",
          dataArr,
          function (webData) {
            if (doPageRefresh) {
              location.reload(true);
              return;
            }
            updateIntoArr(flshToArr(webData, "DFSTATS_"), userVars);
            populateInventory();
            populateCharacterInventory();
            updateAllFields();
          },
          true
        );
        return;
      }
      break;
    case "ironmanaction":
      var actualAction = false;
      if (globalData[itemType[0]]["itemtype"] === "armour") {
        actualAction = "newrepair";
      } else {
        if (parseInt(globalData[itemType[0]]["foodrestore"]) > 0) {
          actualAction = "newcook";
        } else if (parseInt(globalData[itemType[0]]["healthrestore"]) > 0) {
          actualAction = "newadminister";
        }
      }
    case "newcook":
    case "newadminister":
    case "newrepair":
      df_prompt.innerHTML =
        "<div style='text-align: center'>Loading, please wait...</div>";
      df_prompt.parentNode.style.display = "block";
      if (typeof actualAction === "undefined") {
        actualAction = nSlot.dataset.action;
      } else if (actualAction === false) {
        actualAction = nSlot.dataset.action;
      }
      switch (actualAction) {
        case "newcook":
          playSound("cook");
          break;
        case "newadminister":
          playSound("heal");
          break;
        case "newrepair":
          playSound("repair");
          break;
      }

      var dataArr = {};
      dataArr["pagetime"] = userVars["pagetime"];
      dataArr["templateID"] = userVars["template_ID"];
      dataArr["sc"] = userVars["sc"];
      dataArr["creditsnum"] = 0;
      dataArr["buynum"] = 0;
      dataArr["renameto"] = "undefined`undefined";
      dataArr["expected_itemprice"] = "-1";
      dataArr["expected_itemtype2"] = "";
      dataArr["expected_itemtype"] = currentItem.dataset.type;
      dataArr["itemnum2"] = "0";
      dataArr["itemnum"] = currentItem.parentNode.dataset.slot;
      dataArr["action"] = actualAction;
      dataArr["gv"] = 21;
      dataArr["price"] = scrapAmount(itemType[0]);
      dataArr["userID"] = userVars["userID"];
      dataArr["password"] = userVars["password"];

      if (
        currentItem.parentNode.parentNode.parentNode.id === "backpackdisplay"
      ) {
        dataArr["itemnum"] = parseInt(dataArr["itemnum"]) + 1050;
      }

      webCall(
        "inventory_new",
        dataArr,
        function (webData) {
          updateIntoArr(flshToArr(webData, "DFSTATS_"), userVars);
          populateInventory();
          populateCharacterInventory();
          updateAllFields();
        },
        true
      );
      return;
      break;
    case "toimpstore":
      itemData = [
        parseInt(currentItem.parentNode.dataset.slot),
        currentItem.dataset.type,
        currentItem.parentNode.parentNode.parentNode.id,
      ];
      extraData = [itemData];
      if (itemData[2] === "implants") {
        itemData[0] += 1000;
      } else if (itemData[2] === "backpackdisplay") {
        itemData[0] += 1050;
      }
      let impItem = new InventoryItem(currentItem.dataset.type);
      df_prompt.innerHTML = "Are you sure you want to add ";
      if (typeof impItem.name !== "undefined") {
        df_prompt.innerHTML +=
          "<span style='color: red;'>" + impItem.name + "</span>";
      } else {
        df_prompt.innerHTML +=
          "<span style='color: red'>" +
          globalData[impItem.type]["name"] +
          "</span>";
      }
      df_prompt.innerHTML += " to your implant storage?";
      question = true;
      action = function (data) {
        promptLoading();
        var dataArr = {};
        dataArr["pagetime"] = userVars["pagetime"];
        dataArr["templateID"] = userVars["template_id"];
        dataArr["sc"] = userVars["sc"];
        dataArr["userID"] = userVars["userID"];
        dataArr["password"] = userVars["password"];
        dataArr["gv"] = 21;
        dataArr["action"] = "toimpstore";
        dataArr["slotnum"] = data[0][0];

        webCall(
          "hotrods/inventory_actions",
          dataArr,
          function (webData) {
            let flshArr = flshToArr(webData);
            implantStorage = JSON.parse(flshArr["impdata"]);
            delete flshArr["impdata"];
            updateIntoArr(flshArr, userVars);

            populateInventory();
            populateBackpack();
            populateImplants();
            updateAllFields();
          },
          true
        );
      };
      break;
  }
  var noButton = document.createElement("button");

  noButton.style.position = "absolute";
  noButton.style.bottom = "8px";
  noButton.addEventListener("click", function () {
    cleanPlacementMessage();
    df_prompt.parentNode.style.display = "none";
    df_prompt.innerHTML = "";
    df_prompt.classList.remove("warning");
    df_prompt.classList.remove("redhighlight");
    pageLock = false;
  });
  if (question) {
    noButton.textContent = "No";
    noButton.style.right = "86px";
    var yesButton = document.createElement("button");
    yesButton.textContent = "Yes";
    yesButton.style.position = "absolute";
    yesButton.style.left = "86px";
    yesButton.style.bottom = "8px";
    yesButton.addEventListener("click", function () {
      yesButton.disabled = true;
      cleanPlacementMessage();
      df_prompt.classList.remove("warning");
      df_prompt.classList.remove("redhighlight");
      action(extraData);
    });
    var dataInput = df_prompt.querySelectorAll("input");
    if (dataInput.length) {
      if (marketLastMoney === false) {
        yesButton.disabled = true;
      }

      for (var h in dataInput) {
        dataInput[h].oninput = function (dE) {
          var keepDisabled = false;
          for (var g in dataInput) {
            if (dataInput[g].value === "") {
              keepDisabled = true;
            }
          }
          if (keepDisabled) {
            yesButton.disabled = true;
          } else {
            yesButton.disabled = false;
          }
          if (dE.target.type === "number") {
            if (dE.target.value < 0) {
              dE.target.value = 0;
            } else if (parseInt(dE.target.value) > parseInt(dE.target.max)) {
              dE.target.value = dE.target.max;
            }
            if (dE.target.classList.contains("moneyField")) {
              if (itemData[1] === "credits") {
                if (
                  dE.target.value < scrapValue(itemData[1], dataInput[0].value)
                ) {
                  var msgX =
                    df_prompt.getBoundingClientRect().left -
                    20 +
                    df_prompt.offsetWidth / 2 -
                    165 / 2;
                  var msgY = df_prompt.getBoundingClientRect().top + 105;
                  displayPlacementMessage(
                    "This is less than scrap value for this item ($" +
                      scrapValue(itemData[1], dataInput[0].value) +
                      ")",
                    msgX,
                    msgY,
                    "ERROR"
                  );
                } else {
                  cleanPlacementMessage();
                }
              } else {
                if (dE.target.value < scrapValue(itemData[1], itemData[3])) {
                  var msgX =
                    df_prompt.getBoundingClientRect().left -
                    20 +
                    df_prompt.offsetWidth / 2 -
                    165 / 2;
                  var msgY = df_prompt.getBoundingClientRect().top + 105;
                  displayPlacementMessage(
                    "This is less than scrap value for this item ($" +
                      scrapValue(itemData[1], itemData[3]) +
                      ")",
                    msgX,
                    msgY,
                    "ERROR"
                  );
                } else {
                  cleanPlacementMessage();
                }
              }
            }
          }
          if (dE.target.type === "text") {
            if (dE.target.value.length >= 24) {
              dE.preventDefault();
              dE.target.value = dE.target.value.substr(0, 24);
            }
            dE.target.value = dE.target.value.replace(
              /[^A-Z a-z 0-9\'\`\-   ]/g,
              ""
            );
          }
        };
        dataInput[h].onkeydown = function (dE) {
          /*if(dE.target.type === "number")
					 {
					 if(dE.key.length === 1 && isNaN(dE.key) && !dE.ctrlKey || dE.key === "v")
					 {
					 dE.preventDefault();
					 }
					 }*/
          if (dE.target.type === "text") {
            /*if(!dE.key.match(/[A-Z a-z 0-9\'\`\-   ]/g))
						 {
						 dE.preventDefault();
						 }*/
            if (dE.key === "'" || dE.key === '"') {
              dE.preventDefault();
              if (dE.target.value.length < 24) {
                dE.target.value += "`";
                if (dE.target.value === "") {
                  yesButton.disabled = true;
                } else {
                  yesButton.disabled = false;
                }
              }
            }
          }
        };
      }
    }
    df_prompt.appendChild(yesButton);
    df_prompt.onkeydown = function (e) {
      if (e.keyCode === 13) {
        df_prompt.onkeydown = null;
        yesButton.click();
      }
    };
  } else {
    noButton.textContent = "ok";
    noButton.style.left = "125px";
    df_prompt.onkeydown = function (e) {
      if (e.keyCode === 13) {
        df_prompt.onkeydown = null;
        noButton.click();
      }
    };
  }
  df_prompt.appendChild(noButton);
  df_prompt.parentNode.style.display = "block";
  if (dataInput && dataInput.length) {
    dataInput[0].focus();
  } else {
    df_prompt.focus();
  }
}

function doSellFromScriptSLP(itemElem, inPrivate) {
  var extraData = {};
  if (inPrivate) {
    extraData["sendto"] = userVars["member_to"];
  }
  itemData = [
    parseInt(itemElem.parentNode.dataset.slot),
    itemElem.dataset.type,
    itemElem.parentNode.parentNode.parentNode.id,
    itemElem.dataset.quantity,
  ];
  extraData["itemData"] = itemData;
  question = true;
  var priceHolder = document.createElement("div");
  priceHolder.style.position = "absolute";
  priceHolder.style.width = "100%";
  priceHolder.style.textAlign = "center";
  priceHolder.style.bottom = "30px";

  if (itemData[1] === "credits") {
    df_prompt.innerHTML =
      "How many <span style='color: red;'>Credits</span> would you like to sell and for how much?";
    var creditInput = document.createElement("input");
    creditInput.dataset.type = "credit";
    creditInput.style.color = "#cccccc";
    creditInput.style.backgroundColor = "#555555";
    creditInput.type = "number";
    creditInput.min = 0;
    creditInput.value = 100;
    if (parseInt(userVars["DFSTATS_df_credits"]) > 4000) {
      creditInput.max = 4000;
    } else {
      creditInput.max = userVars["DFSTATS_df_credits"];
    }
    var creditLabel = document.createElement("label");
    creditLabel.textContent = "C";
    creditLabel.style.color = "#cccccc";
    priceHolder.appendChild(creditLabel);
    priceHolder.appendChild(creditInput);
    priceHolder.appendChild(document.createElement("br"));
  } else {
    df_prompt.innerHTML = "How much would you like to sell the ";
    if (itemData[1].indexOf("_name") >= 0) {
      var endOName = itemData[1].indexOf("_", itemData[1].indexOf("_name") + 5);
      if (endOName > 0) {
        df_prompt.innerHTML +=
          "<span style='color: red;'>" +
          itemData[1].substring(itemData[1].indexOf("_name") + 5, endOName) +
          "</span>";
      } else {
        df_prompt.innerHTML +=
          "<span style='color: red;'>" +
          itemData[1].substring(itemData[1].indexOf("_name") + 5) +
          "</span>";
      }
    } else {
      df_prompt.innerHTML +=
        "<span style='color: red;'>" +
        globalData[itemType[0]]["name"] +
        "</span>";
    }
    df_prompt.innerHTML += " for?";
  }

  var priceLabel = document.createElement("label");
  priceLabel.textContent = "$";
  priceLabel.style.color = "#ffff00";
  var priceInput = document.createElement("input");
  priceInput.dataset.type = "price";
  priceInput.classList.add("moneyField");
  priceInput.type = "number";
  priceInput.max = 9999999999;
  priceInput.min = 0;

  if (marketLastMoney !== false && lastItemSold === itemType[0]) {
    priceInput.value = marketLastMoney;
  } else {
    priceInput.value = "";
    marketLastMoney = false;
    lastItemSold = itemType[0];
  }

  priceHolder.appendChild(priceLabel);
  priceHolder.appendChild(priceInput);

  df_prompt.appendChild(priceHolder);
  action = sellpriceConfirm;
}

function doSellFromScript(itemElem, marketElem) {
  itemType = itemElem.dataset.type.split("_");
  var extraData = {};
  if (tradeTimer !== undefined) {
    stopQueryUpdate();
  }
  itemData = [
    parseInt(itemElem.parentNode.dataset.slot),
    itemElem.dataset.type,
    itemElem.parentNode.parentNode.parentNode.id,
    itemElem.dataset.quantity,
  ];
  extraData["itemData"] = itemData;
  // nSlot
  extraData["itemnum"] = itemElem.parentNode.dataset.slot;
  extraData["target"] = marketElem.dataset.target;
  extraData["credits"] = marketElem.dataset.credits;
  extraData["update"] = marketElem.dataset.update;
  extraData["trade"] = marketElem.dataset.trade;
  extraData["type"] = itemElem.dataset.type;
  extraData["cash"] = itemElem.dataset.quantity;
  extraData["action"] = "additem";

  df_prompt.innerHTML = "Are you sure you want to trade ";
  if (itemData[1].indexOf("_name") >= 0) {
    var endOName = itemData[1].indexOf("_", itemData[1].indexOf("_name") + 5);
    if (endOName > 0) {
      df_prompt.innerHTML +=
        "<span style='color: red;'>" +
        itemData[1].substring(itemData[1].indexOf("_name") + 5, endOName) +
        "</span>";
    } else {
      df_prompt.innerHTML +=
        "<span style='color: red;'>" +
        itemData[1].substring(itemData[1].indexOf("_name") + 5) +
        "</span>";
    }
  } else {
    df_prompt.innerHTML +=
      "<span style='color: red;'>" +
      globalData[itemType[0]]["name"] +
      "</span>";
  }
  df_prompt.innerHTML += "?";

  action = doTradeAction;

  var noButton = document.createElement("button");

  noButton.style.position = "absolute";
  noButton.style.top = "72px";
  noButton.addEventListener("click", function () {
    cleanPlacementMessage();
    df_prompt.parentNode.style.display = "none";
    df_prompt.innerHTML = "";
    df_prompt.classList.remove("warning");
    df_prompt.classList.remove("redhighlight");
    pageLock = false;
  });

  noButton.textContent = "No";
  noButton.style.right = "86px";
  var yesButton = document.createElement("button");
  yesButton.textContent = "Yes";
  yesButton.style.position = "absolute";
  yesButton.style.left = "86px";
  yesButton.style.top = "72px";
  yesButton.addEventListener("click", function () {
    yesButton.disabled = true;
    cleanPlacementMessage();
    df_prompt.classList.remove("warning");
    df_prompt.classList.remove("redhighlight");
    action(extraData);
  });
  var dataInput = df_prompt.querySelectorAll("input");
  if (dataInput.length) {
    yesButton.disabled = true;
    for (var h in dataInput) {
      dataInput[h].oninput = function (dE) {
        var keepDisabled = false;
        for (var g in dataInput) {
          if (dataInput[g].value === "") {
            keepDisabled = true;
          }
        }
        if (keepDisabled) {
          yesButton.disabled = true;
        } else {
          yesButton.disabled = false;
        }
        if (dE.target.type === "number") {
          if (dE.target.value < 0) {
            dE.target.value = 0;
          } else if (parseInt(dE.target.value) > parseInt(dE.target.max)) {
            dE.target.value = dE.target.max;
          }
          if (dE.target.classList.contains("moneyField")) {
            if (itemData[1] === "credits") {
              if (
                dE.target.value < scrapValue(itemData[1], dataInput[0].value)
              ) {
                var msgX =
                  df_prompt.getBoundingClientRect().left -
                  20 +
                  df_prompt.offsetWidth / 2 -
                  165 / 2;
                var msgY = df_prompt.getBoundingClientRect().top + 105;
                displayPlacementMessage(
                  "This is less than scrap value for this item ($" +
                    scrapValue(itemData[1], dataInput[0].value) +
                    ")",
                  msgX,
                  msgY,
                  "ERROR"
                );
              } else {
                cleanPlacementMessage();
              }
            } else {
              if (dE.target.value < scrapValue(itemData[1], itemData[3])) {
                var msgX =
                  df_prompt.getBoundingClientRect().left -
                  20 +
                  df_prompt.offsetWidth / 2 -
                  165 / 2;
                var msgY = df_prompt.getBoundingClientRect().top + 105;
                displayPlacementMessage(
                  "This is less than scrap value for this item ($" +
                    scrapValue(itemData[1], itemData[3]) +
                    ")",
                  msgX,
                  msgY,
                  "ERROR"
                );
              } else {
                cleanPlacementMessage();
              }
            }
          }
        }
        if (dE.target.type === "text") {
          if (dE.target.value.length >= 24) {
            dE.preventDefault();
            dE.target.value = dE.target.value.substr(0, 24);
          }
          dE.target.value = dE.target.value.replace(
            /[^A-Z a-z 0-9\'\`\-   ]/g,
            ""
          );
        }
      };
      dataInput[h].onkeydown = function (dE) {
        /*if(dE.target.type === "number")
				 {
				 if(dE.key.length === 1 && isNaN(dE.key) && !dE.ctrlKey || dE.key === "v")
				 {
				 dE.preventDefault();
				 }
				 }*/
        if (dE.target.type === "text") {
          /*if(!dE.key.match(/[A-Z a-z 0-9\'\`\-   ]/g))
					 {
					 dE.preventDefault();
					 }*/
          if (dE.key === "'" || dE.key === '"') {
            dE.preventDefault();
            if (dE.target.value.length < 24) {
              dE.target.value += "`";
              if (dE.target.value === "") {
                yesButton.disabled = true;
              } else {
                yesButton.disabled = false;
              }
            }
          }
        }
      };
    }
  }
  df_prompt.appendChild(yesButton);
  df_prompt.onkeydown = function (e) {
    if (e.keyCode === 13) {
      df_prompt.onkeydown = null;
      yesButton.click();
    }
  };
  df_prompt.appendChild(noButton);
  df_prompt.parentNode.style.display = "block";
  if (dataInput && dataInput.length) {
    dataInput[0].focus();
  } else {
    df_prompt.focus();
  }
}

function itemNamer(itemStr, quantity) {
  var slotData = itemStr.trim().split("_");
  var prename = "";
  var itemName = "";
  for (var x in slotData) {
    if (slotData[x].indexOf("colour") >= 0) {
      if (slotData[x].indexOf("^") !== -1) {
        prename += "Custom Colour ";
      } else {
        prename += slotData[x].substring(6) + " ";
      }
    } else if (slotData[x].indexOf("name") >= 0) {
      itemName += slotData[x].substring(4) + " ";
    } else if (slotData[x].indexOf("cooked") >= 0) {
      prename += "Cooked ";
    }
  }
  if (itemName === "") {
    itemName += globalData[slotData[0]]["name"];
  }
  if (itemName === "Credits") {
    itemName = quantity + " " + itemName;
  }
  return (prename + itemName).trim();
}

function scrapItem(data) {
  var page = window.location.search.substring(1).split("&")[0];
  if (page !== "page=24" && page !== "page=59") {
    df_prompt.innerHTML =
      "<div style='text-align: center'>You cannot scrap outside of The Yard...</div>";
    setTimeout(function () {
      populateInventory();
      if (document.getElementById("implants") !== null) {
        populateImplants();
      }
      populateCharacterInventory();
      updateAllFields();
      renderAvatarUpdate();
    }, 5000);
    return;
  }
  playSound("shop_buysell");
  var dataArr = {};
  dataArr["pagetime"] = userVars["pagetime"];
  dataArr["templateID"] = userVars["template_ID"];
  dataArr["sc"] = userVars["sc"];
  dataArr["creditsnum"] = 0;
  dataArr["buynum"] = 0;
  dataArr["renameto"] = "";
  dataArr["expected_itemprice"] = "-1";
  dataArr["expected_itemtype2"] = "";
  dataArr["expected_itemtype"] = data[0][1];
  dataArr["itemnum2"] = "0";
  dataArr["itemnum"] = data[0][0];
  dataArr["price"] = data[1];
  dataArr["action"] = data["action"];
  dataArr["gv"] = 21;
  dataArr["userID"] = userVars["userID"];
  dataArr["password"] = userVars["password"];
  df_prompt.classList.remove("warning");
  df_prompt.classList.remove("redhighlight");
  df_prompt.innerHTML =
    "<div style='text-align: center'>Loading, please wait...</div>";
  webCall(
    "inventory_new",
    dataArr,
    function (webData) {
      updateIntoArr(flshToArr(webData, "DFSTATS_"), userVars);
      if (typeof craftCycleInterval === "number") {
        dataArr = {};
        dataArr["pagetime"] = userVars["pagetime"];
        dataArr["templateID"] = userVars["template_ID"];
        dataArr["sc"] = userVars["sc"];
        dataArr["gv"] = 21;
        dataArr["userID"] = userVars["userID"];
        dataArr["password"] = userVars["password"];
        dataArr["craftItem"] = "";
        dataArr["action"] = "get";

        webCall(
          "DF3D/DF3D_Crafting",
          dataArr,
          function (webData) {
            updateIntoArr(flshToArr(webData, "DFSTATS_"), userVars);
            initiateCrafting();
            populateInventory();
            populateCharacterInventory();
            updateAllFields();
            renderAvatarUpdate();
          },
          true
        );
      } else {
        populateInventory();
        if (document.getElementById("implants") !== null) {
          populateImplants();
        }
        populateCharacterInventory();
        updateAllFields();
        renderAvatarUpdate();
      }
    },
    true
  );
}

function sellpriceConfirm(data) {
  var salePrice = 0;
  var quantity = false;
  var priceInput = df_prompt.querySelector("input[data-type='price']");
  if (priceInput.value !== "") {
    salePrice = parseInt(priceInput.value);
  }
  if (data["itemData"][1] === "credits") {
    var creditInput = df_prompt.querySelector("input[data-type='credit']");
    data[3] = creditInput.value;
    quantity = creditInput.value;
  }
  var itemType = data["itemData"][1].split("_");
  if (getItemType(globalData[itemType[0].trim()]) === "ammo") {
    quantity = data["itemData"][3];
  }

  df_prompt.innerHTML =
    "Are you sure you want to sell " + (quantity ? quantity + " " : " ");
  if (data["itemData"][1].indexOf("_name") >= 0) {
    var endOName = data["itemData"][1].indexOf(
      "_",
      data["itemData"][1].indexOf("_name") + 5
    );
    if (endOName > 0) {
      df_prompt.innerHTML +=
        "<span style='color: red;'>" +
        data["itemData"][1].substring(
          data["itemData"][1].indexOf("_name") + 5,
          endOName
        ) +
        "</span>";
    } else {
      df_prompt.innerHTML +=
        "<span style='color: red;'>" +
        data["itemData"][1].substring(
          data["itemData"][1].indexOf("_name") + 5
        ) +
        "</span>";
    }
  } else {
    var itemType = data["itemData"][1].split("_");
    df_prompt.innerHTML +=
      "<span style='color: red;'>" +
      globalData[itemType[0].trim()]["name"] +
      "</span>";
  }
  df_prompt.innerHTML +=
    " for <span style='color: #FFCC00;'>" +
    (salePrice ? "$" + nf.format(salePrice) : "free") +
    "</span>?";
  df_prompt.classList.add("warning");

  var noButton = document.createElement("button");

  noButton.style.position = "absolute";
  noButton.style.top = "72px";
  noButton.addEventListener("click", function () {
    df_prompt.parentNode.style.display = "none";
    df_prompt.innerHTML = "";
    df_prompt.classList.remove("warning");
    df_prompt.classList.remove("redhighlight");
    pageLock = false;
  });
  noButton.textContent = "No";
  noButton.style.right = "86px";
  var yesButton = document.createElement("button");
  yesButton.textContent = "Yes";
  yesButton.style.position = "absolute";
  yesButton.style.left = "86px";
  yesButton.style.top = "72px";
  yesButton.addEventListener("click", function () {
    data[2] = salePrice;
    df_prompt.classList.remove("warning");
    df_prompt.classList.remove("redhighlight");
    sellItem(data);
  });
  df_prompt.appendChild(yesButton);
  df_prompt.appendChild(noButton);
  df_prompt.onkeydown = function (e) {
    if (e.keyCode === 13) {
      df_prompt.onkeydown = null;
      yesButton.click();
    }
  };
  df_prompt.focus();
}

function sellItem(data) {
  marketLastMoney = data[2];

  var dataArr = {};
  dataArr["pagetime"] = userVars["pagetime"];
  dataArr["templateID"] = userVars["template_ID"];
  dataArr["sc"] = userVars["sc"];
  if (data["sendto"]) {
    dataArr["memberto"] = data["sendto"];
  }
  dataArr["buynum"] = 0;
  dataArr["renameto"] = "";
  dataArr["expected_itemprice"] = "-1"; // same on all sales
  dataArr["expected_itemtype2"] = "";
  dataArr["expected_itemtype"] = "";
  dataArr["itemnum2"] = "0";
  dataArr["itemnum"] = "0"; // slot number
  dataArr["price"] = data[2]; // actual item price they are selling for
  dataArr["userID"] = userVars["userID"];
  dataArr["password"] = userVars["password"];
  dataArr["gv"] = 21;

  if (data["itemData"][1] === "credits") {
    dataArr["creditsnum"] = data[3];
    dataArr["action"] = "newsellcredits";
  } else {
    dataArr["expected_itemtype"] = data["itemData"][1];
    dataArr["itemnum"] = data["itemData"][0];
    dataArr["action"] = "newsell";
  }

  df_prompt.innerHTML =
    "<div style='text-align: center'>Loading, please wait...</div>";
  webCall(
    "inventory_new",
    dataArr,
    function (webData) {
      updateIntoArr(flshToArr(webData, "DFSTATS_"), userVars);
      if (data["sendto"]) {
        getPrivateTrading(data["sendto"]);
      } else {
        getSellingList();
      }
    },
    true
  );
}

function inventoryAction(e) {
  var question = false;
  var action;
  var extraData = {};
  switch (e.target.dataset.action) {
    case "craft":
      var itemType = e.target.parentNode.dataset.type.split("_");
      extraData["type"] = e.target.parentNode.dataset.result;
      df_prompt.textContent = "Are you sure you want to craft the ";
      df_prompt.textContent += e.target.parentNode.dataset.name;
      df_prompt.textContent +=
        " for " + e.target.parentNode.dataset.price + "?";
      action = function (specialSauce) {
        playSound("repair");
        var dataArr = {
          craftItem: specialSauce["type"],
          action: "craft",
          userID: userVars["userID"],
          password: userVars["password"],
        };

        df_prompt.innerHTML =
          "<div style='text-align: center'>Loading, please wait...</div>";
        webCall(
          "DF3D/DF3D_Crafting",
          dataArr,
          function (webData) {
            updateIntoArr(flshToArr(webData, "DFSTATS_"), userVars);
            initiateCrafting();
            populateInventory();
            updateAllFields();
          },
          true
        );
      };
      question = true;
      break;
    case "storageUpgrade":
      var neededCash = getUpgradePrice();
      if (neededCash <= userVars["DFSTATS_df_cash"]) {
        df_prompt.innerHTML =
          "Are you sure you would like to buy 5 more slots of storage space for <span style='color: #FFCC00;'>$" +
          nf.format(neededCash) +
          "</span>?";
        action = function () {
          playSound("buysell");
          var dataArr = {};
          dataArr["pagetime"] = userVars["pagetime"];
          dataArr["templateID"] = userVars["template_ID"];
          dataArr["sc"] = userVars["sc"];
          dataArr["creditsnum"] = "";
          dataArr["buynum"] = "";
          dataArr["renameto"] = "undefined`undefined";
          dataArr["expected_itemprice"] = "-1";
          dataArr["expected_itemtype2"] = "";
          dataArr["expected_itemtype"] = "";
          dataArr["itemnum2"] = "0";
          dataArr["itemnum"] = "0";
          dataArr["price"] = getUpgradePrice();
          dataArr["action"] = "buystorage";
          dataArr["gv"] = 21;
          dataArr["userID"] = userVars["userID"];
          dataArr["password"] = userVars["password"];

          df_prompt.innerHTML =
            "<div style='text-align: center'>Loading, please wait...</div>";
          webCall(
            "inventory_new",
            dataArr,
            function (data) {
              reloadStorageData(data);
              if (
                (storageTab + 1) * 40 >=
                userVars["DFSTATS_df_storage_slots"]
              ) {
                document.getElementById("storageForward").style.display =
                  "none";
              } else {
                document.getElementById("storageForward").style.display =
                  "block";
              }
            },
            true
          );
        };
        question = true;
      } else {
        df_prompt.innerHTML =
          "You do not have enough cash to expand your storage locker. It will cost <span style='color: #FFCC00;'>$" +
          nf.format(neededCash) +
          "</span> to buy 5 more slots.";
      }
      break;
    case "takeFromStorage":
      shiftItem(e.target);
      break;
    case "cbtake":
      var itemType = e.target.parentNode.dataset.type;
      var itemData = itemType.split("_");
      let invItem = new InventoryItem(itemType);
      var feeToRemove =
        scrapValue(itemType, e.target.parentNode.dataset.quantity) * 2;
      if (globalData[invItem.type]["no_transfer"] === true || invItem.nt) {
        feeToRemove = 0;
      }

      if (feeToRemove > userVars["DFSTATS_df_cash"]) {
        df_prompt.innerHTML =
          "Sorry, you do not have enough money to withdraw this item. You need <span style='color: #FFCC00;'>$" +
          nf.format(feeToRemove) +
          "</span>.";
        break;
      }
      df_prompt.innerHTML = "Are you sure you want to take ";
      if (itemType.indexOf("_name") >= 0) {
        var endOName = itemType.indexOf("_", itemType.indexOf("_name") + 5);
        if (endOName > 0) {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemType.substring(itemType.indexOf("_name") + 5, endOName) +
            "</span>";
        } else {
          df_prompt.innerHTML +=
            "<span style='color: red;'>" +
            itemType.substring(itemType.indexOf("_name") + 5) +
            "</span>";
        }
      } else {
        df_prompt.innerHTML +=
          "<span style='color: red;'>" +
          globalData[itemData[0]]["name"] +
          "</span>";
      }
      df_prompt.innerHTML +=
        " for <span style='color: #FFCC00;'>$" +
        nf.format(feeToRemove) +
        "</span>?";
      extraData["type"] = itemType;
      question = true;
      action = function (specialSauce) {
        var dataArr = {};
        dataArr["pagetime"] = userVars["pagetime"];
        dataArr["templateID"] = userVars["template_id"];
        dataArr["sc"] = userVars["sc"];
        dataArr["userID"] = userVars["userID"];
        dataArr["password"] = userVars["password"];
        dataArr["gv"] = 21;
        dataArr["action"] = "remove";
        dataArr["itemcode"] = specialSauce["type"];
        dataArr["invslot"] = findFirstEmptyGenericSlot("inv");

        df_prompt.innerHTML =
          "<div style='text-align: center;'>Loading...</div>";
        df_prompt.parentNode.style.display = "block";
        webCall(
          "hotrods/collectionbook",
          dataArr,
          function (webData) {
            cbContent = flshToArr(webData);
            reloadInventoryData(reloadCollectionBook);
          },
          true
        );
      };
      break;
    default:
      console.log(e.target.dataset.action);
      return;
      break;
  }
  var noButton = document.createElement("button");

  noButton.style.position = "absolute";
  noButton.style.top = "72px";
  noButton.addEventListener("click", function () {
    df_prompt.parentNode.style.display = "none";
    df_prompt.innerHTML = "";
    pageLock = false;
  });
  if (question) {
    noButton.textContent = "No";
    noButton.style.right = "86px";
    var yesButton = document.createElement("button");
    yesButton.textContent = "Yes";
    yesButton.style.position = "absolute";
    yesButton.style.left = "86px";
    yesButton.style.top = "72px";
    yesButton.addEventListener("click", function () {
      yesButton.disabled = true;
      action(extraData);
    });
    df_prompt.appendChild(yesButton);
    df_prompt.onkeydown = function (e) {
      if (e.keyCode === 13) {
        df_prompt.onkeydown = null;
        yesButton.click();
      }
    };
  } else {
    noButton.textContent = "ok";
    noButton.style.left = "125px";
    df_prompt.onkeydown = function (e) {
      if (e.keyCode === 13) {
        df_prompt.onkeydown = null;
        noButton.click();
      }
    };
  }
  df_prompt.appendChild(noButton);
  df_prompt.parentNode.style.display = "block";
  df_prompt.focus();
}

let allowedHoverKeys = ["shift", "control", "alt", "s"];

/**
 *
 * @param {MouseEvent} e
 * @returns
 */
function hoverAction(e) {
  if (!active && !pageLock) {
    var realTarget = document.elementFromPoint(mousePos[0], mousePos[1]);
    while (typeof realTarget.dataset.amchild !== "undefined") {
      realTarget = realTarget.parentNode;
    }
    if (typeof realTarget.dataset.pmoverride !== "undefined") {
      return;
    }
    let canHoverAction = false;
    for (let key of allowedHoverKeys) {
      if (activeKeys.includes(key)) {
        canHoverAction = true;
        break;
      }
    }
    if (!canHoverAction) {
      cleanPlacementMessage();
      return;
    }
    if (activeKeys.includes("shift")) {
      if (realTarget.classList.contains("item")) {
        var itemType = realTarget.dataset.type.split("_")[0].trim();
        var canShift = false;
        if (document.getElementById("storage")) {
          if (realTarget.parentNode.parentNode.parentNode.id === "storage") {
            canShift = findFirstEmptyGenericSlot("inv");
            if (canShift !== false) {
              canShift = "Take";
            }
          } else {
            if (inventoryType === "clan_storage") {
              if (
                (typeof globalData[itemType]["no_transfer"] !== "undefined" &&
                  globalData[itemType]["no_transfer"] === true) ||
                realTarget.dataset.type.indexOf("_nt") >= 0
              ) {
                displayPlacementMessage(
                  "This item is non-transferable",
                  mousePos[0] - 40,
                  mousePos[1] + 10,
                  "ERROR"
                );
              } else {
                canShift = findFirstEmptyClanStorageSlot();
                if (canShift !== false) {
                  canShift = "Store";
                }
              }
            } else {
              canShift = findFirstEmptyStorageSlot();
              if (canShift !== false) {
                canShift = "Store";
              }
            }
          }
        } else if (
          document.getElementById("implants") &&
          realTarget.dataset.itemtype &&
          realTarget.dataset.itemtype === "implant"
        ) {
          if (realTarget.parentNode.parentNode.parentNode.id === "implants") {
            canShift = findFirstEmptyGenericSlot("inv");
            if (canShift) {
              canShift = "Unequip";
            }
          } else {
            canShift = findFirstEmptyGenericSlot("implant");
            if (canShift !== false) {
              canShift = isImplantAvailable(itemType);
            }
            if (canShift !== false) {
              canShift = "Equip";
            }
          }
        } else if (
          document.getElementById("character") &&
          realTarget.dataset.itemtype
        ) {
          if (realTarget.dataset.itemtype === "weapon") {
            if (realTarget.parentNode.dataset.slottype === "weapon") {
              canShift = findFirstEmptyGenericSlot("inv");
              if (canShift !== false) {
                canShift = "Unequip";
              }
            } else {
              let levelReq =
                globalData[itemType]["level_req"] <=
                parseInt(userVars["DFSTATS_df_level"]);
              let strReq =
                globalData[itemType]["str_req"] <=
                parseInt(userVars["DFSTATS_df_strength"]);
              if (levelReq && strReq) {
                for (var i = 1; i <= 3; i++) {
                  if (userVars["DFSTATS_df_weapon" + i + "type"] === "") {
                    canShift = "Equip";
                    break;
                  }
                }
              }
            }
          } else if (realTarget.dataset.itemtype === "armour") {
            if (realTarget.parentNode.dataset.slottype === "armour") {
              canShift = findFirstEmptyGenericSlot("inv");
              if (canShift !== false) {
                canShift = "Unequip";
              }
            } else {
              let canPassStrReq =
                parseInt(globalData[itemType]["str_req"]) <=
                parseInt(userVars["DFSTATS_df_strength"]);
              if (canPassStrReq) {
                canShift = "Equip";
              }
            }
          } else {
            if (
              globalData[itemType]["clothingtype"] &&
              globalData[itemType]["clothingtype"] !== "" &&
              unblockedSlot(itemType)
            ) {
              canShift = "Equip";
            }
          }
        } else if (
          document.getElementById("marketplace") &&
          (marketScreen === "sell" ||
            (marketScreen === "itemforitem" && inTradeWindow)) &&
          !realTarget.parentNode.classList.contains("locked")
        ) {
          if (
            globalData[itemType]["no_transfer"] !== true &&
            realTarget.dataset.type.indexOf("_nt") === -1 &&
            parseInt(tradeListSize) < parseInt(userVars["DFSTATS_df_invslots"])
          ) {
            canShift = "Trade";
          }
        }
        if (canShift !== false) {
          if (allowShifting) {
            displayPlacementMessage(
              canShift,
              mousePos[0] + 10,
              mousePos[1] + 10,
              "ACTION"
            );
          } else {
            displayPlacementMessage(
              "Please wait...",
              mousePos[0] + 10,
              mousePos[1] + 10,
              "ERROR"
            );
          }
          return;
        }
      } else {
        cleanPlacementMessage();
      }
    } else if (activeKeys.includes("control")) {
      // lockedSlots
      if (realTarget.classList.contains("item")) {
        realTarget = realTarget.parentNode;
      }
      if (
        realTarget.classList.contains("validSlot") &&
        realTarget.dataset.slot
      ) {
        var item_slot = parseInt(realTarget.dataset.slot);

        switch (realTarget.parentNode.parentNode.id) {
          case "backpackdisplay":
            item_slot += 50;
          case "implants":
            item_slot += 1000;
            break;
          case "storage":
            if (inventoryType === "clan_storage") {
              return;
            }
            item_slot += 40;
            break;
        }
        item_slot += "";
        if (lockedSlots.includes(item_slot)) {
          displayPlacementMessage(
            "Unlock",
            mousePos[0] + 10,
            mousePos[1] + 10,
            "ACTION"
          );
        } else {
          displayPlacementMessage(
            "Lock",
            mousePos[0] + 10,
            mousePos[1] + 10,
            "ACTION"
          );
        }
        return;
      } else {
        cleanPlacementMessage();
      }
    }
    if (activeKeys.includes("alt")) {
      if (
        realTarget.classList.contains("item") &&
        typeof realTarget.dataset.type !== "undefined"
      ) {
        var itemType = realTarget.dataset.type.split("_")[0].trim();

        if (parseInt(globalData[itemType]["armourrestore"]) > 0) {
          e.preventDefault();
          displayPlacementMessage(
            "Move onto any Armour",
            mousePos[0] + 10,
            mousePos[1] + 10,
            "INFO"
          );
          return;
        } else if (document.getElementById("marketplace") !== null) {
          let selectedCategory =
            document.getElementById("categoryChoice").dataset.catname;
          if (
            selectedCategory === "Engineer" ||
            selectedCategory === "Chef" ||
            selectedCategory === "Doctor"
          ) {
            let itemDisplay = document.getElementById("itemDisplay");
            if (itemDisplay !== null && itemDisplay.lastChild !== null) {
              if (globalData[itemType]["itemtype"] === "armour") {
                e.preventDefault();
                displayPlacementMessage(
                  "Click to jump to relevant engineer level",
                  mousePos[0] + 10,
                  mousePos[1] + 10,
                  "INFO"
                );
                return;
              }
              if (globalData[itemType]["needcook"] === true) {
                e.preventDefault();
                displayPlacementMessage(
                  "Click to jump to relevant chef level",
                  mousePos[0] + 10,
                  mousePos[1] + 10,
                  "INFO"
                );
                return;
              }
              if (globalData[itemType]["needdoctor"] === true) {
                e.preventDefault();
                displayPlacementMessage(
                  "Click to jump to relevant doctor level",
                  mousePos[0] + 10,
                  mousePos[1] + 10,
                  "INFO"
                );
                return;
              }
            }
          }
        }
        cleanPlacementMessage();
      } else {
        cleanPlacementMessage();
      }
    } else if (activeKeys.includes("s")) {
      if (realTarget.classList.contains("item")) {
        if (
          (realTarget.parentNode.parentNode.parentNode.id === "inventory" &&
            findFirstEmptyGenericSlot("inv") !== false) ||
          (realTarget.parentNode.parentNode.parentNode.id ===
            "backpackdisplay" &&
            findFirstEmptyBackpackSlot() !== false)
        ) {
          let targetItem = new InventoryItem(realTarget.dataset.type);
          if (
            typeof globalData[targetItem.type] !== "undefined" &&
            typeof globalData[targetItem.type]["max_quantity"] !==
              "undefined" &&
            globalData[targetItem.type]["max_quantity"] > 1 &&
            realTarget.dataset.quantity > 1
          ) {
            e.preventDefault();
            if (
              realTarget.dataset.quantity <=
              globalData[targetItem.type]["max_quantity"]
            ) {
              displayPlacementMessage(
                "Click to split stack",
                mousePos[0] + 10,
                mousePos[1] + 10,
                "INFO"
              );
            } else {
              displayPlacementMessage(
                "You cannot split overstacks",
                mousePos[0] + 10,
                mousePos[1] + 10,
                "ERROR"
              );
            }
            return;
          }
        }
      }
      cleanPlacementMessage();
    } else {
      cleanPlacementMessage();
    }
  }
}

/**
 * @param {KeyboardEvent} e
 */
function activatedKey(e) {
  if (!activeKeys.includes(e.key.toLowerCase())) {
    activeKeys.push(e.key.toLowerCase());
  }
  hoverAction(e);
}

/**
 *
 * @param {KeyboardEvent} e
 */
function deactivatedKey(e) {
  if (activeKeys.includes(e.key.toLowerCase())) {
    activeKeys.splice(activeKeys.indexOf(e.key.toLowerCase()), 1);
  }
  hoverAction(e);
}

function isImplantAvailable(
  itemType,
  targetSlot,
  doPlacementMessages,
  doHighlighting
) {
  if (typeof targetSlot === "undefined") {
    targetSlot = false;
  }
  if (typeof doPlacementMessages === "undefined") {
    doPlacementMessages = false;
  }
  if (typeof doHighlighting === "undefined") {
    doHighlighting = false;
  }
  var actualItemCode = itemType.split("_")[0];
  var isNotBlocked = true;
  for (var i = 1; i <= userVars["DFSTATS_df_implantslots"]; i++) {
    if (userVars["DFSTATS_df_implant" + i + "_type"] !== "") {
      var actualSlotCode =
        userVars["DFSTATS_df_implant" + i + "_type"].split("_")[0];
      if (typeof globalData[actualSlotCode]["implant_block"] !== "undefined") {
        var checkType = globalData[actualSlotCode]["implant_block"].split(",");
        for (var k in checkType) {
          checkType[k] = checkType[k].trim();
          if (targetSlot !== false && targetSlot === i) {
            if (actualItemCode === checkType[k]) {
              isNotBlocked = true;
              break;
            }
          }
          if (actualItemCode === checkType[k]) {
            if (doPlacementMessages) {
              displayPlacementMessage(
                "This implant is not compatible with " +
                  globalData[actualSlotCode]["name"],
                mousePos[0] + 10,
                mousePos[1] + 10,
                "ERROR"
              );
            }
            if (doHighlighting) {
              document
                .querySelector(`[data-slottype='implant'][data-slot='${i}']`)
                .classList.add("highlight");
            }
            isNotBlocked = false;
            break;
          }
        }
      }
      if (
        globalData[actualSlotCode]["implant_unique"] &&
        globalData[actualSlotCode]["implant_unique"] === true
      ) {
        if (actualItemCode === actualSlotCode) {
          if (doPlacementMessages) {
            displayPlacementMessage(
              "You can only equip one of these at a time",
              mousePos[0] + 10,
              mousePos[1] + 10,
              "ERROR"
            );
          }
          if (doHighlighting) {
            document
              .querySelector(`[data-slottype='implant'][data-slot='${i}']`)
              .classList.add("highlight");
          }
          isNotBlocked = false;
          break;
        }
      }
    }
  }
  return isNotBlocked;
}

function dragStart(e) {
  if (!active && !pageLock) {
    var realTarget = e.target;
    if (realTarget === infoBox) {
      infoBox.style.visibility = "hidden";
      realTarget = document.elementFromPoint(mousePos[0], mousePos[1]);
      infoBox.style.visibility = "visible";
    }

    if (activeKeys.includes("control")) {
      if (
        realTarget.classList.contains("validSlot") ||
        realTarget.classList.contains("item")
      ) {
        var ctrlTarget = realTarget;
        if (ctrlTarget.classList.contains("item")) {
          ctrlTarget = ctrlTarget.parentNode;
        }
        if (
          ctrlTarget.classList.contains("validSlot") &&
          ctrlTarget.dataset.slot &&
          inventoryType !== "clan_storage"
        ) {
          promptLoading();

          var item_slot = parseInt(ctrlTarget.dataset.slot);

          switch (ctrlTarget.parentNode.parentNode.id) {
            case "backpackdisplay":
              item_slot += 50;
            case "implants":
              item_slot += 1000;
              break;
            case "storage":
              item_slot += 40;
              break;
          }

          item_slot += "";

          var dataArr = [];
          dataArr["itemnum"] = item_slot;
          dataArr["userID"] = userVars["userID"];
          dataArr["password"] = userVars["password"];
          dataArr["sc"] = userVars["sc"];
          dataArr["gv"] = 21;

          if (lockedSlots.includes(item_slot)) {
            dataArr["action"] = "removeSlot";
          } else {
            dataArr["action"] = "addSlot";
          }

          webCall(
            "hotrods/item_lock",
            dataArr,
            function (data) {
              if (dataArr["action"] === "removeSlot") {
                ctrlTarget.classList.remove("locked");
              }
              lockedSlots = data.split(",");
              doLockedElems();
              pageLock = false;
              df_prompt.parentNode.style.display = "none";
              df_prompt.innerHTML = "";

              if (document.getElementById("backpackdisplay") !== null) {
                populateBackpack();
              }
              if (document.getElementById("storage") !== null) {
                populateStorage();
              }
            },
            true
          );
          return;
        }
      }
    } else if (activeKeys.includes["alt"]) {
      if (document.getElementById("marketplace") !== null) {
        let itemType = realTarget.dataset.type.split("_")[0].trim();
        let selectedCategory =
          document.getElementById("categoryChoice").dataset.catname;
        let targetIcon = (selectedCategory = "Icon");
        let checkPass = false;
        let checkLevelEquation = Infinity;
        switch (selectedCategory) {
          case "Engineer":
            checkPass = globalData[itemType]["itemtype"] === "armour";
            checkLevelEquation = globalData[itemType]["shop_level"] - 5;
            break;
          case "Chef":
            checkPass = globalData[itemType]["needcook"] === true;
            checkLevelEquation = globalData[itemType]["level"] - 5;
            break;
          case "Doctor":
            checkPass = globalData[itemType]["needdoctor"] === true;
            checkLevelEquation = globalData[itemType]["level"] - 5;
            break;
        }

        if (checkPass) {
          if (realTarget.classList.contains("item")) {
            let itemDisplay = document.getElementById("itemDisplay");
            if (itemDisplay !== null && itemDisplay.lastChild !== null) {
              let targetServices = itemDisplay.querySelectorAll(
                "div." + targetIcon + "[data-level]"
              );
              for (let targetService of targetServices) {
                if (targetService.dataset.level >= checkLevelEquation) {
                  itemDisplay.scrollTop = targetService.offsetTop;
                  return;
                }
              }
            }
          }
        }
      }
    }

    if (realTarget.classList.contains("item")) {
      let realTargetItem = new InventoryItem(realTarget.dataset.type);
      var itemType = realTarget.dataset.type.split("_")[0].trim();
      e.preventDefault();
      if (activeKeys.includes("shift")) {
        if (allowShifting) {
          var canShift = false;
          if (document.getElementById("storage")) {
            if (realTarget.parentNode.parentNode.parentNode.id === "storage") {
              canShift = findFirstEmptyGenericSlot("inv");
            } else {
              if (inventoryType === "clan_storage") {
                if (
                  (typeof globalData[realTargetItem.type]["no_transfer"] !==
                    "undefined" &&
                    globalData[realTargetItem.type]["no_transfer"] === true) ||
                  realTarget.dataset.type.indexOf("_nt") >= 0
                ) {
                  canShift = false;
                } else {
                  canShift = findFirstEmptyClanStorageSlot();
                }
              } else {
                canShift = findFirstEmptyStorageSlot();
              }
            }
          } else if (
            document.getElementById("implants") &&
            realTarget.dataset.itemtype &&
            realTarget.dataset.itemtype === "implant"
          ) {
            if (realTarget.parentNode.parentNode.parentNode.id === "implants") {
              canShift = findFirstEmptyGenericSlot("inv");
            } else {
              canShift = findFirstEmptyGenericSlot("implant");
              if (canShift) {
                canShift = isImplantAvailable(realTargetItem.type);
              }
            }
          } else if (
            document.getElementById("character") &&
            realTarget.dataset.itemtype
          ) {
            if (realTarget.dataset.itemtype === "weapon") {
              if (realTarget.parentNode.dataset.slottype === "weapon") {
                canShift = findFirstEmptyGenericSlot("inv");
              } else {
                let levelReq =
                  globalData[itemType]["level_req"] <=
                  parseInt(userVars["DFSTATS_df_level"]);
                let strReq =
                  globalData[itemType]["str_req"] <=
                  parseInt(userVars["DFSTATS_df_strength"]);
                if (levelReq && strReq) {
                  for (var i = 1; i <= 3; i++) {
                    if (userVars["DFSTATS_df_weapon" + i + "type"] === "") {
                      canShift = i + 30;
                      break;
                    }
                  }
                }
              }
            } else if (realTarget.dataset.itemtype === "armour") {
              if (realTarget.parentNode.dataset.slottype === "armour") {
                canShift = findFirstEmptyGenericSlot("inv");
              } else {
                var canPassStrReq =
                  parseInt(globalData[realTargetItem.type]["str_req"]) <=
                  parseInt(userVars["DFSTATS_df_strength"]);
                if (canPassStrReq) {
                  canShift = true;
                }
              }
            } else {
              if (
                globalData[realTargetItem.type]["clothingtype"] &&
                globalData[realTargetItem.type]["clothingtype"] !== "" &&
                unblockedSlot(realTargetItem.type)
              ) {
                canShift = true;
              }
            }
          } else if (
            document.getElementById("marketplace") &&
            (marketScreen === "sell" ||
              (marketScreen === "itemforitem" && inTradeWindow)) &&
            !realTarget.parentNode.classList.contains("locked")
          ) {
            if (
              (globalData[realTargetItem.type]["no_transfer"] === false ||
                realTarget.dataset.type.indexOf("_nt") === -1) &&
              parseInt(tradeListSize) <
                parseInt(userVars["DFSTATS_df_invslots"])
            ) {
              clearCard(e);
              cleanPlacementMessage();
              if (marketScreen === "itemforitem") {
                doSellFromScript(
                  realTarget,
                  document
                    .getElementById("marketplace")
                    .querySelector("[data-action='tradeitem']")
                );
              } else {
                doSellFromScriptSLP(
                  realTarget,
                  document
                    .getElementById("marketplace")
                    .querySelector("[data-action='sellitem']")
                );
              }
              return;
            }
          }

          if (canShift !== false) {
            clearCard(e);
            cleanPlacementMessage();
            shiftItem(realTarget);
            allowShifting = false;
            setTimeout(function () {
              allowShifting = true;
            }, 250);
            return;
          }
        }
      } else if (activeKeys.includes("s")) {
        if (
          (realTarget.parentNode.parentNode.parentNode.id === "inventory" &&
            findFirstEmptyGenericSlot("inv") !== false) ||
          (realTarget.parentNode.parentNode.parentNode.id ===
            "backpackdisplay" &&
            findFirstEmptyBackpackSlot() !== false)
        ) {
          if (
            typeof globalData[realTargetItem.type] !== "undefined" &&
            typeof globalData[realTargetItem.type]["max_quantity"] !== false &&
            globalData[realTargetItem.type]["max_quantity"] > 1
          ) {
            if (
              realTarget.dataset.quantity <=
              globalData[realTargetItem.type]["max_quantity"]
            ) {
              clearCard(e);
              cleanPlacementMessage();
              splitItem(realTarget, realTargetItem);
              return;
            }
          }
        }
      }

      startX = realTarget.getBoundingClientRect().left;
      startY = realTarget.getBoundingClientRect().top;
      active = true;
      canMove = false;
      currentItem = realTarget;
      currentItem.classList.add("colorShift");
      if (
        currentItem.dataset.quantity &&
        currentItem.dataset.quantity.match(/[0-9]/g)
      ) {
        currentQuantity = parseInt(currentItem.dataset.quantity);
      } else {
        currentQuantity = false;
      }

      var page = window.location.search.substring(1).split("&")[0];
      if (page === "page=24") {
        if (currentItem.dataset.type.indexOf("_name") !== -1) {
          inventoryHolder.querySelector(
            "div[data-action=removerename]"
          ).style.display = "block";
        }
        if (currentItem.parentNode.parentNode.parentNode.id === "inventory")
          if (
            globalData[itemType]["dismantle"] &&
            globalData[itemType]["dismantle"].length > 0
          ) {
            inventoryHolder.querySelector(
              "div[data-action=dismantle]"
            ).style.display = "block";
          }
      }

      fakeGrabbedItem.src = currentItem.style.backgroundImage
        .slice(4, -1)
        .replace(/"/g, "");
      setTimeout(function () {
        var invHoldOffsets = inventoryHolder.getBoundingClientRect();
        fakeGrabbedItem.style.left =
          mousePos[0] - invHoldOffsets.left - fakeGrabbedItem.offsetWidth / 2;
        fakeGrabbedItem.style.top =
          mousePos[1] - invHoldOffsets.top - fakeGrabbedItem.offsetHeight / 2;
        fakeGrabbedItem.classList.add("held");
        fakeGrabbedItem.style.visibility = "visible";
        document.body.style.cursor = "grabbing";
      }, 10);
      if (
        document.getElementById("implantbucket") &&
        document.getElementById("implantMenu") &&
        document.getElementById("implantMenu").dataset.opened === "open"
      ) {
        if (globalData[itemType]["implant"] === true) {
          document.getElementById("implantbucket").classList.add("open");
        }
      }
      clearCard(e);
    }
  }
}

function dragEnd(e) {
  if (active && !pageLock) {
    e.preventDefault();
    if (canMove) {
      if (replacee) {
        var itemArray = {
          0: [
            parseInt(currentItem.parentNode.dataset.slot),
            currentItem.dataset.type,
            currentItem.parentNode.parentNode.parentNode.id,
          ],
          1: [
            parseInt(replacee.parentNode.dataset.slot),
            replacee.dataset.type,
            replacee.parentNode.parentNode.parentNode.id,
          ],
        };

        if (itemOnItemAction !== false) {
          pageLock = true;
          updateInventory(itemArray);
        } else {
          var temp = currentItem.parentNode;
          if (
            (replacee.parentNode.dataset.slottype ===
              currentItem.dataset.itemtype ||
              typeof replacee.parentNode.dataset.slottype === "undefined") &&
            (currentItem.parentNode.dataset.slottype ===
              replacee.dataset.itemtype ||
              typeof currentItem.parentNode.dataset.slottype === "undefined")
          ) {
            replacee.parentNode.appendChild(currentItem);
            temp.appendChild(replacee);
            pageLock = true;
            updateInventory(itemArray);
          }
          setTranslate(0, 0, replacee);
          replacee.classList.remove("colorShift");
          if (replaceeQuantity) {
            replacee.dataset.quantity = replaceeQuantity;
          }
        }
      } else {
        fakeGrabbedItem.style.visibility = "hidden";
        currentItem.style.visibility = "hidden";
        var nSlot = document.elementFromPoint(mousePos[0], mousePos[1]);
        fakeGrabbedItem.style.visibility = "visible";
        while (nSlot.dataset.override) {
          nSlot = nSlot.parentNode;
        }
        currentItem.style.visibility = "visible";
        if (
          (nSlot.classList.contains("fakeItem") ||
            nSlot.classList.contains("profitList")) &&
          nSlot.parentNode.classList.contains("fakeSlot")
        ) {
          nSlot = nSlot.parentNode;
        } else if (
          (nSlot.parentNode.classList.contains("fakeItem") ||
            nSlot.parentNode.classList.contains("profitList")) &&
          nSlot.parentNode.parentNode.classList.contains("fakeSlot")
        ) {
          nSlot = nSlot.parentNode.parentNode;
        }
        if (
          nSlot.classList.contains("validSlot") &&
          !nSlot.hasChildNodes() &&
          (nSlot.dataset.slottype === currentItem.dataset.itemtype ||
            typeof nSlot.dataset.slottype === "undefined")
        ) {
          var itemArray = {
            0: [
              parseInt(currentItem.parentNode.dataset.slot),
              currentItem.dataset.type,
              currentItem.parentNode.parentNode.parentNode.id,
            ],
            1: [
              parseInt(nSlot.dataset.slot),
              "",
              nSlot.parentNode.parentNode.id,
            ],
          };
          nSlot.appendChild(currentItem);
          pageLock = true;
          updateInventory(itemArray);
        } else if (nSlot.classList.contains("fakeSlot")) {
          if (canComplete) {
            pageLock = true;
            dragDropAction(e);
          }
        }
      }
    } else {
      pageLock = false;
    }
    if (document.getElementById("implantbucket")) {
      document.getElementById("implantbucket").classList.remove("open");
    }
    setTranslate(0, 0, currentItem);
    cleanPlacementMessage();
    if (hoverItem) {
      hoverItem.classList.remove("hover");
      hoverItem = null;
    }
    currentItem.classList.remove("colorShift");
    currentItem.classList.remove("held");
    fakeGrabbedItem.style.visibility = "hidden";
    var page = window.location.search.substring(1).split("&")[0];
    if (page === "page=24") {
      inventoryHolder.querySelector(
        "div[data-action=removerename]"
      ).style.display = "none";
      inventoryHolder.querySelector(
        "div[data-action=dismantle]"
      ).style.display = "none";
    }
    replacee = null;
    replaceeQuantity = null;
    currentItem = null;
    startX = null;
    startY = null;
    active = false;
    canMove = false;
    canComplete = false;
    itemOnItemAction = false;
    document.body.style.cursor = "default";
    for (let highlight of document.getElementsByClassName("highlight")) {
      highlight.classList.remove("highlight");
    }
    infoCard(e);
  }
}

function displayPlacementMessage(msg, x, y, type) {
  var floatingText = document.getElementById("textAddon");
  floatingText.innerHTML = msg;
  floatingText.style.width = "165px";
  var invHoldOffsets = inventoryHolder.getBoundingClientRect();
  //floatingText.style.left = x - invHoldOffsets.left + "px";
  //floatingText.style.top = y - invHoldOffsets.top + "px";

  floatingText.style.visibility = "hidden";
  floatingText.style.display = "block";

  if (y + floatingText.offsetHeight > invHoldOffsets.bottom) {
    floatingText.style.top =
      y - floatingText.offsetHeight - 40 - invHoldOffsets.top + "px";
  } else {
    floatingText.style.top = y - invHoldOffsets.top + "px";
  }

  if (x + 20 + floatingText.offsetWidth > invHoldOffsets.right) {
    floatingText.style.left =
      inventoryHolder.offsetWidth - floatingText.offsetWidth + "px";
  } else {
    floatingText.style.left = x + 20 - invHoldOffsets.left + "px";
  }

  switch (type) {
    case "ERROR":
      floatingText.style.color = "red";
      break;
    case "ACTION":
      floatingText.style.color = "#E6CC4D";
      //floatingText.classList.add("cashhack");
      //floatingText.dataset.cash = msg;
      break;
    case "INFO":
      floatingText.style.color = "blue";
      break;
  }

  floatingText.style.visibility = "visible";
}

function cleanPlacementMessage() {
  var floatingText = document.getElementById("textAddon");
  floatingText.style.display = "none";
  floatingText.removeAttribute("class");
}

function unblockedSlot(item) {
  blockingItems = "";
  var output = true;
  item = item.trim();
  if (globalData[item]["clothingblockslot"]) {
    if (
      document.querySelector(
        "[data-slottype='" + globalData[item]["clothingblockslot"] + "']"
      ) &&
      document
        .querySelector(
          "[data-slottype='" + globalData[item]["clothingblockslot"] + "']"
        )
        .hasChildNodes()
    ) {
      blockingItem = globalData[item]["clothingblockslot"];
      output = false;
    }
  }
  var slots = ["hat", "mask", "coat", "armour", "shirt", "trousers"];
  for (var i in slots) {
    if (
      document.querySelector("[data-slottype='" + slots[i] + "']") &&
      document
        .querySelector("[data-slottype='" + slots[i] + "']")
        .hasChildNodes()
    ) {
      var iSlotItem = document
        .querySelector("[data-slottype='" + slots[i] + "']")
        .childNodes[0].dataset.type.split("_")[0];
      if (
        globalData[iSlotItem]["clothingblockslot"] &&
        globalData[item]["clothingtype"] &&
        globalData[iSlotItem]["clothingblockslot"] ===
          globalData[item]["clothingtype"]
      ) {
        blockingItem = globalData[iSlotItem]["clothingtype"];
        output = false;
        break;
      }
    }
  }
  return output;
}

var ieVersion = false;
if (window.document.documentMode !== undefined) {
  ieVersion = true;
}
function drag(e) {
  for (let highlight of document.getElementsByClassName("highlight")) {
    highlight.classList.remove("highlight");
  }
  if (active && !pageLock) {
    e.preventDefault();
    if (ieVersion) {
      if (dragIteration >= 2) {
        dragIteration = 0;
      } else {
        dragIteration++;
      }
    }
    if (!ieVersion || dragIteration === 0) {
      var slot;
      var displayText = false,
        valid = false;
      canMove = false;
      hovering = false;
      canComplete = false;
      var currentItemVariables = currentItem.dataset.type.split("_");
      var itemType = currentItemVariables[0].trim();
      let currentItemII = new InventoryItem(currentItem.dataset.type);
      currentX = mousePos[0];
      currentY = mousePos[1];

      currentX -= startX - 20;
      currentY -= startY - 20;
      if (currentQuantity !== false) {
        currentItem.dataset.quantity = currentQuantity;
      } else {
        currentItem.dataset.quantity = "";
      }
      if (replacee) {
        replacee.classList.remove("colorShift");
        setTranslate(0, 0, replacee);
        replacee.style.visibility = "visible";
        if (replaceeQuantity !== false) {
          replacee.dataset.quantity = replaceeQuantity;
        } else {
          replacee.dataset.quantity = "";
        }
        replacee = null;
        replaceeQuantity = false;
      }
      if (
        inventoryHolder.getBoundingClientRect().left < mousePos[0] &&
        inventoryHolder.getBoundingClientRect().right > mousePos[0] &&
        inventoryHolder.getBoundingClientRect().top < mousePos[1] &&
        inventoryHolder.getBoundingClientRect().bottom > mousePos[1]
      ) {
        valid = true;
        fakeGrabbedItem.style.display = "none";
        currentItem.style.visibility = "hidden";
        slot = document.elementFromPoint(mousePos[0], mousePos[1]);
        while (slot.dataset.override) {
          slot = slot.parentNode;
        }
        if (slot.classList.contains("validSlot") && !slot.hasChildNodes()) {
          // Empty slot code
          if (
            slot.dataset.slottype === currentItem.dataset.itemtype ||
            (typeof slot.dataset.slottype === "undefined" &&
              currentItem.dataset.itemtype !== "credits")
          ) {
            if (slot.dataset.slottype === "armour") {
              canMove = true;
              if (
                parseInt(globalData[itemType]["str_req"]) >
                parseInt(userVars["DFSTATS_df_strength"])
              ) {
                displayPlacementMessage(
                  "You don't have enough strength",
                  mousePos[0] + 10,
                  mousePos[1] + 10,
                  "ERROR"
                );
                displayText = true;
                canMove = false;
              }
              if (canMove) {
                currentX = slot.getBoundingClientRect().left - startX;
                currentY = slot.getBoundingClientRect().top - startY;
                setTranslate(currentX, currentY, currentItem);
                fakeGrabbedItem.style.visibility = "hidden";
              }
            } else if (slot.dataset.slottype === "weapon") {
              canMove = true;
              if (
                globalData[itemType]["level_req"] >
                parseInt(userVars["DFSTATS_df_level"])
              ) {
                displayPlacementMessage(
                  "You are too low level",
                  mousePos[0] + 10,
                  mousePos[1] + 10,
                  "ERROR"
                );
                displayText = true;
                canMove = false;
              }
              /*if (parseInt(globalData[itemType]["pro_req"]) > parseInt(userVars["DFSTATS_df_pro" + (globalData[itemType]["pro_type"])]))
							{
								displayPlacementMessage("You don't have enough " + globalData[itemType]["weptype"].toLowerCase() + " proficiency", mousePos[0] + 10, mousePos[1] + 10, "ERROR");
								displayText = true;
								canMove = false;
							}*/
              if (
                canMove &&
                parseInt(globalData[itemType]["str_req"]) >
                  parseInt(userVars["DFSTATS_df_strength"])
              ) {
                displayPlacementMessage(
                  "You don't have enough strength",
                  mousePos[0] + 10,
                  mousePos[1] + 10,
                  "ERROR"
                );
                displayText = true;
                canMove = false;
              }
              if (canMove) {
                currentX = slot.getBoundingClientRect().left - startX;
                currentY = slot.getBoundingClientRect().top - startY;
                setTranslate(currentX, currentY, currentItem);
                fakeGrabbedItem.style.visibility = "hidden";
              }
            } else if (slot.dataset.slottype === "implant") {
              canMove = true;
              if (currentItem.parentNode.dataset.slottype !== "implant") {
                if (!isImplantAvailable(itemType, false, true, true)) {
                  canMove = false;
                  displayText = true;
                }
              }
              if (canMove) {
                currentX = slot.getBoundingClientRect().left - startX;
                currentY = slot.getBoundingClientRect().top - startY;
                setTranslate(currentX, currentY, currentItem);
                fakeGrabbedItem.style.visibility = "hidden";
              }
            } else if (currentItem.parentNode.dataset.slottype === "backpack") {
              canMove = true;

              if (slot.parentNode.parentNode.id === "backpackdisplay") {
                canMove = false;
                displayPlacementMessage(
                  "Cannot put backpack inside of itself",
                  mousePos[0] + 10,
                  mousePos[1] + 10,
                  "ERROR"
                );
              }
              if (canMove) {
                var totalBackpackSlots = parseInt(
                  globalData[itemType]["slots"]
                );
                if (currentItem.dataset.type.indexOf("_stats") >= 0) {
                  totalBackpackSlots += parseInt(
                    currentItem.dataset.type.charAt(
                      currentItem.dataset.type.indexOf("_stats") + 6
                    )
                  );
                }
                for (var i = 1; i <= totalBackpackSlots; i++) {
                  if (
                    typeof userVars["DFSTATS_df_backpack" + i + "_type"] !==
                      "undefined" &&
                    userVars["DFSTATS_df_backpack" + i + "_type"] !== ""
                  ) {
                    canMove = false;
                    displayPlacementMessage(
                      "Backpack must be empty to unequip it",
                      mousePos[0] + 10,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                    break;
                  }
                }
              }
              if (canMove) {
                currentX = slot.getBoundingClientRect().left - startX;
                currentY = slot.getBoundingClientRect().top - startY;
                setTranslate(currentX, currentY, currentItem);
                fakeGrabbedItem.style.visibility = "hidden";
              } else {
                displayText = true;
              }
            } else if (
              slot.parentNode.parentNode.id === "storage" &&
              inventoryType === "clan_storage"
            ) {
              if (
                (typeof globalData[itemType]["no_transfer"] !== "undefined" &&
                  globalData[itemType]["no_transfer"] === true) ||
                currentItem.dataset.type.indexOf("_nt") >= 0
              ) {
                displayPlacementMessage(
                  "This item is non-transferable",
                  mousePos[0] - 40,
                  mousePos[1] + 10,
                  "ERROR"
                );
                displayText = true;
              } else {
                canMove = true;
                currentX = slot.getBoundingClientRect().left - startX;
                currentY = slot.getBoundingClientRect().top - startY;
                setTranslate(currentX, currentY, currentItem);
                fakeGrabbedItem.style.visibility = "hidden";
              }
            } else if (
              unblockedSlot(itemType) ||
              typeof slot.dataset.slottype === "undefined"
            ) {
              canMove = true;
              currentX = slot.getBoundingClientRect().left - startX;
              currentY = slot.getBoundingClientRect().top - startY;
              setTranslate(currentX, currentY, currentItem);
              fakeGrabbedItem.style.visibility = "hidden";
            } else {
              displayPlacementMessage(
                "Something is blocking this item",
                mousePos[0] + 10,
                mousePos[1] + 10,
                "ERROR"
              );
              displayText = true;
            }
          } else {
            if (currentItem.dataset.itemtype === "credits") {
              displayPlacementMessage(
                "You cannot place credits here",
                mousePos[0] + 10,
                mousePos[1] + 10,
                "ERROR"
              );
              displayText = true;
            } else {
              displayPlacementMessage(
                "You cannot place this item here",
                mousePos[0] + 10,
                mousePos[1] + 10,
                "ERROR"
              );
              displayText = true;
            }
          }
        } else if (
          slot.classList.contains("item") ||
          slot.classList.contains("validSlot")
        ) {
          // Switch items
          if (slot.classList.contains("validSlot")) {
            slot = slot.childNodes[0];
          }
          if (slot !== currentItem) {
            var slotType = slot.dataset.type.trim().split("_")[0];
            var slotMatchCheck = false;
            if (slot.dataset.itemtype === currentItem.dataset.itemtype) {
              slotMatchCheck = true;
            }
            var neitherCredits = false;
            if (
              currentItem.dataset.itemtype !== "credits" &&
              slot.dataset.itemtype !== "credits"
            ) {
              neitherCredits = true;
            }
            var curSlotClear = false;
            if (
              typeof currentItem.parentNode.dataset.slottype === "undefined"
            ) {
              curSlotClear = true;
            }
            var nuSlotClear = false;
            if (typeof slot.parentNode.dataset.slottype === "undefined") {
              nuSlotClear = true;
            }
            var parentTypeMatch = false;
            if (
              currentItem.parentNode.dataset.slottype &&
              slot.parentNode.dataset.slottype &&
              currentItem.parentNode.dataset.slottype ===
                slot.parentNode.dataset.slottype
            ) {
              parentTypeMatch = true;
            }
            var aggressorClear = false;
            if (unblockedSlot(itemType)) {
              aggressorClear = true;
            }
            if (unblockedSlot(slotType)) {
              aggressorClear = true;
            }
            if (
              (parentTypeMatch && aggressorClear) ||
              (slotMatchCheck &&
                nuSlotClear &&
                unblockedSlot(itemType) &&
                unblockedSlot(slotType)) ||
              (slotMatchCheck &&
                curSlotClear &&
                unblockedSlot(slotType) &&
                unblockedSlot(itemType)) ||
              (curSlotClear && nuSlotClear && neitherCredits)
            ) {
              replacee = slot;
              if (
                slot.dataset.quantity &&
                slot.dataset.quantity.match(/[0-9]/g)
              ) {
                replaceeQuantity = parseInt(slot.dataset.quantity);
              } else {
                replaceeQuantity = false;
              }
              if (slot.parentNode.dataset.slottype === "armour") {
                canMove = true;
                if (
                  parseInt(globalData[itemType]["str_req"]) >
                  parseInt(userVars["DFSTATS_df_strength"])
                ) {
                  displayPlacementMessage(
                    "You don't have enough strength",
                    mousePos[0] + 10,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                  canMove = false;
                }
              } else if (currentItem.parentNode.dataset.slottype === "armour") {
                canMove = true;
                if (
                  parseInt(globalData[slotType]["str_req"]) >
                  parseInt(userVars["DFSTATS_df_strength"])
                ) {
                  displayPlacementMessage(
                    "You don't have enough strength",
                    mousePos[0] + 10,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                  canMove = false;
                }
              } else if (slot.parentNode.dataset.slottype === "weapon") {
                canMove = true;
                if (
                  globalData[itemType]["level_req"] >
                  parseInt(userVars["DFSTATS_df_level"])
                ) {
                  displayPlacementMessage(
                    "You are too low level",
                    mousePos[0] + 10,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                  canMove = false;
                }
                /*if (parseInt(globalData[itemType]["pro_req"]) > parseInt(userVars["DFSTATS_df_pro" + (globalData[itemType]["pro_type"])]))
								{
									displayPlacementMessage("You don't have enough " + globalData[itemType]["weptype"].toLowerCase() + " proficiency", mousePos[0] + 10, mousePos[1] + 10, "ERROR");
									displayText = true;
									canMove = false;
								}*/
                if (
                  canMove &&
                  parseInt(globalData[itemType]["str_req"]) >
                    parseInt(userVars["DFSTATS_df_strength"])
                ) {
                  displayPlacementMessage(
                    "You don't have enough strength",
                    mousePos[0] + 10,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                  canMove = false;
                }
              } else if (currentItem.parentNode.dataset.slottype === "weapon") {
                canMove = true;
                if (
                  globalData[slotType]["level_req"] >
                  parseInt(userVars["DFSTATS_df_level"])
                ) {
                  displayPlacementMessage(
                    "You are too low level",
                    mousePos[0] + 10,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                  canMove = false;
                }
                /*if (parseInt(globalData[slotType]["pro_req"]) > parseInt(userVars["DFSTATS_df_pro" + (globalData[slotType]["pro_type"])]))
								{
									displayPlacementMessage("You don't have enough " + globalData[slotType]["weptype"].toLowerCase() + " proficiency", mousePos[0] + 10, mousePos[1] + 10, "ERROR");
									displayText = true;
									canMove = false;
								}*/
                if (
                  canMove &&
                  parseInt(globalData[slotType]["str_req"]) >
                    parseInt(userVars["DFSTATS_df_strength"])
                ) {
                  displayPlacementMessage(
                    "You don't have enough strength",
                    mousePos[0] + 10,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                  canMove = false;
                }
              } else if (slot.parentNode.dataset.slottype === "implant") {
                canMove = true;
                if (currentItem.parentNode.dataset.slottype !== "implant") {
                  if (
                    !isImplantAvailable(
                      itemType,
                      parseInt(slot.parentNode.dataset.slot),
                      true,
                      true
                    )
                  ) {
                    canMove = false;
                    displayText = true;
                  }
                }
              } else if (
                currentItem.parentNode.dataset.slottype === "implant"
              ) {
                canMove = true;
                if (
                  !isImplantAvailable(
                    slotType,
                    parseInt(currentItem.parentNode.dataset.slot),
                    true,
                    true
                  )
                ) {
                  canMove = false;
                  displayText = true;
                }
              } else if (slot.parentNode.dataset.slottype === "backpack") {
                canMove = true;

                var totalCurrentBackpackSlots = parseInt(
                  globalData[slotType]["slots"]
                );
                if (replacee.dataset.type.indexOf("_stats") >= 0) {
                  totalCurrentBackpackSlots += parseInt(
                    replacee.dataset.type.charAt(
                      replacee.dataset.type.indexOf("_stats") + 6
                    )
                  );
                }
                var lastUsedSlot = 0;
                var totalItemsInPack = 0;
                for (var i = 1; i <= totalCurrentBackpackSlots; i++) {
                  if (
                    typeof userVars["DFSTATS_df_backpack" + i + "_type"] !==
                      "undefined" &&
                    userVars["DFSTATS_df_backpack" + i + "_type"] !== ""
                  ) {
                    lastUsedSlot = i;
                    totalItemsInPack++;
                  }
                }

                var totalNuBackpackSlots = parseInt(
                  globalData[itemType]["slots"]
                );
                if (currentItem.dataset.type.indexOf("_stats") >= 0) {
                  totalNuBackpackSlots += parseInt(
                    currentItem.dataset.type.charAt(
                      currentItem.dataset.type.indexOf("_stats") + 6
                    )
                  );
                }
                var bpD = document.getElementById("backpackdisplay");
                if (totalItemsInPack > totalNuBackpackSlots) {
                  canMove = false;
                  displayPlacementMessage(
                    "New backpack is too small to hold all of your items",
                    mousePos[0] + 10,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  if (bpD !== null) {
                    var bp_validItems = 0;
                    for (var i = 1; i <= totalCurrentBackpackSlots; i++) {
                      if (
                        typeof userVars["DFSTATS_df_backpack" + i + "_type"] !==
                          "undefined" &&
                        userVars["DFSTATS_df_backpack" + i + "_type"] !== ""
                      ) {
                        bp_validItems++;
                        if (bp_validItems > totalNuBackpackSlots) {
                          bpD
                            .querySelector("td[data-slot='" + i + "']")
                            .classList.add("highlight");
                        } else if (i > totalNuBackpackSlots) {
                          bpD
                            .querySelector("td[data-slot='" + i + "']")
                            .classList.add("highlight", "special");
                        }
                      }
                    }
                  }
                  displayText = true;
                } else if (lastUsedSlot > totalNuBackpackSlots) {
                  canMove = false;
                  displayPlacementMessage(
                    "Items need to be rearranged for new backpack",
                    mousePos[0] + 10,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  if (bpD !== null) {
                    for (var i = 1; i <= totalCurrentBackpackSlots; i++) {
                      if (
                        typeof userVars["DFSTATS_df_backpack" + i + "_type"] !==
                          "undefined" &&
                        userVars["DFSTATS_df_backpack" + i + "_type"] !== ""
                      ) {
                        if (i > totalNuBackpackSlots) {
                          bpD
                            .querySelector("td[data-slot='" + i + "']")
                            .classList.add("highlight", "special");
                        }
                      }
                    }
                  }
                  displayText = true;
                }
              } else if (
                currentItem.parentNode.dataset.slottype === "backpack"
              ) {
                canMove = true;

                var totalCurrentBackpackSlots = parseInt(
                  globalData[itemType]["slots"]
                );
                if (currentItem.dataset.type.indexOf("_stats") >= 0) {
                  totalCurrentBackpackSlots += parseInt(
                    currentItem.dataset.type.charAt(
                      currentItem.dataset.type.indexOf("_stats") + 6
                    )
                  );
                }
                var lastUsedSlot = 0;
                var totalItemsInPack = 0;
                for (var i = 1; i <= totalCurrentBackpackSlots; i++) {
                  if (
                    typeof userVars["DFSTATS_df_backpack" + i + "_type"] !==
                      "undefined" &&
                    userVars["DFSTATS_df_backpack" + i + "_type"] !== ""
                  ) {
                    lastUsedSlot = i;
                    totalItemsInPack++;
                  }
                }

                var totalNuBackpackSlots = parseInt(
                  globalData[slotType]["slots"]
                );
                if (replacee.dataset.type.indexOf("_stats") >= 0) {
                  totalNuBackpackSlots += parseInt(
                    replacee.dataset.type.charAt(
                      replacee.dataset.type.indexOf("_stats") + 6
                    )
                  );
                }
                var bpD = document.getElementById("backpackdisplay");
                if (totalItemsInPack > totalNuBackpackSlots) {
                  canMove = false;
                  displayPlacementMessage(
                    "New backpack is too small to hold all of your items",
                    mousePos[0] + 10,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  if (bpD !== null) {
                    var bp_validItems = 0;
                    for (var i = 1; i <= totalCurrentBackpackSlots; i++) {
                      if (
                        typeof userVars["DFSTATS_df_backpack" + i + "_type"] !==
                          "undefined" &&
                        userVars["DFSTATS_df_backpack" + i + "_type"] !== ""
                      ) {
                        bp_validItems++;
                        if (bp_validItems > totalNuBackpackSlots) {
                          bpD
                            .querySelector("td[data-slot='" + i + "']")
                            .classList.add("highlight");
                        }
                      }
                    }
                  }
                  displayText = true;
                } else if (lastUsedSlot > totalNuBackpackSlots) {
                  canMove = false;
                  displayPlacementMessage(
                    "Items need to be rearranged for new backpack",
                    mousePos[0] + 10,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  if (bpD !== null) {
                    for (var i = 1; i <= totalCurrentBackpackSlots; i++) {
                      if (
                        typeof userVars["DFSTATS_df_backpack" + i + "_type"] !==
                          "undefined" &&
                        userVars["DFSTATS_df_backpack" + i + "_type"] !== ""
                      ) {
                        if (i > totalNuBackpackSlots) {
                          bpD
                            .querySelector("td[data-slot='" + i + "']")
                            .classList.add("highlight");
                        }
                      }
                    }
                  }
                  displayText = true;
                }
              } else if (
                slotMatchCheck &&
                slot.dataset.itemtype === "ammo" &&
                currentItem.dataset.type === slot.dataset.type
              ) {
                if (currentQuantity !== false && replaceeQuantity !== false) {
                  if (
                    replaceeQuantity <
                    parseInt(globalData[itemType]["max_quantity"])
                  ) {
                    var combinedQuantity = currentQuantity + replaceeQuantity;
                    if (
                      combinedQuantity >
                      parseInt(globalData[itemType]["max_quantity"])
                    ) {
                      currentItem.dataset.quantity =
                        globalData[itemType]["max_quantity"];
                      replacee.dataset.quantity =
                        combinedQuantity -
                        parseInt(globalData[itemType]["max_quantity"]);
                    } else {
                      currentItem.dataset.quantity = combinedQuantity;
                      replacee.dataset.quantity = "";
                      replacee.style.visibility = "hidden";
                    }
                  }
                  canMove = true;
                }
              } else if (
                replacee.parentNode.parentNode.parentNode.id === "storage" &&
                inventoryType === "clan_storage"
              ) {
                if (
                  (typeof globalData[itemType]["no_transfer"] !== "undefined" &&
                    globalData[itemType]["no_transfer"] === true) ||
                  currentItem.dataset.type.indexOf("_nt") >= 0
                ) {
                  displayPlacementMessage(
                    "This item is non-transferable",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                } else {
                  canMove = true;
                }
              } else if (
                currentItem.parentNode.parentNode.parentNode.id === "storage" &&
                inventoryType === "clan_storage"
              ) {
                if (
                  (globalData[slotType]["no_transfer"] &&
                    globalData[slotType]["no_transfer"] === true) ||
                  replacee.dataset.type.indexOf("_nt") >= 0
                ) {
                  displayPlacementMessage(
                    "This item is non-transferable",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                } else {
                  canMove = true;
                }
              } else if (slot.dataset.type === currentItem.dataset.type) {
                canMove = false;
              } else if (
                e.altKey &&
                globalData[itemType]["armourrestore"] > 0 &&
                slot.dataset.itemtype === "armour"
              ) {
                let minDamageCanRestore =
                  parseInt(globalData[itemType]["min_repair"] ?? 0) / 100;
                let maxLevelToRestore =
                  parseInt(globalData[itemType]["max_repair"] ?? 100) / 100;
                if (
                  replaceeQuantity >=
                    Math.floor(
                      parseInt(globalData[slotType]["hp"]) * minDamageCanRestore
                    ) &&
                  replaceeQuantity <
                    Math.floor(
                      parseInt(globalData[slotType]["hp"]) * maxLevelToRestore
                    )
                ) {
                  canMove = false;
                  displayPlacementMessage(
                    "Repair armour",
                    mousePos[0] + 10,
                    mousePos[1] + 10,
                    "ACTION"
                  );
                  displayText = true;
                  canComplete = true;
                  itemOnItemAction = "repair";
                } else {
                  canMove = true;
                }
              } else {
                canMove = true;
              }
              if (canMove) {
                currentX = slot.getBoundingClientRect().left - startX;
                currentY = slot.getBoundingClientRect().top - startY;
                setTranslate(-currentX, -currentY, replacee);
                replacee.classList.add("colorShift");
                setTranslate(currentX, currentY, currentItem);
                fakeGrabbedItem.style.visibility = "hidden";
              }
              if (canComplete) {
                canMove = canComplete;
              }
            } else {
              if (currentItem.dataset.itemtype === "credits") {
                displayPlacementMessage(
                  "You cannot place credits here",
                  mousePos[0] + 10,
                  mousePos[1] + 10,
                  "ERROR"
                );
                displayText = true;
              } else if (
                slot.parentNode.dataset.slottype !==
                  currentItem.dataset.itemtype &&
                typeof slot.parentNode.dataset.slottype !== "undefined"
              ) {
                displayPlacementMessage(
                  "You cannot switch these items",
                  mousePos[0] + 10,
                  mousePos[1] + 10,
                  "ERROR"
                );
                displayText = true;
              } else if (!unblockedSlot(itemType) || !unblockedSlot(slotType)) {
                displayPlacementMessage(
                  "Something is blocking this move",
                  mousePos[0] + 10,
                  mousePos[1] + 10,
                  "ERROR"
                );
                displayText = true;
              } else {
                displayPlacementMessage(
                  "You cannot place this item here",
                  mousePos[0] + 10,
                  mousePos[1] + 10,
                  "ERROR"
                );
                displayText = true;
              }
            }
          }
        } else if (slot.classList.contains("blockedSlot")) {
          if (
            slot.dataset.slottype === currentItem.dataset.itemtype ||
            typeof slot.dataset.slottype === "undefined"
          ) {
            displayPlacementMessage(
              "Something is blocking this slot",
              mousePos[0] + 10,
              mousePos[1] + 10,
              "ERROR"
            );
            displayText = true;
          } else {
            displayPlacementMessage(
              "You cannot place this item here",
              mousePos[0] + 10,
              mousePos[1] + 10,
              "ERROR"
            );
            displayText = true;
          }
        } else if (slot.classList.contains("lockedSlot")) {
          if (
            slot.dataset.slottype === currentItem.dataset.itemtype ||
            typeof slot.dataset.slottype === "undefined"
          ) {
            switch (inventoryType) {
              case "clan_storage":
                displayPlacementMessage(
                  "You cannot access this slot",
                  mousePos[0] + 10,
                  mousePos[1] + 10,
                  "ERROR"
                );
                break;
              default:
                displayPlacementMessage(
                  "This slot is inaccessible",
                  mousePos[0] + 10,
                  mousePos[1] + 10,
                  "ERROR"
                );
                break;
            }
            displayText = true;
          } else {
            displayPlacementMessage(
              "You cannot place this item here",
              mousePos[0] + 10,
              mousePos[1] + 10,
              "ERROR"
            );
            displayText = true;
          }
        } else if (
          slot.classList.contains("fakeSlot") ||
          ((slot.classList.contains("fakeItem") ||
            slot.classList.contains("profitList")) &&
            slot.parentNode.classList.contains("fakeSlot")) ||
          ((slot.parentNode.classList.contains("fakeItem") ||
            slot.parentNode.classList.contains("profitList")) &&
            slot.parentNode.parentNode.classList.contains("fakeSlot"))
        ) {
          // do fake things
          if (
            (slot.classList.contains("fakeItem") ||
              slot.classList.contains("profitList")) &&
            slot.parentNode.classList.contains("fakeSlot")
          ) {
            slot = slot.parentNode;
          } else if (
            (slot.parentNode.classList.contains("fakeItem") ||
              slot.parentNode.classList.contains("profitList")) &&
            slot.parentNode.parentNode.classList.contains("fakeSlot")
          ) {
            slot = slot.parentNode.parentNode;
          }
          hoverItem = slot;
          hovering = true;
          switch (slot.dataset.action) {
            case "discard":
              var item_slot = parseInt(currentItem.parentNode.dataset.slot);

              if (
                currentItem.parentNode.parentNode.parentNode.id ===
                "backpackdisplay"
              ) {
                item_slot += 1050;
              }
              if (currentItem.parentNode.dataset.slottype === "implant") {
                item_slot += 1000;
              }
              item_slot += "";

              if (!lockedSlots.includes(item_slot)) {
                var canContinue = true;
                if (item_slot === "35") {
                  var totalCurrentBackpackSlots = parseInt(
                    globalData[itemType]["slots"]
                  );
                  if (currentItem.dataset.type.indexOf("_stats") >= 0) {
                    totalCurrentBackpackSlots += parseInt(
                      currentItem.dataset.type.charAt(
                        currentItem.dataset.type.indexOf("_stats") + 6
                      )
                    );
                  }
                  for (var i = 1; i <= totalCurrentBackpackSlots; i++) {
                    if (
                      typeof userVars["DFSTATS_df_backpack" + i + "_type"] !==
                        "undefined" &&
                      userVars["DFSTATS_df_backpack" + i + "_type"] !== ""
                    ) {
                      displayPlacementMessage(
                        "Backpack contains items",
                        slot.getBoundingClientRect().left - 95,
                        mousePos[1] + 20,
                        "ERROR"
                      );
                      displayText = true;
                      canContinue = false;
                      break;
                    }
                  }
                }
                if (canContinue) {
                  displayPlacementMessage(
                    "Discard this",
                    slot.getBoundingClientRect().left - 95,
                    mousePos[1] + 20,
                    "ACTION"
                  );
                  displayText = true;
                  hoverItem.classList.add("hover");
                  canComplete = true;
                }
              } else {
                displayPlacementMessage(
                  "Locked slot",
                  slot.getBoundingClientRect().left - 95,
                  mousePos[1] + 20,
                  "ERROR"
                );
                displayText = true;
              }
              break;
            case "simpleStore":
              if (inventoryType === "clan_storage") {
                if (
                  (typeof globalData[itemType]["no_transfer"] !== "undefined" &&
                    globalData[itemType]["no_transfer"] === true) ||
                  currentItem.dataset.type.indexOf("_nt") >= 0
                ) {
                  displayPlacementMessage(
                    "This item is non-transferable",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                } else {
                  if (findFirstEmptyClanStorageSlot()) {
                    displayPlacementMessage(
                      "Store item",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ACTION"
                    );
                    canComplete = true;
                  } else {
                    displayPlacementMessage(
                      "Storage full",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                  }
                }
              } else {
                if (findFirstEmptyStorageSlot()) {
                  displayPlacementMessage(
                    "Store item",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ACTION"
                  );
                  canComplete = true;
                } else {
                  displayPlacementMessage(
                    "Storage full",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                }
              }
              displayText = true;
              break;
            case "tradeitem":
            case "sellitem":
              if (
                parseInt(tradeListSize) >=
                parseInt(userVars["DFSTATS_df_invslots"])
              ) {
                displayPlacementMessage(
                  "Your selling list is full",
                  mousePos[0] - 40,
                  mousePos[1] + 10,
                  "ERROR"
                );
                displayText = true;
              } else if (
                (typeof globalData[itemType]["no_transfer"] !== "undefined" &&
                  globalData[itemType]["no_transfer"] === true) ||
                currentItem.dataset.type.indexOf("_nt") >= 0
              ) {
                displayPlacementMessage(
                  "This item is non-transferable",
                  mousePos[0] - 40,
                  mousePos[1] + 10,
                  "ERROR"
                );
                displayText = true;
              } else if (currentItem.parentNode.classList.contains("locked")) {
                displayPlacementMessage(
                  "Item is in a locked slot",
                  mousePos[0] - 40,
                  mousePos[1] + 10,
                  "ERROR"
                );
                displayText = true;
              } else {
                displayPlacementMessage(
                  "Sell this",
                  mousePos[0] - 40,
                  mousePos[1] + 10,
                  "ACTION"
                );
                displayText = true;
                canComplete = true;
              }
              break;
            case "buyservice":
              var allowedToUse = false;
              if (
                currentItem.dataset.itemtype === "armour" &&
                slot.dataset.profession === "Engineer"
              ) {
                if (
                  parseInt(slot.dataset.level) <
                  parseInt(globalData[itemType]["shop_level"]) - 5
                ) {
                  displayPlacementMessage(
                    slot.parentNode.querySelector(".seller").textContent +
                      " is too low level to repair this",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                } else if (
                  parseInt(currentItem.dataset.quantity) >=
                  parseInt(globalData[itemType]["hp"])
                ) {
                  displayPlacementMessage(
                    "This doesn't need to be repaired",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                } else {
                  allowedToUse = true;
                }
              } else if (globalData[itemType]) {
                if (
                  (globalData[itemType]["needdoctor"] === true &&
                    slot.dataset.profession === "Doctor") ||
                  (globalData[itemType]["needcook"] === true &&
                    slot.dataset.profession === "Chef")
                ) {
                  var serviceType = "";
                  var cannotBeServiced = false;
                  switch (slot.dataset.profession) {
                    case "Doctor":
                      if (
                        parseInt(userVars["DFSTATS_df_hpcurrent"]) >=
                        parseInt(userVars["DFSTATS_df_hpmax"])
                      ) {
                        cannotBeServiced = "You are not wounded";
                      }
                      serviceType = "administer";
                      break;
                    case "Chef":
                      serviceType = "cook";
                      break;
                  }
                  if (slot.dataset.level < globalData[itemType]["level"] - 5) {
                    displayPlacementMessage(
                      slot.parentNode.querySelector(".seller").textContent +
                        " is too low level to " +
                        serviceType +
                        " this",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                  } else if (cannotBeServiced) {
                    displayPlacementMessage(
                      cannotBeServiced,
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                  } else {
                    allowedToUse = true;
                  }
                }
              }
              if (allowedToUse) {
                if (slot.dataset.disabled) {
                  displayPlacementMessage(
                    "Cannot afford this service",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                } else if (currentItem.dataset.type.indexOf("_cooked") >= 0) {
                  displayPlacementMessage(
                    "This is already cooked",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                } else {
                  displayPlacementMessage(
                    "Buy service",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ACTION"
                  );
                  canComplete = true;
                }
              }
              displayText = true;
              break;
            case "sendItemPrivate":
              if (
                parseInt(tradeListSize) >=
                parseInt(userVars["DFSTATS_df_invslots"])
              ) {
                displayPlacementMessage(
                  "Your selling list is full",
                  mousePos[0] - 40,
                  mousePos[1] + 10,
                  "ERROR"
                );
                displayText = true;
              } else if (
                (typeof globalData[itemType]["no_transfer"] !== "undefined" &&
                  globalData[itemType]["no_transfer"] === true) ||
                currentItem.dataset.type.indexOf("_nt") >= 0
              ) {
                displayPlacementMessage(
                  "This item is non-transferable",
                  mousePos[0] - 40,
                  mousePos[1] + 10,
                  "ERROR"
                );
                displayText = true;
              } else if (currentItem.parentNode.classList.contains("locked")) {
                displayPlacementMessage(
                  "Item is in a locked slot",
                  mousePos[0] - 40,
                  mousePos[1] + 10,
                  "ERROR"
                );
                displayText = true;
              } else {
                displayPlacementMessage(
                  "Sell this",
                  mousePos[0] - 40,
                  mousePos[1] + 10,
                  "ACTION"
                );
                displayText = true;
                canComplete = true;
              }
              break;
            case "scrap":
              var value = scrapValue(
                currentItem.dataset.type,
                currentItem.dataset.quantity
              );
              if (value) {
                var item_slot = parseInt(currentItem.parentNode.dataset.slot);

                if (
                  currentItem.parentNode.parentNode.parentNode.id ===
                  "backpackdisplay"
                ) {
                  item_slot += 1050;
                }
                if (currentItem.parentNode.dataset.slottype === "implant") {
                  item_slot += 1000;
                }
                item_slot += "";

                if (!lockedSlots.includes(item_slot)) {
                  var canContinue = true;
                  if (item_slot === "35") {
                    var totalCurrentBackpackSlots = parseInt(
                      globalData[itemType]["slots"]
                    );
                    if (currentItem.dataset.type.indexOf("_stats") >= 0) {
                      totalCurrentBackpackSlots += parseInt(
                        currentItem.dataset.type.charAt(
                          currentItem.dataset.type.indexOf("_stats") + 6
                        )
                      );
                    }
                    for (var i = 1; i <= totalCurrentBackpackSlots; i++) {
                      if (
                        typeof userVars["DFSTATS_df_backpack" + i + "_type"] !==
                          "undefined" &&
                        userVars["DFSTATS_df_backpack" + i + "_type"] !== ""
                      ) {
                        displayPlacementMessage(
                          "Backpack contains items",
                          mousePos[0] - 40,
                          mousePos[1] + 10,
                          "ERROR"
                        );
                        displayText = true;
                        canContinue = false;
                        break;
                      }
                    }
                  }
                  if (canContinue) {
                    displayPlacementMessage(
                      "Value: $" + nf.format(value),
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ACTION"
                    );
                    displayText = true;
                    hoverItem.classList.add("hover");
                    canComplete = true;
                  }
                } else {
                  displayPlacementMessage(
                    "Locked slot",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                }
              }
              break;
            case "enhance":
              var value = enhanceValue(currentItem.dataset.type);
              if (value) {
                if (
                  (globalData[itemType]["itemtype"] === "weapon" &&
                    currentItem.dataset.type.indexOf("_stats888") >= 0) ||
                  (globalData[itemType]["itemtype"] === "armour" &&
                    currentItem.dataset.type.indexOf("_stats2424") >= 0) ||
                  (globalData[itemType]["itemtype"] === "backpack" &&
                    currentItem.dataset.type.indexOf("_stats3") >= 0)
                ) {
                  displayPlacementMessage(
                    "This item already has perfect stats",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                } else {
                  var canContinue = true;
                  if (
                    currentItem.parentNode.dataset.slot === "35" &&
                    currentItem.dataset.type.indexOf("_stats") >= 0
                  ) {
                    var totalCurrentBackpackSlots = parseInt(
                      globalData[itemType]["slots"]
                    );
                    totalCurrentBackpackSlots += parseInt(
                      currentItem.dataset.type.charAt(
                        currentItem.dataset.type.indexOf("_stats") + 6
                      )
                    );
                    var lastUsedSlot = 0;
                    var totalUsedSlots = 0;
                    for (var i = 1; i <= totalCurrentBackpackSlots; i++) {
                      if (
                        typeof userVars["DFSTATS_df_backpack" + i + "_type"] !==
                          "undefined" &&
                        userVars["DFSTATS_df_backpack" + i + "_type"] !== ""
                      ) {
                        lastUsedSlot = i;
                        totalUsedSlots++;
                      }
                    }
                    if (
                      lastUsedSlot >
                      parseInt(globalData[itemType]["slots"]) + 1
                    ) {
                      if (
                        totalUsedSlots >
                        parseInt(globalData[itemType]["slots"]) + 1
                      ) {
                        displayPlacementMessage(
                          "You can potentially get a smaller backpack, please remove lower items",
                          mousePos[0] - 40,
                          mousePos[1] + 10,
                          "ERROR"
                        );
                      } else {
                        displayPlacementMessage(
                          "You can potentially get a smaller backpack, please move items to higher slots",
                          mousePos[0] - 40,
                          mousePos[1] + 10,
                          "ERROR"
                        );
                      }
                      canContinue = false;
                      displayText = true;
                    }
                  }
                  if (canContinue) {
                    if (parseInt(userVars["DFSTATS_df_cash"]) >= value) {
                      displayPlacementMessage(
                        "Cost: $" + nf.format(value),
                        mousePos[0] - 40,
                        mousePos[1] + 10,
                        "ACTION"
                      );
                      displayText = true;
                      hoverItem.classList.add("hover");
                      canComplete = true;
                    } else {
                      displayPlacementMessage(
                        "You need $" + nf.format(value) + " to enhance this",
                        mousePos[0] - 40,
                        mousePos[1] + 10,
                        "ERROR"
                      );
                      displayText = true;
                    }
                  }
                }
              } else {
                displayPlacementMessage(
                  "This item cannot be enhanced",
                  mousePos[0] - 40,
                  mousePos[1] + 10,
                  "ERROR"
                );
                displayText = true;
              }
              break;
            case "reinforce":
              if (globalData[itemType]["itemtype"] === "armour") {
                if (currentItem.dataset.type.indexOf("_re9") >= 0) {
                  displayPlacementMessage(
                    "This item already has max reinforcement",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                } else {
                  if (findInInventory("raremetalscrap") !== false) {
                    displayPlacementMessage(
                      "Cost: 1 Rare Metal Scrap",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ACTION"
                    );
                    displayText = true;
                    hoverItem.classList.add("hover");
                    canComplete = true;
                  } else {
                    displayPlacementMessage(
                      "You need 1 Rare Metal Scrap to reinforce this",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                    displayText = true;
                  }
                }
              } else {
                displayPlacementMessage(
                  "This item cannot be reinforced",
                  mousePos[0] - 40,
                  mousePos[1] + 10,
                  "ERROR"
                );
                displayText = true;
              }
              break;
            case "adye":
              var value = dyeValue(currentItem.dataset.type);
              if (value) {
                if (userVars["DFSTATS_df_cash"] >= value) {
                  displayPlacementMessage(
                    "Cost: $" + nf.format(value),
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ACTION"
                  );
                  displayText = true;
                  hoverItem.classList.add("hover");
                  canComplete = true;
                } else {
                  displayPlacementMessage(
                    "You need $" + nf.format(value) + " to dye this",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                }
              } else {
                displayPlacementMessage(
                  "This item cannot be dyed",
                  mousePos[0] - 40,
                  mousePos[1] + 10,
                  "ERROR"
                );
                displayText = true;
              }
              break;
            case "rename":
              var value = false;
              switch (globalData[itemType]["itemtype"]) {
                case "weapon":
                case "backpack":
                case "armour":
                  value = true;
                  break;
              }
              if (value) {
                if (userVars["yard_use_credits"] === "1") {
                  if (userVars["DFSTATS_df_credits"] >= 500) {
                    displayPlacementMessage(
                      "Cost: " + nf.format(500) + " Credits",
                      slot.getBoundingClientRect().left - slot.offsetWidth,
                      mousePos[1] + 10,
                      "ACTION"
                    );
                    displayText = true;
                    hoverItem.classList.add("hover");
                    canComplete = true;
                  } else {
                    displayPlacementMessage(
                      "You need " + nf.format(500) + " Credits to rename this",
                      slot.getBoundingClientRect().left - slot.offsetWidth,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                    displayText = true;
                  }
                } else {
                  if (userVars["DFSTATS_df_cash"] >= 20e6) {
                    displayPlacementMessage(
                      "Cost: $" + nf.format(20e6),
                      slot.getBoundingClientRect().left - slot.offsetWidth,
                      mousePos[1] + 10,
                      "ACTION"
                    );
                    displayText = true;
                    hoverItem.classList.add("hover");
                    canComplete = true;
                  } else {
                    displayPlacementMessage(
                      "You need $" + nf.format(20e6) + " to rename this",
                      slot.getBoundingClientRect().left - slot.offsetWidth,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                    displayText = true;
                  }
                }
              } else {
                displayPlacementMessage(
                  "This item cannot be renamed",
                  slot.getBoundingClientRect().left - slot.offsetWidth,
                  mousePos[1] + 10,
                  "ERROR"
                );
                displayText = true;
              }
              break;
            case "removerename":
              var value = false;
              switch (globalData[itemType]["itemtype"]) {
                case "weapon":
                case "armour":
                case "backpack":
                  value = true;
                  break;
              }
              if (value) {
                if (userVars["yard_use_credits"] === "1") {
                  if (userVars["DFSTATS_df_credits"] >= 50) {
                    displayPlacementMessage(
                      "Cost: " + nf.format(50) + " Credits",
                      slot.getBoundingClientRect().left - slot.offsetWidth,
                      mousePos[1] + 10,
                      "ACTION"
                    );
                    displayText = true;
                    hoverItem.classList.add("hover");
                    canComplete = true;
                  } else {
                    displayPlacementMessage(
                      "You need " +
                        nf.format(50) +
                        " Credits to remove the rename on this",
                      slot.getBoundingClientRect().left - slot.offsetWidth,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                    displayText = true;
                  }
                } else {
                  if (userVars["DFSTATS_df_cash"] >= 2.5e6) {
                    displayPlacementMessage(
                      "Cost: $" + nf.format(2.5e6),
                      slot.getBoundingClientRect().left - slot.offsetWidth,
                      mousePos[1] + 10,
                      "ACTION"
                    );
                    displayText = true;
                    hoverItem.classList.add("hover");
                    canComplete = true;
                  } else {
                    displayPlacementMessage(
                      "You need $" +
                        nf.format(2.5e6) +
                        " to remove the rename on this",
                      slot.getBoundingClientRect().left - slot.offsetWidth,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                    displayText = true;
                  }
                }
              } else {
                displayPlacementMessage(
                  "This item's name cannot be removed",
                  slot.getBoundingClientRect().left - slot.offsetWidth,
                  mousePos[1] + 10,
                  "ERROR"
                );
                displayText = true;
              }
              break;
            case "dismantle":
              if (
                currentItem.parentNode.parentNode.parentNode.id === "inventory"
              )
                if (
                  globalData[itemType]["dismantle"] &&
                  globalData[itemType]["dismantle"].length > 0
                ) {
                  var item_slot = parseInt(currentItem.parentNode.dataset.slot);

                  item_slot += "";

                  if (lockedSlots.includes(item_slot)) {
                    displayPlacementMessage(
                      "Locked slot",
                      slot.getBoundingClientRect().left - 95,
                      mousePos[1] + 20,
                      "ERROR"
                    );
                    displayText = true;
                  } else {
                    displayPlacementMessage(
                      "Dismantle: " +
                        generateTextItemList(
                          globalData[itemType]["dismantle"].split(",")
                        ),
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ACTION"
                    );
                    displayText = true;
                    hoverItem.classList.add("hover");
                    canComplete = true;
                  }
                }
              break;
            case "godcraft":
              var value = false;
              switch (globalData[itemType]["itemtype"]) {
                case "weapon":
                  if (userVars["yard_use_credits"] === "1") {
                    value = 100;
                  } else {
                    value = 10e6;
                  }
                  break;
                case "armour":
                  if (userVars["yard_use_credits"] === "1") {
                    value = 200;
                  } else {
                    value = 20e6;
                  }
                  break;
                case "backpack":
                  if (userVars["yard_use_credits"] === "1") {
                    value = 50;
                  } else {
                    value = 5e6;
                  }
                  break;
              }
              if (value) {
                if (
                  (globalData[itemType]["itemtype"] === "weapon" &&
                    currentItem.dataset.type.indexOf("_stats888") >= 0) ||
                  (globalData[itemType]["itemtype"] === "armour" &&
                    currentItem.dataset.type.indexOf("_stats2424") >= 0) ||
                  (globalData[itemType]["itemtype"] === "backpack" &&
                    currentItem.dataset.type.indexOf("_stats3") >= 0)
                ) {
                  displayPlacementMessage(
                    "This already has perfect stats!",
                    slot.getBoundingClientRect().left - 20,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                } else {
                  if (userVars["yard_use_credits"] === "1") {
                    if (userVars["DFSTATS_df_credits"] >= value) {
                      displayPlacementMessage(
                        "Cost: " + nf.format(value) + " Credits",
                        slot.getBoundingClientRect().left - 20,
                        mousePos[1] + 10,
                        "ACTION"
                      );
                      displayText = true;
                      hoverItem.classList.add("hover");
                      canComplete = true;
                    } else {
                      displayPlacementMessage(
                        "You need " +
                          nf.format(value) +
                          " Credits to godcraft this",
                        slot.getBoundingClientRect().left - 20,
                        mousePos[1] + 10,
                        "ERROR"
                      );
                      displayText = true;
                    }
                  } else {
                    if (userVars["DFSTATS_df_cash"] >= value) {
                      displayPlacementMessage(
                        "Cost: $" + nf.format(value),
                        slot.getBoundingClientRect().left - 20,
                        mousePos[1] + 10,
                        "ACTION"
                      );
                      displayText = true;
                      hoverItem.classList.add("hover");
                      canComplete = true;
                    } else {
                      displayPlacementMessage(
                        "You need $" + nf.format(value) + " to godcraft this",
                        slot.getBoundingClientRect().left - 20,
                        mousePos[1] + 10,
                        "ERROR"
                      );
                      displayText = true;
                    }
                  }
                }
              } else {
                displayPlacementMessage(
                  "This item cannot be enhanced",
                  slot.getBoundingClientRect().left - 20,
                  mousePos[1] + 10,
                  "ERROR"
                );
                displayText = true;
              }
              break;
            case "mdye":
              var value = false;
              switch (globalData[itemType]["itemtype"]) {
                case "weapon":
                case "armour":
                  if (
                    typeof globalData[itemType]["othercolours"] !==
                      "undefined" &&
                    globalData[itemType]["othercolours"] !== ""
                  ) {
                    if (userVars["yard_use_credits"] === "1") {
                      value = 250;
                    } else {
                      value = 1e6;
                    }
                  }
                  break;
                case "item":
                  if (
                    typeof globalData[itemType]["othercolours"] !==
                      "undefined" &&
                    globalData[itemType]["othercolours"] !== "" &&
                    globalData[itemType]["clothingtype"] &&
                    globalData[itemType]["clothtype"] !== ""
                  ) {
                    if (userVars["yard_use_credits"] === "1") {
                      value = 100;
                    } else {
                      value = 1e6;
                    }
                  }
                  break;
              }
              if (value) {
                if (userVars["yard_use_credits"] === "1") {
                  if (userVars["DFSTATS_df_credits"] >= value) {
                    displayPlacementMessage(
                      "Cost: " + nf.format(value) + " Credits",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ACTION"
                    );
                    displayText = true;
                    hoverItem.classList.add("hover");
                    canComplete = true;
                  } else {
                    displayPlacementMessage(
                      "You need " + nf.format(value) + " Credits to dye this",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                    displayText = true;
                  }
                } else {
                  if (userVars["DFSTATS_df_cash"] >= value) {
                    displayPlacementMessage(
                      "Cost: $" + nf.format(value),
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ACTION"
                    );
                    displayText = true;
                    hoverItem.classList.add("hover");
                    canComplete = true;
                  } else {
                    displayPlacementMessage(
                      "You need $" + nf.format(value) + " to dye this",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                    displayText = true;
                  }
                }
              } else {
                displayPlacementMessage(
                  "This item cannot be dyed",
                  mousePos[0] - 40,
                  mousePos[1] + 10,
                  "ERROR"
                );
                displayText = true;
              }
              break;
            case "giveToChar":
              var value = true;
              if (globalData[itemType]["healthrestore"] > 0) {
                if (
                  parseInt(userVars["DFSTATS_df_hpcurrent"]) >=
                    parseInt(userVars["DFSTATS_df_hpmax"]) &&
                  !(
                    globalData[itemType]["boostdamagehours"] > 0 ||
                    globalData[itemType]["boostexphours"] > 0 ||
                    globalData[itemType]["boostspeedhours"] > 0 ||
                    globalData[itemType]["boostdamagehours_ex"] > 0 ||
                    globalData[itemType]["boostexphours_ex"] > 0 ||
                    globalData[itemType]["boostspeedhours_ex"] > 0
                  )
                ) {
                  displayPlacementMessage(
                    "You are not wounded",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                  value = false;
                } else {
                  displayPlacementMessage(
                    "Use this",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ACTION"
                  );
                }
              } else if (
                typeof globalData[itemType]["armourrestore"] !== "undefined" &&
                globalData[itemType]["armourrestore"] > 0
              ) {
                let minDamageCanRestore =
                  parseInt(globalData[itemType]["min_repair"] ?? 0) / 100;
                let maxLevelToRestore =
                  parseInt(globalData[itemType]["max_repair"] ?? 100) / 100;
                if (
                  parseInt(userVars["DFSTATS_df_armourhp"]) >=
                  parseInt(userVars["DFSTATS_df_armourhpmax"])
                ) {
                  displayPlacementMessage(
                    "Your armour isn't broken",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                  value = false;
                } else if (
                  parseInt(userVars["DFSTATS_df_armourhp"]) <
                  Math.floor(
                    parseInt(userVars["DFSTATS_df_armourhpmax"]) *
                      minDamageCanRestore
                  )
                ) {
                  displayPlacementMessage(
                    "Your armour is too broken",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                  value = false;
                } else if (
                  parseInt(userVars["DFSTATS_df_armourhp"]) >=
                  Math.ceil(
                    parseInt(userVars["DFSTATS_df_armourhpmax"]) *
                      maxLevelToRestore
                  )
                ) {
                  displayPlacementMessage(
                    "This kit cannot fix your armour more",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                  value = false;
                } else {
                  displayPlacementMessage(
                    "Repair armour",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ACTION"
                  );
                }
              } else if (parseInt(globalData[itemType]["foodrestore"]) > 0) {
                if (
                  parseInt(userVars["DFSTATS_df_hungerhp"]) >=
                    50 + Math.floor(userVars["DFSTATS_df_survival"] * 1.5) &&
                  !(
                    globalData[itemType]["boostdamagehours"] > 0 ||
                    globalData[itemType]["boostexphours"] > 0 ||
                    globalData[itemType]["boostspeedhours"] > 0 ||
                    globalData[itemType]["boostdamagehours_ex"] > 0 ||
                    globalData[itemType]["boostexphours_ex"] > 0 ||
                    globalData[itemType]["boostspeedhours_ex"] > 0
                  )
                ) {
                  displayPlacementMessage(
                    "You are not hungry",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                  value = false;
                } else {
                  displayPlacementMessage(
                    "Consume this",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ACTION"
                  );
                }
              } else if (globalData[itemType]["canread"] === true) {
                if (window.location.pathname.indexOf("/DF3D/") === -1) {
                  displayPlacementMessage(
                    "Read this",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ACTION"
                  );
                } else {
                  displayPlacementMessage(
                    "It isn't safe to read this here",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                }
              } else if (
                globalData[itemType]["boostdamagehours"] > 0 ||
                globalData[itemType]["boostexphours"] > 0 ||
                globalData[itemType]["boostspeedhours"] > 0 ||
                globalData[itemType]["boostdamagehours_ex"] > 0 ||
                globalData[itemType]["boostexphours_ex"] > 0 ||
                globalData[itemType]["boostspeedhours_ex"] > 0
              ) {
                for (var i = 0; i < currentItemVariables.length; i++) {
                  if (currentItemVariables[i].indexOf("ex") == 0) {
                    if (
                      parseInt(userVars["pagetime"]) >
                      parseInt(currentItemVariables[i].substr(2))
                    ) {
                      displayPlacementMessage(
                        "You cannot apply expired boosts",
                        mousePos[0] - 40,
                        mousePos[1] + 10,
                        "ERROR"
                      );
                      displayText = true;
                      value = false;
                      break;
                    }
                  }
                }
                if (value) {
                  displayPlacementMessage(
                    "Apply this",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ACTION"
                  );
                }
              } else if (
                (typeof globalData[itemType]["opencontents"] !== "undefined" &&
                  globalData[itemType]["opencontents"].length > 0) ||
                (typeof globalData[itemType]["moneygift"] !== "undefined" &&
                  globalData[itemType]["moneygift"] === true)
              ) {
                displayPlacementMessage(
                  "Open this",
                  mousePos[0] - 40,
                  mousePos[1] + 10,
                  "ACTION"
                );
              } else if (
                typeof globalData[itemType]["rewardcontents"] !== "undefined" &&
                globalData[itemType]["rewardcontents"].length > 0
              ) {
                if (typeof currentItemII.ex !== "undefined") {
                  if (
                    parseInt(userVars["pagetime"]) > parseInt(currentItemII.ex)
                  ) {
                    if (
                      typeof globalData[itemType]["expire_use"] !==
                        "undefined" &&
                      globalData[itemType]["expire_use"]
                    ) {
                      displayPlacementMessage(
                        "Claim this",
                        mousePos[0] - 40,
                        mousePos[1] + 10,
                        "ACTION"
                      );
                    } else {
                      displayPlacementMessage(
                        "Expired",
                        mousePos[0] - 40,
                        mousePos[1] + 10,
                        "ERROR"
                      );
                      value = false;
                      displayText = true;
                    }
                  } else {
                    if (
                      typeof globalData[itemType]["expire_use"] !==
                        "undefined" &&
                      globalData[itemType]["expire_use"]
                    ) {
                      displayPlacementMessage(
                        "Cannot be claimed yet",
                        mousePos[0] - 40,
                        mousePos[1] + 10,
                        "ERROR"
                      );
                      value = false;
                      displayText = true;
                    } else {
                      displayPlacementMessage(
                        "Claim this",
                        mousePos[0] - 40,
                        mousePos[1] + 10,
                        "ACTION"
                      );
                    }
                  }
                } else {
                  displayPlacementMessage(
                    "Claim this",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ACTION"
                  );
                }
              } else if (
                typeof globalData[itemType]["gm_days"] !== "undefined" &&
                globalData[itemType]["gm_days"] !== "0"
              ) {
                displayPlacementMessage(
                  "Use this",
                  mousePos[0] - 40,
                  mousePos[1] + 10,
                  "ACTION"
                );
              } else if (
                typeof globalData[itemType]["implant_stats"] !== "undefined" &&
                globalData[itemType]["implant_stats"].length > 0
              ) {
                displayPlacementMessage(
                  "Modify this",
                  mousePos[0] - 40,
                  mousePos[1] + 10,
                  "ACTION"
                );
              } else if (
                typeof globalData[itemType]["implant_mods"] !== "undefined" &&
                globalData[itemType]["implant_mods"].length > 0
              ) {
                if (
                  userVars["DFSTATS_df_cash"] >=
                  (globalData[itemType]["implant_reroll_cost"] ?? 1e6)
                ) {
                  displayPlacementMessage(
                    "Reroll this",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ACTION"
                  );
                } else {
                  displayPlacementMessage(
                    "You need $" +
                      nf.format(
                        globalData[itemType]["implant_reroll_cost"] ?? 1e6
                      ) +
                      " to reroll this",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  value = false;
                  displayText = true;
                }
              } else {
                value = false;
              }
              if (value) {
                displayText = true;
                canComplete = true;
              }
              break;
            case "newadminister":
              if (parseInt(globalData[itemType]["healthrestore"]) > 0) {
                if (
                  parseInt(userVars["DFSTATS_df_hpcurrent"]) <
                  parseInt(userVars["DFSTATS_df_hpmax"]) * 0.25
                ) {
                  displayPlacementMessage(
                    "You are too badly wounded to do that",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                } else if (
                  parseInt(userVars["DFSTATS_df_hungerhp"]) <
                  Math.floor(
                    (50 + Math.floor(userVars["DFSTATS_df_survival"] * 1.5)) *
                      0.25
                  )
                ) {
                  displayPlacementMessage(
                    "You are too hungry to do that",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                } else {
                  if (
                    parseInt(userVars["DFSTATS_df_hpcurrent"]) >=
                      parseInt(userVars["DFSTATS_df_hpmax"]) &&
                    !(
                      globalData[itemType]["boostdamagehours"] > 0 ||
                      globalData[itemType]["boostexphours"] > 0 ||
                      globalData[itemType]["boostspeedhours"] > 0 ||
                      globalData[itemType]["boostdamagehours_ex"] > 0 ||
                      globalData[itemType]["boostexphours_ex"] > 0 ||
                      globalData[itemType]["boostspeedhours_ex"] > 0
                    )
                  ) {
                    displayPlacementMessage(
                      "You are not wounded",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                    displayText = true;
                  } else if (globalData[itemType]["needdoctor"] === false) {
                    displayPlacementMessage(
                      "This doesn't need to be administered",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                    displayText = true;
                  } else if (
                    parseInt(userVars["DFSTATS_df_level"]) <
                    parseInt(globalData[itemType]["level"]) - 5
                  ) {
                    displayPlacementMessage(
                      "You're too low level to administer that",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                    displayText = true;
                  } else {
                    displayPlacementMessage(
                      "Administer this",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ACTION"
                    );
                    displayText = true;
                    canComplete = true;
                  }
                }
              }
              break;
            case "newcook":
              if (parseInt(globalData[itemType]["foodrestore"]) > 0) {
                if (
                  parseInt(userVars["DFSTATS_df_hpcurrent"]) <
                  parseInt(userVars["DFSTATS_df_hpmax"]) * 0.25
                ) {
                  displayPlacementMessage(
                    "You are too badly wounded to do that",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                } else if (
                  parseInt(userVars["DFSTATS_df_hungerhp"]) <
                  Math.floor(
                    (50 + Math.floor(userVars["DFSTATS_df_survival"] * 1.5)) *
                      0.25
                  )
                ) {
                  displayPlacementMessage(
                    "You are too hungry to do that",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                } else {
                  if (
                    globalData[itemType]["needcook"] === false ||
                    currentItem.dataset.type.indexOf("_cooked") >= 0
                  ) {
                    displayPlacementMessage(
                      "This doesn't need to be cooked",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                    displayText = true;
                  } else if (
                    parseInt(userVars["DFSTATS_df_level"]) <
                    parseInt(globalData[itemType]["level"]) - 5
                  ) {
                    displayPlacementMessage(
                      "You're too low level to cook that",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                    displayText = true;
                  } else {
                    displayPlacementMessage(
                      "Cook this",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ACTION"
                    );
                    displayText = true;
                    canComplete = true;
                  }
                }
              }
              break;
            case "newrepair":
              if (globalData[itemType]["itemtype"] === "armour") {
                if (
                  parseInt(userVars["DFSTATS_df_hpcurrent"]) <
                  parseInt(userVars["DFSTATS_df_hpmax"]) * 0.25
                ) {
                  displayPlacementMessage(
                    "You are too badly wounded to do that",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                } else if (
                  parseInt(userVars["DFSTATS_df_hungerhp"]) <
                  Math.floor(
                    (50 + Math.floor(userVars["DFSTATS_df_survival"] * 1.5)) *
                      0.25
                  )
                ) {
                  displayPlacementMessage(
                    "You are too hungry to do that",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                } else {
                  if (
                    parseInt(currentItem.dataset.quantity) >=
                    parseInt(globalData[itemType]["hp"])
                  ) {
                    displayPlacementMessage(
                      "This doesn't need to be repaired",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                    displayText = true;
                  } else if (
                    parseInt(userVars["DFSTATS_df_level"]) <
                    parseInt(globalData[itemType]["shop_level"]) - 5
                  ) {
                    displayPlacementMessage(
                      "You're too low level to repair that",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                    displayText = true;
                  } else {
                    displayPlacementMessage(
                      "Repair this",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ACTION"
                    );
                    displayText = true;
                    canComplete = true;
                  }
                }
              }
              break;
            case "ironmanaction":
              if (globalData[itemType]["itemtype"] === "armour") {
                if (
                  parseInt(userVars["DFSTATS_df_hpcurrent"]) <
                  parseInt(userVars["DFSTATS_df_hpmax"]) * 0.25
                ) {
                  displayPlacementMessage(
                    "You are too badly wounded to do that",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                } else if (
                  parseInt(userVars["DFSTATS_df_hungerhp"]) <
                  Math.floor(
                    (50 + Math.floor(userVars["DFSTATS_df_survival"] * 1.5)) *
                      0.25
                  )
                ) {
                  displayPlacementMessage(
                    "You are too hungry to do that",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                  displayText = true;
                } else {
                  if (
                    parseInt(currentItem.dataset.quantity) >=
                    parseInt(globalData[itemType]["hp"])
                  ) {
                    displayPlacementMessage(
                      "This doesn't need to be repaired",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                    displayText = true;
                  } else if (
                    parseInt(userVars["DFSTATS_df_level"]) <
                    parseInt(globalData[itemType]["shop_level"]) - 10
                  ) {
                    displayPlacementMessage(
                      "You're too low level to repair that",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                    displayText = true;
                  } else {
                    displayPlacementMessage(
                      "Repair this",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ACTION"
                    );
                    displayText = true;
                    canComplete = true;
                  }
                }
              } else {
                if (parseInt(globalData[itemType]["foodrestore"]) > 0) {
                  if (
                    parseInt(userVars["DFSTATS_df_hpcurrent"]) <
                    parseInt(userVars["DFSTATS_df_hpmax"]) * 0.25
                  ) {
                    displayPlacementMessage(
                      "You are too badly wounded to do that",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                    displayText = true;
                  } else if (
                    parseInt(userVars["DFSTATS_df_hungerhp"]) <
                    Math.floor(
                      (50 + Math.floor(userVars["DFSTATS_df_survival"] * 1.5)) *
                        0.25
                    )
                  ) {
                    displayPlacementMessage(
                      "You are too hungry to do that",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                    displayText = true;
                  } else {
                    if (
                      globalData[itemType]["needcook"] === false ||
                      currentItem.dataset.type.indexOf("_cooked") >= 0
                    ) {
                      displayPlacementMessage(
                        "This doesn't need to be cooked",
                        mousePos[0] - 40,
                        mousePos[1] + 10,
                        "ERROR"
                      );
                      displayText = true;
                    } else if (
                      parseInt(userVars["DFSTATS_df_level"]) <
                      parseInt(globalData[itemType]["level"]) - 10
                    ) {
                      displayPlacementMessage(
                        "You're too low level to cook that",
                        mousePos[0] - 40,
                        mousePos[1] + 10,
                        "ERROR"
                      );
                      displayText = true;
                    } else {
                      displayPlacementMessage(
                        "Cook this",
                        mousePos[0] - 40,
                        mousePos[1] + 10,
                        "ACTION"
                      );
                      displayText = true;
                      canComplete = true;
                    }
                  }
                } else if (
                  parseInt(globalData[itemType]["healthrestore"]) > 0
                ) {
                  if (
                    parseInt(userVars["DFSTATS_df_hpcurrent"]) <
                    parseInt(userVars["DFSTATS_df_hpmax"]) * 0.25
                  ) {
                    displayPlacementMessage(
                      "You are too badly wounded to do that",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                    displayText = true;
                  } else if (
                    parseInt(userVars["DFSTATS_df_hungerhp"]) <
                    Math.floor(
                      (50 + Math.floor(userVars["DFSTATS_df_survival"] * 1.5)) *
                        0.25
                    )
                  ) {
                    displayPlacementMessage(
                      "You are too hungry to do that",
                      mousePos[0] - 40,
                      mousePos[1] + 10,
                      "ERROR"
                    );
                    displayText = true;
                  } else {
                    if (
                      parseInt(userVars["DFSTATS_df_hpcurrent"]) >=
                      parseInt(userVars["DFSTATS_df_hpmax"])
                    ) {
                      displayPlacementMessage(
                        "You are not wounded",
                        mousePos[0] - 40,
                        mousePos[1] + 10,
                        "ERROR"
                      );
                      displayText = true;
                    } else if (globalData[itemType]["needdoctor"] === false) {
                      displayPlacementMessage(
                        "This doesn't need to be administered",
                        mousePos[0] - 40,
                        mousePos[1] + 10,
                        "ERROR"
                      );
                      displayText = true;
                    } else if (
                      parseInt(userVars["DFSTATS_df_level"]) <
                      parseInt(globalData[itemType]["level"]) - 10
                    ) {
                      displayPlacementMessage(
                        "You're too low level to administer that",
                        mousePos[0] - 40,
                        mousePos[1] + 10,
                        "ERROR"
                      );
                      displayText = true;
                    } else {
                      displayPlacementMessage(
                        "Administer this",
                        mousePos[0] - 40,
                        mousePos[1] + 10,
                        "ACTION"
                      );
                      displayText = true;
                      canComplete = true;
                    }
                  }
                }
              }
              break;
            case "cbadd":
              var itemInCB = false;
              for (var i = 0; i < cbContent["total_items"]; i++) {
                if (cbContent["cb" + i + "_type"] === itemType) {
                  itemInCB = true;
                  break;
                }
              }
              if (itemInCB) {
                displayPlacementMessage(
                  "Item already in book",
                  mousePos[0] - 40,
                  mousePos[1] + 10,
                  "ERROR"
                );
              } else {
                displayPlacementMessage(
                  "Place in book",
                  mousePos[0] - 40,
                  mousePos[1] + 10,
                  "ACTION"
                );
                canComplete = true;
              }
              displayText = true;
              break;
            case "toimpstore":
              if (
                typeof globalData[itemType]["implant"] !== "undefined" &&
                globalData[itemType]["implant"] === true
              ) {
                let totalStoredImplants = 0;
                for (let impType in implantStorage) {
                  totalStoredImplants += parseInt(
                    implantStorage[impType]["quantity"]
                  );
                }
                if (
                  userVars["PLAYERVARS_implant_storage_slots"] >
                  totalStoredImplants
                ) {
                  displayPlacementMessage(
                    "Add to your implant storage",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ACTION"
                  );
                  canComplete = true;
                } else {
                  displayPlacementMessage(
                    "Implant storage is full",
                    mousePos[0] - 40,
                    mousePos[1] + 10,
                    "ERROR"
                  );
                }
                displayText = true;
              }
              break;
          }
          canMove = canComplete;
        }
        fakeGrabbedItem.style.display = "block";
        currentItem.style.visibility = "visible";
      }
      if (!displayText) {
        cleanPlacementMessage();
      }
      if (!hovering && hoverItem) {
        hoverItem.classList.remove("hover");
        hoverItem = null;
      }

      if (!valid) {
        return;
      }

      if (!canMove || hovering) {
        //currentX = mousePos[0] - startX - 20;
        //currentY = mousePos[1] - startY - 20;
        var invHoldOffsets = inventoryHolder.getBoundingClientRect();
        currentX =
          mousePos[0] - invHoldOffsets.left - fakeGrabbedItem.offsetWidth / 2;
        currentY =
          mousePos[1] - invHoldOffsets.top - fakeGrabbedItem.offsetHeight / 2;
        setTranslate(0, 0, currentItem);
        fakeGrabbedItem.style.visibility = "visible";
      }

      setTranslate(currentX, currentY, fakeGrabbedItem);
    }
  }
}

function setTranslate(xPos, yPos, el) {
  el.style.left = xPos + "px";
  el.style.top = yPos + "px";
}

class InventoryItem {
  type;
  colour;
  re;
  stats;
  nt = false;
  cooked = false;
  name;
  ex;
  q;

  /**
   *
   * @param {string} itemStr
   */
  constructor(itemStr) {
    let regMatch = null;
    regMatch = /^([\da-zA-Z \s]+)(?=$|_)/.exec(itemStr);
    this.type = regMatch[0].trim();

    if (
      (regMatch = itemStr.match(/(?<=_name)([a-zA-Z0-9 \-\(\)\.`]+)(?=$|_)/))
    ) {
      this.name = regMatch[0];
    }

    if ((regMatch = itemStr.match(/(?<=_colour)([0-9a-zA-Z\^ ]+)(?=$|_)/))) {
      this.colour = regMatch[0];
    }
    if ((regMatch = itemStr.match(/(?<=_colour)rgb\(([0-9\,]+)\)(?=$|_)/))) {
      let colorsBytes = regMatch[1].split(",");
      this.colour = colorsBytes.join("^");
    }

    if ((regMatch = itemStr.match(/(?<=_stats)([0-9a-z]+)(?=$|_)/))) {
      this.stats = regMatch[0];
    }

    if ((regMatch = itemStr.match(/(?<=_re)(\d)(?=$|_)/))) {
      this.re = regMatch[0];
    }

    if (/(?<=_nt)(?=$|_)/.test(itemStr)) {
      this.nt = true;
    }
    if (/(?<=_cooked)(?=$|_)/.test(itemStr)) {
      this.cooked = true;
    }

    if ((regMatch = itemStr.match(/(?<=_ex)([\d]+)(?=$|_)/))) {
      this.ex = regMatch[0];
    }

    if ((regMatch = itemStr.match(/(?<=_q)(\d)(?=$|_)/))) {
      this.q = regMatch[0];
    }
  }

  toString() {
    let outputString = "";
    for (let index in this) {
      if (index !== "type") {
        if (typeof this[index] !== "boolean") {
          if (typeof this[index] !== "undefined") {
            outputString += "_" + index + this[index];
          }
        } else {
          if (this[index]) {
            outputString += "_" + index;
          }
        }
      } else {
        outputString += this[index];
      }
    }
    return outputString;
  }
}
